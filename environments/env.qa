# =============================================================================
# QA Environment Configuration
# =============================================================================
# 
# Environment variables for QA deployment
# =============================================================================

# =====================================================================
# Environment Settings
# =====================================================================
NODE_ENV=production
BASE_URL=qa.elderwand.com
API_BASE_URL=https://qa-api.elderwand.com

# =====================================================================
# Proxy Settings
# =====================================================================
PROXY_PORT=80
PROXY_SSL_PORT=443

# =====================================================================
# Application Settings
# =====================================================================
# Console
CONSOLE_PORT=8083
CONSOLE_HOST=console

# Experience Studio
EXPERIENCE_PORT=8081
EXPERIENCE_HOST=experience-studio

# Elder Wand (Launchpad)
ELDER_WAND_PORT=8080
ELDER_WAND_HOST=elder-wand

# Product Studio
PRODUCT_PORT=8082
PRODUCT_HOST=product-studio

# =====================================================================
# Logging
# =====================================================================
LOG_LEVEL=info
ENABLE_DEBUG=false

# =====================================================================
# Security
# =====================================================================
ENABLE_HTTPS=true
SSL_CERT_PATH=/etc/nginx/ssl/qa.crt
SSL_KEY_PATH=/etc/nginx/ssl/qa.key 