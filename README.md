# Elder Wand Multi-Project Deployment

This repository contains the Elder Wan<PERSON> application suite with multiple Angular projects deployed using a **pipeline-style Docker approach** that matches Azure CI/CD workflows exactly.

## 🏗️ Architecture

The pipeline-style deployment uses individual Dockerfiles for each service:

| Service | Pipeline Build | Reverse Proxy | Direct Access |
|---------|---------------|---------------|---------------|
| Console | ✅ | http://localhost/console/ | http://localhost:8081 |
| Launchpad (Elder Wand) | ✅ | http://localhost/launchpad/ | http://localhost:8082 |
| Experience Studio | ✅ | http://localhost/experience/ | http://localhost:8083 |
| Product Studio | ✅ | http://localhost/product/ | http://localhost:8084 |

```
┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│     Console     │ │   Elder Wand    │ │   Experience    │ │ Product Studio  │
│   (Port 8081)   │ │   (Port 8082)   │ │     Studio      │ │  (Port 8084)    │
│                 │ │                 │ │   (Port 8083)   │ │                 │
└─────────────────┘ └─────────────────┘ └─────────────────┘ └─────────────────┘
         │                   │                   │                   │
         └───────────────────┘─────────┼-────────┘───────────────────┘
                                       │       
                                       │
                                       │
                              ┌────────┘────────┐
                              │ Nginx Proxy     │
                              │ (Port 80)       │  
                              │                 │
                              │ /console/       │
                              │ /launchpad/     │
                              │ /experience/    │
                              │ /product/       │
                              └─────────────────┘
```

## 🚀 Quick Start (Local Development)

### Prerequisites
- **Docker Desktop** installed and running
- **Git** for cloning the repository
- **Node.js** (optional, for local development builds)

### Step-by-Step Local Setup

#### 1. Clone and Navigate
```bash
# Clone the repository
git clone <repository-url>
cd elderwand
```

#### 2. Start Docker Desktop
Make sure Docker Desktop is running on your machine:
```bash
# On macOS/Linux
open -a Docker

# On Windows
# Start Docker Desktop from the Start menu
```

#### 3. Start All Services
The script will build all Docker images and start the containers:
```bash
# Make the script executable (first time only)
chmod +x scripts/start-docker.sh

# Start all services
./scripts/start-docker.sh start
```

#### 4. Verify Services Are Running
Check the status of all containers:
```bash
./scripts/start-docker.sh status
```

#### 5. Access Your Applications
Once all services are running, you can access them at:

| Application | URL | Description |
|-------------|-----|-------------|
| **Health Check** | http://localhost/health | System health status |
| **Console** | http://localhost/console/ | Admin management interface |
| **Experience Studio** | http://localhost/experience/ | Design and development tools |
| **Launchpad** | http://localhost/launchpad/ | Main application launcher |
| **Product Studio** | http://localhost/product/ | Product management tools |

### Direct Container Access (for debugging)
You can also access individual containers directly:

| Service | Direct URL | Port |
|---------|------------|------|
| Console | http://localhost:8081 | 8081 |
| Elder Wand | http://localhost:8082 | 8082 |
| Experience Studio | http://localhost:8083 | 8083 |
| Product Studio | http://localhost:8084 | 8084 |

### Common Operations

#### Start/Stop Services
```bash
# Start all services
./scripts/start-docker.sh start

# Stop all services
./scripts/start-docker.sh stop

# Restart all services
./scripts/start-docker.sh restart

# Full rebuild (stop, rebuild images, start)
./scripts/start-docker.sh rebuild
```

#### Monitoring and Debugging
```bash
# Check service status and health
./scripts/start-docker.sh status

# View logs for specific service
./scripts/start-docker.sh logs console
./scripts/start-docker.sh logs elder-wand
./scripts/start-docker.sh logs experience-studio
./scripts/start-docker.sh logs product-studio

# View nginx proxy logs
docker logs elder-wand-proxy-direct

# Show help
./scripts/start-docker.sh help
```

#### Troubleshooting Common Issues

**Port Already in Use:**
```bash
# Check what's using the ports
lsof -i :80
lsof -i :8081
lsof -i :8082
lsof -i :8083
lsof -i :8084

# Stop conflicting containers
docker stop $(docker ps -q)
```

**Container Name Conflicts:**
```bash
# Remove conflicting containers
docker stop elder-wand-proxy-direct
docker rm elder-wand-proxy-direct

# Then restart
./scripts/start-docker.sh start
```

**Build Issues:**
```bash
# Clean rebuild everything
./scripts/start-docker.sh rebuild

# Or rebuild specific service manually
docker build -f projects/console/Dockerfile -t elderwand-direct-console:latest .
```

**Network Issues:**
```bash
# Test direct container access
curl http://localhost:8081  # Console
curl http://localhost:8082  # Elder Wand
curl http://localhost:8083  # Experience Studio
curl http://localhost:8084  # Product Studio

# Test nginx proxy
curl http://localhost/health
curl http://localhost/console/
```

### Development Workflow

#### For Frontend Development
```bash
# Build specific project locally (without Docker)
npm run build:console
npm run build:elder-wand
npm run build:experience-studio
npm run build:product-studio

# Build all projects
npm run build:all
```

#### For Docker Development
```bash
# Quick restart (keeps images, just restarts containers)
./scripts/start-docker.sh restart

# Full rebuild (rebuilds all images)
./scripts/start-docker.sh rebuild

# View real-time logs
./scripts/start-docker.sh logs console -f
```

### Access Applications Locally
Once started, access the applications at:
- **Health Check**: http://localhost/health
- **Console**: http://localhost/console
- **Experience Studio**: http://localhost/experience
- **Launchpad**: http://localhost/launchpad  
- **Product Studio**: http://localhost/product

## 🌍 Environment Configurations

### Available Environments
- **`local`** - Local development environment
- **`dev`** - Development environment  
- **`qa`** - Quality Assurance environment
- **`uat`** - User Acceptance Testing environment
- **`prod`** - Production environment

### Environment Files
Each environment has its own configuration file:
- `env.local` - Local development (localhost)
- `env.dev` - Development environment
- `env.qa` - QA environment  
- `env.uat` - UAT environment
- `env.prod` - Production environment

### Deploy to Specific Environment
   ```bash
# Deploy to local environment
./scripts/deploy.sh local

# Deploy to development
./scripts/deploy.sh dev

# Deploy to QA
./scripts/deploy.sh qa

# Deploy to UAT
./scripts/deploy.sh uat

# Deploy to production
./scripts/deploy.sh prod
   ```

## ⚙️ API_BASE_URL Configuration

### How It Works
The `API_BASE_URL` is configured through environment variables and injected into each Angular application via the `env-config.js` file.

### Configuration Files
1. **Environment Files**: Define the API_BASE_URL for each environment
   ```bash
   # environments/env.local
   API_BASE_URL=http://localhost:3000
   
   # environments/env.dev
   API_BASE_URL=https://api-dev.elderwand.com
   ```

2. **Runtime Configuration**: Each container gets `config/env-config.js` with the API base URL
   ```javascript
   window.__env = window.__env || {};
   window.__env.API_BASE_URL = 'http://localhost:3000';
   ```

3. **Angular Integration**: Applications read the configuration at runtime
   ```typescript
   const apiBaseUrl = (window as any).__env?.API_BASE_URL || 'http://localhost:3000';
   ```

### Environment Variable Injection
Environment variables are automatically loaded and injected into containers:
```bash
# From environments/env.local
API_BASE_URL=http://localhost:3000
NODE_ENV=development

# Injected into containers as:
docker run -e API_BASE_URL=http://localhost:3000 -e NODE_ENV=development ...
```

## 🔨 Build Process

Each project follows this pipeline-style build process:

1. **Build Phase** - Uses project-specific Dockerfile:
   ```bash
   docker build -f projects/[PROJECT]/Dockerfile -t elderwand-direct-[PROJECT]:latest .
   ```

2. **Run Phase** - Starts container with environment variables:
   ```bash
   docker run -d --name [PROJECT]-app-direct -p [PORT]:8080 [ENV_VARS] elderwand-direct-[PROJECT]:latest
   ```

3. **Proxy Setup** - Nginx routes traffic to individual containers

## 🔧 Development & Project Organization

### Project Structure
```
elderwand/
├── scripts/              # Executable scripts
│   ├── start-docker.sh   # Main pipeline deployment
│   └── deploy.sh         # Environment deployment
├── environments/         # Environment configuration
│   ├── env.local         # Local development
│   ├── env.dev           # Development environment
│   └── env.prod          # Production environment
├── config/               # Runtime configuration
│   └── env-config.js     # Runtime environment injection
├── projects/             # Angular applications
│   ├── console/          # Admin management interface
│   ├── elder-wand/       # Main application launcher
│   ├── experience-studio/# Design and development tools
│   └── product-studio/   # Product management tools
└── README.md             # This comprehensive guide
```

### Key Benefits
- ✅ **Pipeline Alignment** - Matches Azure CI/CD workflows exactly
- ✅ **Simplified Structure** - All commands in 2 scripts only
- ✅ **Clean Organization** - Logical file grouping
- ✅ **Zero Docker Compose** - Pure pipeline approach

## 🔍 Troubleshooting

### Quick Diagnostics
```bash
# Check all container status
./scripts/start-docker.sh status

# View container logs
./scripts/start-docker.sh logs [service-name]

# Check individual container health
docker ps --filter "name=elder-wand-app-direct"
```

### Common Error Solutions

**Docker Daemon Not Running:**
```bash
# Start Docker Desktop
open -a Docker  # macOS
# Or start from Start menu on Windows
```

**Port Conflicts:**
```bash
# Check what's using port 80
sudo lsof -i :80

# Stop all containers and restart
docker stop $(docker ps -q)
./scripts/start-docker.sh start
```

**Container Build Failures:**
```bash
# Clean rebuild everything
./scripts/start-docker.sh rebuild

# Check Docker disk space
docker system df
docker system prune -a
```

**API Configuration Issues:**
```bash
# Check current API_BASE_URL configuration
curl http://localhost:8082/env-config.js

# Verify environment file
cat environments/env.local | grep API_BASE_URL
```

## 📚 Script Reference

### Available Commands
- `start` - Build and start all services
- `stop` - Stop all services and cleanup
- `restart` - Stop then start
- `rebuild` - Clean rebuild everything
- `status` - Show service status and health
- `logs [service]` - View service logs
- `help` - Show help information

### Key Files
- `scripts/start-docker.sh` - Main pipeline-style Docker script
- `scripts/deploy.sh` - Environment-specific deployment
- `environments/env.*` - Environment variables
- `projects/*/Dockerfile` - Individual service Dockerfiles
- `config/env-config.js` - Runtime configuration template

## 📁 Project Structure

```
elderwand/
├── config/               # Environment configuration files
│   └── env-config.js     # Runtime environment config
├── environments/         # Environment configuration files
│   ├── env.local        # Local development
│   ├── env.dev          # Development environment
│   ├── env.qa           # QA environment
│   ├── env.uat          # UAT environment
│   └── env.prod         # Production environment
├── nginx/               # Nginx reverse proxy configuration
│   └── nginx.conf       # Main nginx configuration
├── projects/            # Angular applications
│   ├── console/         # Admin management interface
│   ├── elder-wand/      # Main application launcher
│   ├── experience-studio/  # Design and development tools
│   ├── product-studio/  # Product management tools
│   └── shared/          # Shared components and services
├── scripts/             # Deployment and utility scripts
│   └── start-docker.sh  # Main Docker management script
├── angular.json         # Angular workspace configuration
├── package.json         # NPM dependencies and scripts
└── README.md            # This comprehensive guide
```

## 🎯 Summary

This Elder Wand multi-project deployment uses a **pipeline-style Docker approach** that:

✅ **Matches Azure CI/CD workflows** exactly  
✅ **Uses individual Dockerfiles** for each service  
✅ **Provides simple local development** with one command  
✅ **Includes comprehensive troubleshooting** guides  
✅ **Supports multiple environments** (local, dev, qa, uat, prod)  

### Quick Reference

| Command | Description |
|---------|-------------|
| `./scripts/start-docker.sh start` | Start all services |
| `./scripts/start-docker.sh stop` | Stop all services |
| `./scripts/start-docker.sh status` | Check service status |
| `./scripts/start-docker.sh rebuild` | Full rebuild |
| `./scripts/start-docker.sh logs [service]` | View service logs |

### Application URLs

| Application | URL |
|-------------|-----|
| Health Check | http://localhost/health |
| Console | http://localhost/console/ |
| Experience Studio | http://localhost/experience/ |
| Launchpad | http://localhost/launchpad/ |
| Product Studio | http://localhost/product/ |

---

**🚀 Elder Wand is ready for local development and production deployment!**


