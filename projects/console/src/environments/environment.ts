// Environment configuration for Console Application
// This file contains development environment settings
export const environment = {
  production: false,
  
  // Application URLs
  elderWandUrl: 'http://localhost:4200',
  
  // API Configuration
  // API_BASE_URL will be injected from Docker environment, fallback to default
  apiBaseUrl: (window as any).__env?.API_BASE_URL || 'https://aava-dev.avateam.io',
  
  // Legacy API URLs (consider consolidating these)
  apiUrl: "https://ava-plus-experience-studio-api-dev.azurewebsites.net",
  consoleApiV2: "https://aava-dev.avateam.io/v2/api/admin",
  consoleApi: 'https://aava-dev.avateam.io/v1/api/admin',
  consoleInstructionApi: 'https://aava-dev.avateam.io/v1/api/instructions',
  consoleEmbeddingApi: 'https://aava-dev.avateam.io/v1/api/embedding',
  consoleApiAuthUrl: 'https://aava-dev.avateam.io/api/auth',
  consolePipelineApi: 'https://aava-dev.avateam.io/force/platform/pipeline/api/v1',
  
  // Redirect and WebSocket URLs
  consoleRedirectUrl: 'http://localhost:4203/console/',
  logStreamingApiUrl: 'wss://aava-dev.avateam.io/ws-pipeline-log-stream',
  enableLogStreaming: 'all',
  
  // Helper function to get API endpoint with base URL
  getApiUrl: (endpoint: string) => {
    const baseUrl = (window as any).__env?.API_BASE_URL || 'https://aava-dev.avateam.io';
    return `${baseUrl}${endpoint}`;
  }
};
