<div
  id="app-container"
  [class.login-active]="true"
>
  <div class="panel">
    <div #dialogHost></div>
  </div>
  <div class="glass-effect"></div>

  <!-- Retractable Header - Now part of main flow -->
  <app-nav-header
    *ngIf="showHeaderAndNav"
    class="retractable-header"
  >
  </app-nav-header>

  <div class="content-wrapper" [class.post-login]="showHeaderAndNav">
    <app-loader></app-loader>
    <router-outlet></router-outlet>
  </div>
</div>
<footer>© 2025 Ascendion. All Rights Reserved</footer>
