import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { AgentServiceService } from './agent-service.service';
import { environment } from '../../../../environments/environment';

describe('AgentServiceService', () => {
  let service: AgentServiceService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [AgentServiceService]
    });
    service = TestBed.inject(AgentServiceService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should get collaborative agents with pagination', () => {
    const mockResponse = [
      {
        id: 8,
        name: 'Python_Data_Analyst_V9301000',
        agentDetails: 'Python_Data_Analyst_V301',
        role: 'Python Developer',
        goal: 'Analyze data and provide insights using Python',
        backstory: 'You are an experienced data analyst with strong Python skills.',
        description: '%1$s',
        expectedOutput: 'Output is :',
        createdBy: '<EMAIL>',
        createdAt: '2025-07-01T23:53:24.469252',
        modifiedBy: '<EMAIL>',
        modifiedAt: '2025-07-01T23:53:24.469251',
        isDeleted: false
      }
    ];

    service.getCollaborativeAgentsPaginated(1, 10).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${environment.consoleApiV2}/ava/force/da/agent?page=1&records=10&isDeleted=false`);
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);
  });

  it('should get collaborative agent details by ID', () => {
    const mockResponse = {
      agentDetail: {
        id: 10,
        name: 'Python_Data_Analyst_V93010002',
        agentDetails: 'Python_Data_Analyst_V302',
        role: 'Python Developer',
        goal: 'Analyze data and provide insights using Python',
        backstory: 'You are an experienced data analyst with strong Python skills.',
        description: '%1$s',
        expectedOutput: 'Output is :',
        createdBy: '<EMAIL>',
        createdAt: '2025-07-02T08:56:23.161753',
        modifiedBy: '<EMAIL>',
        modifiedAt: '2025-07-02T08:56:23.161752',
        agentConfigs: {
          temperature: 0.3,
          modelRef: [
            {
              modelId: 40,
              modelDeploymentName: 'llama4-scout-17b-instruct-v1',
              model: 'llama4-scout-17b-instruct-v1',
              modelType: 'Generative',
              aiEngine: 'AmazonBedrock'
            }
          ],
          knowledgeBaseRef: [
            {
              knowledgeBaseId: 1470,
              indexCollectionName: 'testing',
              modelId: 2,
              modelDeploymentName: 'text-embedding-ada-002',
              model: 'text-embedding-ada-002',
              modelType: 'Embedding',
              aiEngine: 'AzureOpenAI'
            }
          ],
          toolRef: [
            {
              toolId: 3,
              toolName: 'NL2SQLTool',
              parameters: [
                {
                  parameterId: 1,
                  parameterName: 'db_uri',
                  parameterType: 'string'
                }
              ]
            }
          ],
          userToolRef: [
            {
              toolId: 2,
              toolName: 'Get Details for Servicenow Ticket',
              toolClassName: 'GetServiceNowTicketDetails'
            }
          ]
        },
        isDeleted: false
      }
    };

    service.getCollaborativeAgentDetailsById('10').subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${environment.consoleApiV2}/ava/force/da/agent?agentId=10`);
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);
  });

  it('should create collaborative agent with v2 API', () => {
    const mockPayload = {
      agentDetails: 'Python_Data_Analyst_V301',
      name: 'Python_Data_Analyst_V9301',
      role: 'Python Developer',
      goal: 'Analyze data and provide insights using Python',
      backstory: 'You are an experienced data analyst with strong Python skills.',
      description: '%1$s',
      expectedOutput: 'Output is :',
      agentConfigs: {
        temperature: 0.3,
        topP: 0.95,
        maxToken: '4000',
        modelRef: [84, 40],
        knowledgeBaseRef: [1480, 1470],
        levelId: 4,
        maxIter: null,
        maxRpm: 5,
        maxExecutionTime: 5,
        allowDelegation: true,
        allowCodeExecution: true,
        isSafeCodeExecution: true,
        toolRef: [3, 5],
        userToolRef: [2, 5]
      },
      createdBy: '<EMAIL>',
      modifiedBy: '<EMAIL>'
    };

    const mockResponse = {
      message: 'Agent created successfully',
      agentId: 32,
      reviewId: 41
    };

    service.createCollaborativeAgentV2(mockPayload).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${environment.consoleApiV2}/ava/force/da/agent`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(mockPayload);
    req.flush(mockResponse);
  });

  it('should update collaborative agent with v2 API', () => {
    const mockPayload = {
      id: 3,
      agentDetail: 'Python_Data_Analyst_V2',
      name: 'Python_Data_Analyst_V2_new',
      role: 'Software Engineer',
      goal: 'Analyze data and provide insights using Python',
      backstory: 'You are an experienced data analyst with strong Python skills.',
      description: '%1$s',
      expectedOutput: 'Output is :',
      agentConfigs: {
        temperature: 0.3,
        topP: 0.95,
        maxToken: '4000',
        modelRef: [84, 40],
        knowledgeBaseRef: [1480, 1470],
        levelId: 4,
        maxIter: null,
        maxRpm: null,
        maxExecutionTime: null,
        allowDelegation: true,
        allowCodeExecution: true,
        isSafeCodeExecution: true,
        toolRef: [2, 5],
        userToolRef: [1, 4]
      },
      createdBy: '<EMAIL>',
      modifiedBy: '<EMAIL>'
    };

    const mockResponse = {
      message: 'Agent updated Successfully',
      agentId: 3
    };

    service.updateCollaborativeAgentV2(mockPayload).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${environment.consoleApiV2}/ava/force/da/agent`);
    expect(req.request.method).toBe('PUT');
    expect(req.request.body).toEqual(mockPayload);
    req.flush(mockResponse);
  });

  it('should submit collaborative agent change request', () => {
    const mockPayload = {
      id: 47,
      agentDetails: 'Python_Data_Analyst_V201',
      name: 'Python_Data_Analyst_V9201',
      role: 'Python Developer',
      goal: 'Analyze data and provide insights using Python',
      backstory: 'You are an experienced data analyst with strong Python skills.',
      description: '%1$s',
      expectedOutput: 'Output is :',
      agentConfigs: {
        temperature: 0.4,
        maxToken_llm: '5000',
        modelRef: [84, 40],
        knowledgeBaseRef: [1480, 1470],
        levelId: 4,
        maxIter: null,
        maxRpm: 5,
        maxExecutionTime: 5,
        allowDelegation: true,
        allowCodeExecution: true,
        isSafeCodeExecution: true,
        toolRef: [3, 5],
        userToolRef: [2, 5]
      },
      modifiedBy: '<EMAIL>',
      isDeleted: false
    };

    const mockResponse = {
      message: 'Agent Change Requested Successfully',
      agentId: 7,
      reviewId: 42
    };

    service.submitCollaborativeAgentChangeRequest(mockPayload).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${environment.consoleApiV2}/ava/force/da/agent/change_request`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(mockPayload);
    req.flush(mockResponse);
  });
});
