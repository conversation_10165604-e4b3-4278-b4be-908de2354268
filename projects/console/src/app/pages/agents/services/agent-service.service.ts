import { HttpHeaders, HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../../environments/environment';
import { map, Observable } from 'rxjs';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';

@Injectable({
  providedIn: 'root'
})
export class AgentServiceService {
  private baseUrl = environment.consoleApi;
  private v2BaseUrl = environment.consoleApiV2;
  private headers = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    }),
  }

  constructor(
    private http: HttpClient,
    private tokenStorage: TokenStorageService
  ) { }

  /**
   * Get user signature from token storage
   * @returns User signature or default email
   */
  private getUserSignature(): string {
    return this.tokenStorage.getDaUsername() || '<EMAIL>';
  }

  public getAllAgentList() {
    const url = `${this.baseUrl}/ava/force/agents`;
    return this.http.get(url, this.headers).pipe(
      map((response: any) => {
          return response;
      })
    );
  }

  /**
   * Get all individual agents
   * @returns Observable with individual agents list
   */
  public getAllIndividualAgents() {
    const url = `${this.baseUrl}/ava/force/individualAgents`;
    return this.http.get(url, this.headers).pipe(
      map((response: any) => {
        return response;
      })
    );
  }



  /**
   * Get collaborative agents with pagination
   * @param page - Page number (1-based)
   * @param records - Number of records per page
   * @returns Observable with paginated collaborative agents list
   */
  public getCollaborativeAgentsPaginated(page: number = 1, records: number = 10) {
    const url = `${this.v2BaseUrl}/ava/force/da/agent`;
    const params = {
      page: page.toString(),
      records: records.toString(),
      isDeleted: 'false'
    };

    return this.http.get(url, { ...this.headers, params }).pipe(
      map((response: any) => {
        return response;
      })
    );
  }

  /**
   * Get collaborative agent details by ID
   * @param agentId - The ID of the collaborative agent to fetch
   * @returns Observable with the agent details
   */
  public getCollaborativeAgentDetailsById(agentId: string): Observable<any> {
    const url = `${this.v2BaseUrl}/ava/force/da/agent`;
    const params = {
      agentId: agentId
    };

    return this.http.get(url, { ...this.headers, params }).pipe(
      map((response: any) => {
        return response;
      })
    );
  }

  public getLabels() {
    const url = `${this.baseUrl}/ava/force/label`;
    return this.http.get(url, this.headers).pipe(
      map((response: any) => {
          return response;
      })
    );
  }

  /**
   * Parse label values from string format to array of options
   * @param labelValues - String in format "id=name;id=name;"
   * @returns Array of {value, name} objects
   */
  public parseLabelValues(labelValues: string): {value: string, name: string}[] {
    if (!labelValues) return [];

    const labelPairs = labelValues.split(";");
    return labelPairs
      .filter((pair: string) => pair.trim())
      .map((pair: string) => {
        const [value, name] = pair.split("=");
        return { value: value?.trim() || '', name: name?.trim() || '' };
      });
  }

  /**
   * Get models from labels API
   * @returns Observable with parsed model options
   */
  public getModelsFromLabels(): Observable<any[]> {
    return this.getLabels().pipe(
      map((response: any) => {
        const categoryLabels = response.categoryLabels || [];
        const modelCategory = categoryLabels.find((category: any) => category.categoryId === 1);

        if (modelCategory) {
          const modelLabel = modelCategory.labels.find((label: any) => label.labelCode === 'MODEL');
          if (modelLabel && modelLabel.labelValues) {
            return this.parseLabelValues(modelLabel.labelValues).map(option => ({
              id: option.value,
              name: option.name,
              type: 'model',
              icon: 'assets/images/model.png',
              description: `Model: ${option.name}`
            }));
          }
        }
        return [];
      })
    );
  }

  /**
   * Get knowledge bases from labels API
   * @returns Observable with parsed knowledge base options
   */
  public getKnowledgeBasesFromLabels(): Observable<any[]> {
    return this.getLabels().pipe(
      map((response: any) => {
        const categoryLabels = response.categoryLabels || [];
        const iclCategory = categoryLabels.find((category: any) => category.categoryId === 2);

        if (iclCategory) {
          const knowledgeLabel = iclCategory.labels.find((label: any) => label.labelCode === 'RAG_KNOWLEDGEBASE_NAME');
          if (knowledgeLabel && knowledgeLabel.labelValues) {
            return this.parseLabelValues(knowledgeLabel.labelValues).map(option => ({
              id: option.value,
              name: option.name,
              type: 'knowledge',
              icon: 'assets/images/knowledge.png',
              description: `Knowledge Base: ${option.name}`
            }));
          }
        }
        return [];
      })
    );
  }

  /**
   * Get guardrails from labels API
   * @returns Observable with parsed guardrail options
   */
  public getGuardrailsFromLabels(): Observable<any[]> {
    return this.getLabels().pipe(
      map((response: any) => {
        const categoryLabels = response.categoryLabels || [];
        const otherCategory = categoryLabels.find((category: any) => category.categoryId === 3);

        if (otherCategory) {
          // Include ALL labels from Other category, including "Enable Guardrails"
          return otherCategory.labels
            .filter((label: any) => label.labelType === 'Toggle') // Include all toggle types
            .map((label: any) => ({
              id: label.labelId.toString(),
              name: label.labelName,
              code: label.labelCode,
              type: 'guardrail',
              icon: 'assets/images/guardrail.png',
              description: label.labelInfo || `Guardrail: ${label.labelName}`
            }));
        }
        return [];
      })
    );
  }

  /**
   * Get agent details by ID
   * @param agentId - The ID of the agent to fetch
   * @returns Observable with the agent details
   */
  public getAgentById(agentId: string): Observable<any> {
    const url = `${this.baseUrl}/ava/force/individualAgent?individualAgentId=${agentId}`;
    return this.http.get(url, this.headers).pipe(
      map((response: any) => {
        return response;
      })
    );
  }

  /**
   * Save individual agent with the provided payload
   * @param payload - The individual agent data to save
   * @returns Observable with the API response
   */
  public individualAgentSave(payload: any): Observable<any> {
    const url = `${this.baseUrl}/ava/force/individualAgent`;
    return this.http.post(url, payload, this.headers).pipe(
      map((response: any) => {
        return response;
      })
    );
  }



  /**
   * Create new collaborative agent with v2 API
   * @param payload - The collaborative agent data to create
   * @returns Observable with the API response
   */
  public createCollaborativeAgentV2(payload: any): Observable<any> {
    const url = `${this.v2BaseUrl}/ava/force/da/agent`;
    return this.http.post(url, payload, this.headers).pipe(
      map((response: any) => {
        return response;
      })
    );
  }

  /**
   * Edit individual agent data
   * @param payload - The individual agent data to update
   * @returns Observable with the API response
   */
  public individualAgentEdit(payload: any): Observable<any> {
    const url = `${this.baseUrl}/ava/force/individualAgent`;
    return this.http.put(url, payload, this.headers).pipe(
      map((response: any) => {
        return response;
      })
    );
  }



  /**
   * Update collaborative agent with v2 API
   * @param payload - The collaborative agent data to update
   * @returns Observable with the API response
   */
  public updateCollaborativeAgentV2(payload: any): Observable<any> {
    const url = `${this.v2BaseUrl}/ava/force/da/agent`;
    return this.http.put(url, payload, this.headers).pipe(
      map((response: any) => {
        return response;
      })
    );
  }

  /**
   * Submit collaborative agent change request
   * @param payload - The collaborative agent change request data
   * @returns Observable with the API response
   */
  public submitCollaborativeAgentChangeRequest(payload: any): Observable<any> {
    const url = `${this.v2BaseUrl}/ava/force/da/agent/change_request`;
    return this.http.put(url, payload, this.headers).pipe(
      map((response: any) => {
        return response;
      })
    );
  }

  /**
   * Get models for collaborative agents using the direct API
   * @returns Observable with the models array
   */
  public getCollaborativeModels(): Observable<any> {
    const url = `${this.baseUrl}/ava/force/model?modelType=Generative`;
    return this.http.get(url, this.headers).pipe(
      map((response: any) => {
        return response;
      })
    );
  }


  /**
   * Delete individual agent by ID (Legacy - kept for backward compatibility)
   * @param agentId - The ID of the agent to delete
   * @returns Observable with the API response
   */
  public deleteAgent(agentId: string): Observable<any> {
    const url = `${this.baseUrl}/ava/force/individualAgent?individualAgentId=${agentId}`;
    return this.http.delete(url, this.headers).pipe(
      map((response: any) => {
        return response;
      })
    );
  }

  /**
   * Delete collaborative agent by ID using v2 API
   * @param agentId - The ID of the collaborative agent to delete
   * @returns Observable with the API response
   */
  public deleteCollaborativeAgent(agentId: string): Observable<any> {
    const userSignature = this.getUserSignature();
    const url = `${this.v2BaseUrl}/ava/force/da/agent/change_request?agentId=${agentId}&modifiedBy=${userSignature}`;
    return this.http.delete(url, this.headers).pipe(
      map((response: any) => {
        return response;
      })
    );
  }

  /**
   * Get individual agent minimal details for playground dropdown
   * @returns Observable with individual agents minimal details
   */
  public getIndividualAgentMinDetails(): Observable<any> {
    const url = `${this.baseUrl}/ava/force/individualAgentMinDetails`;
    return this.http.get(url, this.headers).pipe(
      map((response: any) => {
        return response;
      })
    );
  }
}
