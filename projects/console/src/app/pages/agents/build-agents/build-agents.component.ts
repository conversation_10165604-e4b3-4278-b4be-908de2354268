import {
  Component,
  <PERSON><PERSON>nit,
  <PERSON><PERSON><PERSON><PERSON>,
  AfterViewInit,
  ChangeDetectorRef,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormsModule,
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import {
  ButtonComponent,
  DropdownOption,
  PopupComponent,
  AvaTextboxComponent,
  IconComponent,
} from '@ava/play-comp-library';
import {
  CanvasBoardComponent,
  CanvasNode,
  CanvasEdge,
} from '../../../shared/components/canvas-board/canvas-board.component';
import {
  BuildAgentNodeComponent,
  BuildAgentNodeData,
  ExecuteNodeData,
} from './components/build-agent-node';

import { ChatMessage } from '../../../shared/components/chat-window';
import { ToolExecutionService } from '../../../shared/services/tool-execution/tool-execution.service';
import {
  Subscription,
  Subject,
  takeUntil,
  switchMap,
  finalize,
  catchError,
  of,
} from 'rxjs';
import { PromptsService } from '../../../shared/services/prompts.service';
import { ToolsService } from '../../../shared/services/tools.service';
import { AgentServiceService } from '../services/agent-service.service';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { AgentPlaygroundService } from './services/agent-playground.service';
import { KnowledgeBaseService } from '../../../shared/services/knowledge-base.service';

import { AvaTab } from '../../workflows/workflow-execution/workflow-execution.component';
import {
  CustomTab,
  CustomTabsComponent,
} from '../../../shared/components/custom-tabs/custom-tabs.component';
import { PlaygroundComponent } from '../../../shared/components/playground/playground.component';

interface ToolItem {
  id: string;
  name: string;
  description: string;
  icon: string;
  type: 'tool' | 'model' | 'knowledge' | 'prompt' | 'guardrail';
  [key: string]: any;
}

@Component({
  selector: 'app-build-agents',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonComponent,
    CanvasBoardComponent,
    BuildAgentNodeComponent,
    PlaygroundComponent,
    AvaTextboxComponent,
    IconComponent,
    PopupComponent,
    CustomTabsComponent,
  ],
  templateUrl: './build-agents.component.html',
  styleUrls: ['./build-agents.component.scss'],
})
export class BuildAgentsComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild(CanvasBoardComponent) canvasBoardComponent!: CanvasBoardComponent;

  private destroy$ = new Subject<void>();
  activeTab: string = 'prompts';
  searchQuery: string = '';
  searchForm!: FormGroup;

  isSidebarCollapsed = false;
  showPreview = false;
  selectedItem: any = null;
  previewData: any = null;
  isLoadingPreview = false;
  canvasNodes: CanvasNode[] = [];
  canvasEdges: CanvasEdge[] = [];
  buildAgentNodes: BuildAgentNodeData[] = [];
  executeNodes: ExecuteNodeData[] = [];
  selectedNodeId: string | null = null;
  currentAgentType = 'individual';
  currentMode: 'create' | 'view' | 'edit' | 'duplicate' = 'create';
  currentAgentId: string | null = null;
  isFieldsDisabled = false;
  isDuplicateMode = false;
  handleEditModeConnections = false;

  showSuccessPopup = false;
  showErrorPopup = false;
  showWarningPopup = false;
  isApprovalSuccess = false;
  popupMessage = '';
  popupTitle = '';

  get showApprovalButton(): boolean {
    return false;
  }
  currentAgentDetails: any = null;
  private readonly nodeLimits = {
    individual: { prompt: 1, model: 1, knowledge: -1, guardrail: -1 },
    collaborative: { prompt: 1, model: 1, knowledge: -1, tool: -1 },
  };
  tabs: AvaTab[] = [];
  private _customTabs: CustomTab[] = [];
  get customTabs(): CustomTab[] {
    return this._customTabs;
  }

  allToolItems: { [key: string]: ToolItem[] } = {
    prompts: [],
    models: [],
    knowledge: [],
    tools: [],
    guardrails: [],
  };

  navigationHints = [
    'Alt + Drag to pan canvas',
    'Mouse wheel to zoom',
    'Space to reset view',
  ];
  isExecuteMode = false;
  showChatInterface = false;
  chatMessages: ChatMessage[] = [];
  isProcessingChat = false;
  promptOptions: DropdownOption[] = [];
  private executionSubscription?: Subscription;

  isEditMode = false;
  isViewMode = false;
  isNewAgent = true;
  agentConfigIds = new Map<string, number>();

  // Get dynamic button text based on current mode
  get primaryButtonText(): string {
    if (this.isExecuteMode) {
      return 'Run'; // When in execute mode, show "Run"
    }

    if (this.isViewMode) {
      return 'Execute'; // When viewing existing agent, show "Execute"
    }

    if (this.isEditMode) {
      // For collaborative agents in edit mode, use "Update and Send Approval"
      if (this.currentAgentType === 'collaborative') {
        return 'Update and Send Approval';
      }
      return 'Update'; // When editing existing individual agent, show "Update"
    }

    if (this.isDuplicateMode) {
      return 'Save'; // When duplicating agent, show "Save"
    }

    // For collaborative agents in create mode, use "Save and Send Approval"
    if (this.currentAgentType === 'collaborative') {
      return 'Save and Send Approval';
    }

    return 'Save'; // When creating new individual agent, show "Save"
  }

  // Agent data properties for saving
  agentName: string = '';
  agentDetail: string = '';
  agentCode: string = ''; // Store the agent code (e.g., BUGBUSTERCHATBOT) for API calls
  agentMetadata: {
    org: string;
    domain: string;
    project: string;
    team: string;
  } = { org: '', domain: '', project: '', team: '' };

  private buildOrganizationPath(): string {
    const { org, domain, project, team } = this.agentMetadata;
    return org && domain && project && team
      ? `@${org}@${domain}@${project}@${team}`
      : '@ASCENDION@PLATFORM_ENGINEERING@AVA@DIGITAL_ASCENDER';
  }

  // Cache for labels API response to avoid multiple calls
  private labelsCache: any = null;

  // Agent Playground Properties
  agentPlaygroundForm!: FormGroup;
  selectedPrompt = 'default';
  selectedAgentMode = '';
  selectedUseCaseIdentifier = '';
  agentChatPayload: any[] = [];
  agentFilesUploadedData: any[] = [];
  agentAttachment: string[] = [];
  isAgentPlaygroundLoading = false;
  agentPlaygroundDestroy = new Subject<boolean>();

  responseModalConfig = {
    id: 'responseModal',
    type: 'dialog',
    title: 'Agent Response',
    closeButton: false,
    primaryButtonConfig: {
      text: 'OK',
      buttonType: 'primary',
      buttonSize: 'medium',
      imageUrl: '',
      linkURL: '',
    },
  };
  isResponseModalOpen = false;
  modalMessage = '';
  isModalError = false;
  autoSelectedAgentFromCard: any = null;
  testSpaceInput = '';

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private toolExecutionService: ToolExecutionService,
    private cdr: ChangeDetectorRef,
    private toolsService: ToolsService,
    private agentService: AgentServiceService,
    private promptsService: PromptsService,
    private tokenStorage: TokenStorageService,
    private agentPlaygroundService: AgentPlaygroundService,
    private formBuilder: FormBuilder,
    private knowledgeBaseService: KnowledgeBaseService,
  ) {
    this.agentPlaygroundForm = this.formBuilder.group({
      isConversational: [true],
      isUseTemplate: [false],
    });
    this.searchForm = this.formBuilder.group({ search: [''] });
  }

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.currentAgentType =
        params['type'] === 'individual' || params['type'] === 'collaborative'
          ? params['type']
          : 'individual';
      this.currentAgentDetails = null;
      this.configureTabsForAgentType();
      this.loadDataForAgentType();
    });
    this.route.queryParams.subscribe((params) => {
      if (params['id']) {
        this.currentAgentId = params['id'];
        const mode = params['mode'];
        this.isViewMode =
          this.isEditMode =
          this.isExecuteMode =
          this.isDuplicateMode =
          this.isNewAgent =
            false;
        this.isFieldsDisabled = false;

        if (mode === 'view') {
          this.isViewMode = true;
          this.isFieldsDisabled = true;
        } else if (mode === 'edit') {
          this.isEditMode = true;
          this.handleEditModeConnections = true;
        } else if (mode === 'execute') {
          this.isExecuteMode = true;
          this.isFieldsDisabled = true;
          this.showChatInterface = true;
        } else if (mode === 'duplicate') {
          this.isDuplicateMode = true;
          this.agentConfigIds.clear();
        } else {
          this.isNewAgent = true;
        }
        this.loadAgentData(params['id']);
      } else {
        this.isNewAgent = true;
        this.isEditMode = this.isViewMode = false;
        this.isFieldsDisabled = false;
        this.currentAgentId = null;
      }
    });

    this.activeTab = 'prompts';
    this.searchForm
      .get('search')
      ?.valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe((value) => {
        this.searchQuery = value || '';
        this.updateFilteredTools();
      });
    this.updateFilteredTools();
  }

  ngAfterViewInit(): void {
    this.cdr.detectChanges();
    setTimeout(() => {
      if (this.canvasBoardComponent && this.buildAgentNodes.length > 0) {
        this.canvasBoardComponent.updateNodeConnectionPoints();
        if (this.canvasEdges.length === 0 && this.buildAgentNodes.length > 1) {
          this.createAgentFlowConnections(this.buildAgentNodes);
        }
      }
    }, 300);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    if (this.executionSubscription) {
      this.executionSubscription.unsubscribe();
    }
    this.agentPlaygroundDestroy.next(true);
    this.agentPlaygroundDestroy.complete();
  }

  private generateMockItems(
    type: string,
    name: string,
    icon: string,
    count: number,
  ): ToolItem[] {
    return Array.from({ length: count }, (_, i) => ({
      id: `${type}${i + 1}`,
      name,
      icon,
      type: type as any,
      description:
        'AI agents are software programs that use artificial intelligence to perform tasks and achieve goals.',
    }));
  }

  private loadPrompts(): void {
    this.promptsService.fetchAllPrompts().subscribe({
      next: (prompts: any[]) => {
        const filteredPrompts = this.filterPromptsByAgentType(prompts);
        this.allToolItems['prompts'] = filteredPrompts.map((prompt: any) => ({
          id: prompt.id?.toString() || Math.random().toString(),
          name: prompt.name || prompt.title || 'Unknown Prompt',
          description:
            prompt.description ||
            prompt.descriptionConsolidated ||
            'AI prompt for agent tasks',
          icon: 'assets/images/prompt.png',
          type: 'prompt',
          promptType: prompt.type,
          role: prompt.role,
          goal: prompt.goal,
          backstory: prompt.backstory,
          expectedOutput: prompt.expectedOutput,
          descriptionConsolidated: prompt.descriptionConsolidated,
          expectedOutputConsolidated: prompt.expectedOutputConsolidated,
          originalPromptData: prompt,
        }));
        this.updateFilteredTools();
      },
      error: (error) => {
        console.error('Error loading prompts:', error);
      },
    });
  }

  private filterPromptsByAgentType(prompts: any[]): any[] {
    if (!Array.isArray(prompts) || prompts.length === 0) return [];

    if (this.currentAgentType === 'collaborative') {
      return prompts.filter((prompt) => prompt.type?.trim() === 'zero shot');
    } else {
      return prompts.filter((prompt) => {
        const promptType = prompt.type?.trim();
        return promptType === 'zero shot' || promptType === 'free form';
      });
    }
  }

  private loadModels(): void {
    if (this.currentAgentType === 'collaborative') {
      this.agentService.getCollaborativeModels().subscribe({
        next: (response: any) => {
          if (response && Array.isArray(response)) {
            this.allToolItems['models'] = response.map((model: any) => ({
              id: model.id,
              name: `${model.modelDeploymentName} (${model.model})`,
              description:
                model.modelDescription ||
                `${model.modelType} model via ${model.aiEngine}`,
              icon: 'assets/images/Boundingbox.png',
              type: 'model' as const,
              model: model.model,
              aiEngine: model.aiEngine,
              modelType: model.modelType,
            }));
          } else if (response && response.models) {
            this.allToolItems['models'] = response.models.map((model: any) => ({
              id: model.id,
              name: model.model,
              description: model.modelDescription,
              icon: 'assets/images/Boundingbox.png',
              type: 'model' as const,
            }));
          }
          this.updateFilteredTools();
        },
        error: (error: any) => {
          console.error('Error loading models from direct API:', error);
          this.loadModelsFromLabels();
        },
      });
    } else {
      this.loadModelsFromLabels();
    }
  }

  private loadModelsFromLabels(): void {
    this.agentService.getModelsFromLabels().subscribe({
      next: (models: any[]) => {
        this.allToolItems['models'] = models.map((model: any) => ({
          id: model.id,
          name: model.name,
          description: model.description,
          icon: model.icon,
          type: 'model' as const,
        }));
        this.updateFilteredTools();
      },
      error: (error: any) => {
        console.error('Error loading models from labels API:', error);
        this.allToolItems['models'] = this.generateMockItems(
          'model',
          'Model Name',
          'assets/images/Boundingbox.png',
          2,
        );
        this.updateFilteredTools();
      },
    });
  }

  private loadModelsFromCache(): void {
    if (!this.labelsCache) return;

    const categoryLabels = this.labelsCache.categoryLabels || [];
    const modelCategory = categoryLabels.find(
      (category: any) => category.categoryId === 1,
    );

    if (modelCategory) {
      const modelLabel = modelCategory.labels.find(
        (label: any) => label.labelCode === 'MODEL',
      );
      if (modelLabel && modelLabel.labelValues) {
        const parsedModels = this.agentService.parseLabelValues(
          modelLabel.labelValues,
        );
        this.allToolItems['models'] = parsedModels.map((option) => ({
          id: option.value,
          name: option.name,
          type: 'model',
          icon: 'assets/images/model.png',
          description: `Model: ${option.name}`,
        }));
        this.updateFilteredTools();
      }
    }
  }

  private loadKnowledgeBase(): void {
    if (this.currentAgentType === 'collaborative') {
      // Use knowledge base service directly for collaborative agents
      this.knowledgeBaseService.fetchAllKnowledge().subscribe({
        next: (response: any[]) => {
          // Transform CardData format to the expected format for collaborative agents
          this.allToolItems['knowledge'] = response.map((kb: any) => ({
            id: kb.id,
            name: kb.title || kb.name || 'Unknown Knowledge Base',
            description:
              kb.description || `Knowledge Base: ${kb.title || kb.name}`,
            icon: 'assets/images/import_contacts.png',
            type: 'knowledge' as const,
            // Add any additional properties if available
            vectorDb: kb.vectorDb,
            modelRef: kb.modelRef,
            splitSize: kb.splitSize,
          }));
          this.updateFilteredTools();
        },
        error: (error) => {
          console.error(
            'Error loading knowledge bases from knowledge base service:',
            error,
          );
          // For collaborative agents, don't fallback to labels API
          // Just use empty array or mock data
          this.allToolItems['knowledge'] = this.generateMockItems(
            'knowledge',
            'Knowledge Base Name',
            'assets/images/import_contacts.png',
            2,
          );
          this.updateFilteredTools();
        },
      });
    } else {
      this.loadKnowledgeBaseFromLabels();
    }
  }

  private loadKnowledgeBaseFromLabels(): void {
    this.agentService.getKnowledgeBasesFromLabels().subscribe({
      next: (knowledgeItems) => {
        this.allToolItems['knowledge'] = knowledgeItems.map((item: any) => ({
          id: item.id,
          name: item.name,
          description: item.description,
          icon: item.icon,
          type: 'knowledge' as const,
        }));
        this.updateFilteredTools();
      },
      error: (error) => {
        console.error('Error loading knowledge bases from labels API:', error);
        // Fallback to mock data if API fails
        this.allToolItems['knowledge'] = this.generateMockItems(
          'knowledge',
          'Knowledge Base Name',
          'assets/images/import_contacts.png',
          2,
        );
        this.updateFilteredTools();
      },
    });
  }

  // Load knowledge bases from cached labels data
  private loadKnowledgeBaseFromCache(): void {
    if (!this.labelsCache) return;

    const categoryLabels = this.labelsCache.categoryLabels || [];
    const iclCategory = categoryLabels.find(
      (category: any) => category.categoryId === 2,
    );

    if (iclCategory) {
      const knowledgeLabel = iclCategory.labels.find(
        (label: any) => label.labelCode === 'RAG_KNOWLEDGEBASE_NAME',
      );
      if (knowledgeLabel && knowledgeLabel.labelValues) {
        const parsedKnowledge = this.agentService.parseLabelValues(
          knowledgeLabel.labelValues,
        );
        this.allToolItems['knowledge'] = parsedKnowledge.map((option) => ({
          id: option.value, // This is the actual knowledge base ID we need for payload
          name: option.name,
          type: 'knowledge',
          icon: 'assets/images/knowledge.png',
          description: `Knowledge Base: ${option.name}`,
        }));
      }
    }
  }

  private loadTools(): void {
    // Load both built-in and user-defined tools
    const builtInTools$ = this.toolsService.getBuiltInToolsList();

    // Load built-in tools first
    builtInTools$.subscribe({
      next: (response: any) => {
        const builtInTools = response.tools || [];
        const builtInToolItems = builtInTools.map((tool: any) => ({
          id: `builtin-${tool.toolId}`,
          name: tool.toolName || 'Unknown Built-in Tool',
          description: 'Built-in tool for AI agent tasks',
          icon: 'assets/images/build.png',
          type: 'tool',
        }));

        // Then load user-defined tools with dynamic records count
        this.loadUserToolsWithPagination(builtInToolItems);
      },
      error: (error: any) => {
        console.error('Error loading built-in tools:', error);
        // Still try to load user tools even if built-in tools fail
        this.loadUserToolsWithPagination([]);
      },
    });
  }

  private loadUserToolsWithPagination(builtInToolItems: any[]): void {
    // First, get the total count by making an initial API call
    this.toolsService.getUserToolsList(1, 11).subscribe({
      next: (userResponse: any) => {
        const totalRecords = userResponse.totalNoOfRecords || 51;

        // Now load all user tools using the total count
        this.toolsService.getUserToolsList(1, totalRecords).subscribe({
          next: (fullUserResponse: any) => {
            const userTools = fullUserResponse.userToolDetails || [];
            const userToolItems = userTools.map((tool: any) => ({
              id: `user-${tool.id}`,
              name: tool.name || 'Unknown User Tool',
              description:
                tool.description || 'User-defined tool for AI agent tasks',
              icon: 'assets/images/build.png',
              type: 'tool',
            }));

            // Combine both built-in and user-defined tools
            this.allToolItems['tools'] = [
              ...builtInToolItems,
              ...userToolItems,
            ];
            this.updateFilteredTools();
          },
          error: (error) => {
            console.error('Error loading full user tools list:', error);
            // Use only built-in tools if user tools fail
            this.allToolItems['tools'] = builtInToolItems;
            this.updateFilteredTools();
          },
        });
      },
      error: (error) => {
        console.error('Error loading user tools:', error);
        // Use only built-in tools if user tools fail
        this.allToolItems['tools'] = builtInToolItems;
        this.updateFilteredTools();
      },
    });
  }

  private loadGuardrails(): void {
    this.agentService.getGuardrailsFromLabels().subscribe({
      next: (guardrails: any[]) => {
        this.allToolItems['guardrails'] = guardrails.map((guardrail: any) => ({
          id: guardrail.id,
          name: guardrail.name,
          description: guardrail.description,
          icon: guardrail.icon,
          type: 'guardrail' as const,
          code: guardrail.code,
        }));
        this.updateFilteredTools();
        this.updateFilteredTools();
      },
      error: (error: any) => {
        console.error('Error loading guardrails from labels API:', error);
        // Fallback to mock data if API fails
        this.allToolItems['guardrails'] = this.generateMockItems(
          'guardrail',
          'Guardrail Name',
          'assets/images/swords.png',
          2,
        );
        this.updateFilteredTools();
      },
    });
  }

  // Load guardrails from cached labels data
  private loadGuardrailsFromCache(): void {
    if (!this.labelsCache) return;

    const categoryLabels = this.labelsCache.categoryLabels || [];
    const otherCategory = categoryLabels.find(
      (category: any) => category.categoryId === 3,
    );

    if (otherCategory) {
      // Include ALL toggle labels from Other category, including "Enable Guardrails"
      this.allToolItems['guardrails'] = otherCategory.labels
        .filter((label: any) => label.labelType === 'Toggle') // Include all toggle types
        .map((label: any) => ({
          id: label.labelId.toString(),
          name: label.labelName,
          code: label.labelCode,
          type: 'guardrail' as const,
          icon: 'assets/images/guardrail.png',
          description: label.labelInfo || `Guardrail: ${label.labelName}`,
        }));
    }
  }

  private _filteredTools: ToolItem[] = [];

  get currentTabTools(): ToolItem[] {
    return this.allToolItems[this.activeTab] || [];
  }
  get filteredTools(): ToolItem[] {
    return this._filteredTools;
  }

  private updateFilteredTools(): void {
    const currentTools = this.currentTabTools;
    const availableTools = currentTools.filter((tool) => {
      const isAlreadyAdded = this.buildAgentNodes.some(
        (node) => node.originalToolData && node.originalToolData.id === tool.id,
      );
      return !isAlreadyAdded;
    });

    if (!this.searchQuery) {
      this._filteredTools = availableTools;
      return;
    }

    const query = this.searchQuery.toLowerCase();
    this._filteredTools = availableTools.filter(
      (tool) =>
        tool.name.toLowerCase().includes(query) ||
        tool.description.toLowerCase().includes(query),
    );
  }

  onTabChange(tabValue: string | number): void {
    this.activeTab = tabValue.toString();
    this.searchQuery = '';
    this.searchForm.get('search')?.setValue('', { emitEvent: false });
    this.updateFilteredTools();
  }

  onSearchChange(query: string): void {
    this.searchQuery = query;
    this.updateFilteredTools();
  }

  onCustomTabChange(tabValue: string | number): void {
    this.activeTab = tabValue.toString();
    this.searchQuery = '';
    this.searchForm.get('search')?.setValue('', { emitEvent: false });
    this.updateFilteredTools();
  }

  toggleSidebar(): void {
    this.isSidebarCollapsed = !this.isSidebarCollapsed;
  }
  isActiveCustomTab(tabValue: string | number): boolean {
    return this.activeTab === tabValue.toString();
  }

  getTabIcon(tab: any): string {
    const iconMap: { [key: string]: string } = {
      prompts: 'FileText',
      models: 'Box',
      knowledge: 'BookOpen',
      guardrails: 'Swords',
      tools: 'Wrench',
    };
    return iconMap[tab.value] || 'Circle';
  }

  private handleAutoTabSwitch(nodeType: string): void {
    const currentLimits =
      this.nodeLimits[this.currentAgentType as keyof typeof this.nodeLimits];
    const currentNodeLimit =
      currentLimits[nodeType as keyof typeof currentLimits];

    if (currentNodeLimit === 1) {
      const nextTab = this.getNextRequiredTab(nodeType);
      if (nextTab) {
        this.activeTab = nextTab;
        this.searchQuery = '';
        this.searchForm.get('search')?.setValue('', { emitEvent: false });
        this.updateFilteredTools();
      }
    }
  }

  private getNextRequiredTab(currentNodeType: string): string | null {
    const currentLimits =
      this.nodeLimits[this.currentAgentType as keyof typeof this.nodeLimits];

    if (currentNodeType === 'prompt') {
      const hasModel = this.buildAgentNodes.some(
        (node) => node.type === 'model',
      );
      if (!hasModel && currentLimits['model'] === 1) {
        return 'models';
      }
    } else if (currentNodeType === 'model') {
      return 'knowledge';
    }

    return null;
  }

  onItemPreview(item: ToolItem): void {
    this.selectedItem = item;
    this.showPreview = true;
    this.loadPreviewData(item);
  }

  closePreview(): void {
    this.showPreview = false;
    this.selectedItem = null;
    this.previewData = null;
  }

  private loadPreviewData(item: ToolItem): void {
    this.isLoadingPreview = true;
    this.previewData = null;

    const itemData =
      this.allToolItems[item.type + 's']?.find((i) => i.id === item.id) ||
      this.allToolItems[
        item.type === 'guardrail' ? 'guardrails' : item.type
      ]?.find((i) => i.id === item.id);

    if (itemData) {
      this.previewData = {
        type: item.type,
        title: itemData.name || `${item.type} Details`,
        data: itemData,
      };
    } else {
      this.previewData = { error: `${item.type} details not found` };
    }
    this.isLoadingPreview = false;
  }

  getIconForType(type: string): string {
    const iconMap: { [key: string]: string } = {
      prompt: 'FileText',
      model: 'assets/images/deployed_code.png',
      knowledge: 'assets/images/import_contacts.png',
      tool: 'assets/images/build.png',
      guardrail: 'assets/images/swords.png',
    };
    return iconMap[type] || 'assets/images/build.png'; // Default to tool icon
  }

  getAdditionalFields(data: any): { key: string; value: any }[] {
    if (!data) return [];

    const excludeFields = [
      'id',
      'name',
      'description',
      'labelCode',
      'categoryName',
      'categoryId',
      'labelInfo',
    ];
    const additionalFields: { key: string; value: any }[] = [];

    Object.keys(data).forEach((key) => {
      if (
        !excludeFields.includes(key) &&
        data[key] !== null &&
        data[key] !== undefined
      ) {
        additionalFields.push({
          key: this.formatFieldName(key),
          value: data[key],
        });
      }
    });

    return additionalFields;
  }

  private formatFieldName(fieldName: string): string {
    return fieldName
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, (str) => str.toUpperCase())
      .trim();
  }

  private configureTabsForAgentType(): void {
    if (this.currentAgentType === 'individual') {
      this.tabs = [
        { id: 'prompts', label: 'Prompts', iconName: 'FileText' },
        { id: 'models', label: 'Models', iconName: 'Box' },
        { id: 'knowledge', label: 'Knowledge Base', iconName: 'BookOpen' },
        { id: 'guardrails', label: 'Guardrails', iconName: 'Swords' },
      ];
    } else {
      this.tabs = [
        { id: 'prompts', label: 'Prompts', iconName: 'FileText' },
        { id: 'models', label: 'Models', iconName: 'Box' },
        { id: 'knowledge', label: 'Knowledge Base', iconName: 'BookOpen' },
        { id: 'tools', label: 'Tools', iconName: 'Wrench' },
      ];
    }

    this._customTabs = this.tabs.map((tab) => ({
      label: tab.label,
      value: tab.id,
      icon: tab.iconName,
      disabled: false,
    }));

    this.activeTab = 'models';
  }

  private canAddNodeOfType(nodeType: string): boolean {
    const currentLimits =
      this.nodeLimits[this.currentAgentType as keyof typeof this.nodeLimits];
    const limit = currentLimits[nodeType as keyof typeof currentLimits];

    if (limit === undefined || limit === -1) return true;

    const currentCount = this.buildAgentNodes.filter(
      (node) => node.type === nodeType,
    ).length;
    return currentCount < limit;
  }

  private getNodeLimit(nodeType: string): number {
    const currentLimits =
      this.nodeLimits[this.currentAgentType as keyof typeof this.nodeLimits];
    return currentLimits[nodeType as keyof typeof currentLimits] || -1;
  }

  private removeItemFromList(tool: any): void {
    let tabType: string;
    if (tool.type === 'guardrail') {
      tabType = 'guardrails';
    } else if (tool.type === 'knowledge') {
      tabType = 'knowledge';
    } else {
      tabType = `${tool.type}s`;
    }
    if (this.allToolItems[tabType]) {
      this.allToolItems[tabType] = this.allToolItems[tabType].filter(
        (item) => item.id !== tool.id,
      );
    }
  }

  private addItemBackToList(node: BuildAgentNodeData): void {
    let tabType: string;
    if (node.type === 'guardrail') {
      tabType = 'guardrails';
    } else if (node.type === 'knowledge') {
      tabType = 'knowledge';
    } else {
      tabType = `${node.type}s`;
    }
    if (this.allToolItems[tabType]) {
      const originalToolId = node.originalToolData?.id;
      const existingItem = this.allToolItems[tabType].find(
        (item) =>
          item.name === node.name ||
          item.id === node.id ||
          (originalToolId && item.id === originalToolId),
      );

      if (!existingItem) {
        let toolItem: ToolItem;
        if (node.originalToolData) {
          toolItem = { ...node.originalToolData };
        } else {
          toolItem = {
            id: node.id,
            name: node.name,
            description: `${node.type} component: ${node.name}`,
            icon: node.icon || this.getIconForType(node.type),
            type: node.type,
          };
        }
        this.allToolItems[tabType].push(toolItem);
      }
    }
  }

  isTabRequired(tabValue: string): boolean {
    const nodeType =
      tabValue === 'guardrails' ? 'guardrail' : tabValue.slice(0, -1);
    const currentLimits =
      this.nodeLimits[this.currentAgentType as keyof typeof this.nodeLimits];
    const limit = currentLimits[nodeType as keyof typeof currentLimits];
    return limit === 1;
  }

  validateMandatoryComponents(): {
    isValid: boolean;
    missingComponents: string[];
  } {
    const missingComponents: string[] = [];
    const currentLimits =
      this.nodeLimits[this.currentAgentType as keyof typeof this.nodeLimits];

    Object.entries(currentLimits).forEach(([nodeType, limit]) => {
      if (limit === 1) {
        const hasComponent = this.buildAgentNodes.some(
          (node) => node.type === nodeType,
        );
        if (!hasComponent) {
          missingComponents.push(nodeType);
        }
      }
    });

    return { isValid: missingComponents.length === 0, missingComponents };
  }

  private loadDataForAgentType(): void {
    this.allToolItems = {
      prompts: [],
      models: [],
      knowledge: [],
      tools: [],
      guardrails: [],
    };
    this.loadLabelsAndData();
  }

  private loadLabelsAndData(): void {
    if (this.currentAgentType === 'collaborative') {
      this.loadPrompts();
      this.loadModels();
      this.loadKnowledgeBase();
      this.loadTools();
    } else {
      this.agentService.getLabels().subscribe({
        next: (response: any) => {
          this.labelsCache = response;
          this.loadPrompts();
          this.loadModelsFromCache();
          this.loadKnowledgeBaseFromCache();
          this.loadGuardrailsFromCache();
        },
        error: (error) => {
          console.error('Error loading labels API:', error);
          this.loadPrompts();
          this.loadModels();
          this.loadKnowledgeBase();
          this.loadGuardrails();
        },
      });
    }
  }

  getBuilderTitle(): string {
    return this.currentAgentType === 'individual'
      ? 'Individual Agent Builder'
      : 'Collaborative Agent Builder';
  }

  onDragStart(event: DragEvent, tool: ToolItem): void {
    if (event.dataTransfer) {
      event.dataTransfer.setData('application/reactflow', JSON.stringify(tool));
      event.dataTransfer.effectAllowed = 'move';
    }
  }

  onCanvasDropped(event: {
    event: DragEvent;
    position: { x: number; y: number };
  }): void {
    const toolData = event.event.dataTransfer?.getData('application/reactflow');
    if (!toolData) return;

    try {
      const tool = JSON.parse(toolData);

      if (!this.canAddNodeOfType(tool.type)) {
        const limit = this.getNodeLimit(tool.type);
        if (limit === 1) {
          const existingNode = this.buildAgentNodes.find(
            (node) => node.type === tool.type,
          );
          if (existingNode) {
            this.onDeleteNode(existingNode.id);
          }
        } else {
          this.showErrorMessage(
            'Node Limit Reached',
            `You can only add ${limit} ${tool.type} node(s) for ${this.currentAgentType} agents.`,
          );
          return;
        }
      }

      const autoPosition = this.calculateAutoPosition(tool.type);
      let nodeName = tool.name;
      if (tool.type === 'prompt') {
        if (this.currentAgentType === 'collaborative') {
          nodeName = tool.goal || tool.name;
        } else {
          nodeName = tool.prompt || tool.name;
        }
      }

      const buildAgentNode: BuildAgentNodeData = {
        id: this.generateNodeId(),
        name: nodeName,
        icon: tool.type === 'prompt' ? tool.icon : undefined,
        type: tool.type,
        position: autoPosition,
        originalToolData: tool,
      };

      const nodeWidth = this.isExecuteMode ? 55 : 90;
      const newCanvasNode: CanvasNode = {
        id: buildAgentNode.id,
        type: 'build-agent',
        data: { ...buildAgentNode, width: nodeWidth },
        position: autoPosition,
      };

      this.buildAgentNodes = [...this.buildAgentNodes, buildAgentNode];
      this.canvasNodes = [...this.canvasNodes, newCanvasNode];
      this.recalculateNodePositionsAfterDeletion();

      setTimeout(() => {
        this.createAgentFlowConnections(this.buildAgentNodes);
        if (this.canvasBoardComponent) {
          this.canvasBoardComponent.updateNodeConnectionPoints();
        }
        this.cdr.detectChanges();
      }, 200);

      this.removeItemFromList(tool);
      this.updateFilteredTools();
      this.cdr.detectChanges();
      this.handleAutoTabSwitch(tool.type);
    } catch (error) {
      console.error('Error adding node:', error);
    }
  }

  onNodeSelected(nodeId: string): void {
    this.selectedNodeId = nodeId;
    // Single click only selects the node, no preview
  }

  onNodeDoubleClicked(nodeId: string): void {
    const selectedNode = this.buildAgentNodes.find(
      (node) => node.id === nodeId,
    );
    if (selectedNode) {
      const toolItem = this.findToolItemForNode(selectedNode);
      if (toolItem) {
        this.onItemPreview(toolItem);
      } else {
        let actualToolItem: ToolItem | null = null;
        if (this.allToolItems[selectedNode.type + 's']) {
          actualToolItem =
            this.allToolItems[selectedNode.type + 's'].find(
              (item: any) => item.name === selectedNode.name,
            ) || null;
        }

        if (actualToolItem) {
          this.onItemPreview(actualToolItem);
        } else {
          const basicToolItem: ToolItem = {
            id: selectedNode.id,
            name: selectedNode.name,
            type: selectedNode.type,
            description: `${selectedNode.type} component: ${selectedNode.name}`,
            icon: selectedNode.icon || this.getIconForType(selectedNode.type),
          };
          this.onItemPreview(basicToolItem);
        }
      }
    }
  }

  private findToolItemForNode(node: BuildAgentNodeData): ToolItem | null {
    const tabType = node.type === 'guardrail' ? 'guardrails' : node.type + 's';
    return (
      this.allToolItems[tabType]?.find(
        (item: any) => item.name === node.name,
      ) || null
    );
  }

  onNodeMoved(event: {
    nodeId: string;
    position: { x: number; y: number };
  }): void {
    const buildAgentNodeIndex = this.buildAgentNodes.findIndex(
      (node) => node.id === event.nodeId,
    );
    if (buildAgentNodeIndex !== -1) {
      this.buildAgentNodes = [
        ...this.buildAgentNodes.slice(0, buildAgentNodeIndex),
        {
          ...this.buildAgentNodes[buildAgentNodeIndex],
          position: event.position,
        },
        ...this.buildAgentNodes.slice(buildAgentNodeIndex + 1),
      ];
    }

    const canvasNodeIndex = this.canvasNodes.findIndex(
      (node) => node.id === event.nodeId,
    );
    if (canvasNodeIndex !== -1) {
      this.canvasNodes = [
        ...this.canvasNodes.slice(0, canvasNodeIndex),
        { ...this.canvasNodes[canvasNodeIndex], position: event.position },
        ...this.canvasNodes.slice(canvasNodeIndex + 1),
      ];
    }

    this.cdr.detectChanges();
  }

  onDeleteNode(nodeId: string): void {
    const nodeToDelete = this.buildAgentNodes.find(
      (node) => node.id === nodeId,
    );
    if (nodeToDelete) {
      this.addItemBackToList(nodeToDelete);
    }

    this.buildAgentNodes = this.buildAgentNodes.filter(
      (node) => node.id !== nodeId,
    );
    this.canvasNodes = this.canvasNodes.filter((node) => node.id !== nodeId);
    this.canvasEdges = this.canvasEdges.filter(
      (edge) => edge.source !== nodeId && edge.target !== nodeId,
    );

    this.recalculateNodePositionsAfterDeletion();
    this.createAgentFlowConnections(this.buildAgentNodes);

    setTimeout(() => {
      if (this.canvasBoardComponent) {
        this.canvasBoardComponent.updateNodeConnectionPoints();
      }
      this.updateFilteredTools();
      this.cdr.detectChanges();
    }, 100);
  }

  private recalculateNodePositionsAfterDeletion(): void {
    const nodesByType: { [key: string]: BuildAgentNodeData[] } = {
      prompt: [],
      model: [],
      knowledge: [],
      guardrail: [],
      tool: [],
    };

    this.buildAgentNodes.forEach((node) => {
      if (nodesByType[node.type]) {
        nodesByType[node.type].push(node);
      }
    });

    const reorderedNodes: BuildAgentNodeData[] = [];
    const flowOrder = ['prompt', 'model', 'knowledge', 'guardrail', 'tool'];
    this.buildAgentNodes = [];

    flowOrder.forEach((nodeType) => {
      nodesByType[nodeType].forEach((node) => {
        node.position = this.calculateHierarchyBasedPosition(nodeType);
        reorderedNodes.push(node);
        this.buildAgentNodes.push(node);
      });
    });

    this.buildAgentNodes = reorderedNodes;
    this.canvasNodes = reorderedNodes.map((node) => ({
      id: node.id,
      type: 'build-agent',
      data: { ...node, width: this.isExecuteMode ? 55 : 90 },
      position: node.position,
    }));
  }

  onConnectionCreated(edge: CanvasEdge): void {
    const newEdge: CanvasEdge = {
      id:
        edge.id ||
        `edge_${edge.source}_${edge.target}_${Math.floor(Math.random() * 1000)}`,
      source: edge.source,
      target: edge.target,
      animated: edge.animated || true,
    };
    this.canvasEdges = [...this.canvasEdges, newEdge];
  }

  onStartConnection(_event: {
    nodeId: string;
    handleType: 'source' | 'target';
    event: MouseEvent;
  }): void {
    // Canvas board handles connection logic
  }

  onNodePositionChanged(event: {
    nodeId: string;
    position: { x: number; y: number };
  }): void {
    const buildAgentNodeIndex = this.buildAgentNodes.findIndex(
      (node) => node.id === event.nodeId,
    );
    if (buildAgentNodeIndex !== -1) {
      this.buildAgentNodes = [
        ...this.buildAgentNodes.slice(0, buildAgentNodeIndex),
        {
          ...this.buildAgentNodes[buildAgentNodeIndex],
          position: event.position,
        },
        ...this.buildAgentNodes.slice(buildAgentNodeIndex + 1),
      ];
    }

    const canvasNodeIndex = this.canvasNodes.findIndex(
      (node) => node.id === event.nodeId,
    );
    if (canvasNodeIndex !== -1) {
      this.canvasNodes = [
        ...this.canvasNodes.slice(0, canvasNodeIndex),
        { ...this.canvasNodes[canvasNodeIndex], position: event.position },
        ...this.canvasNodes.slice(canvasNodeIndex + 1),
      ];
    }

    this.cdr.detectChanges();
  }

  onUndo(): void {
    /* Implement undo functionality */
  }
  onRedo(): void {
    /* Implement redo functionality */
  }
  onReset(): void {
    this.buildAgentNodes = [];
    this.canvasNodes = [];
    this.canvasEdges = [];
    this.currentAgentDetails = null;
  }
  onRun(): void {
    this.onExecute();
  }

  // Get metadata from navbar cookies
  private getMetadataFromNavbar(): {
    orgPath: string;
    levelId: number;
    orgId?: number;
    domainId?: number;
    projectId?: number;
    teamId?: number;
    org?: string;
    domain?: string;
    project?: string;
    team?: string;
  } {
    const orgPath = this.tokenStorage.getCookie('org_path');
    let organizationPath = '';
    let levelId = 0; // Initialize to 0 instead of default value
    let orgId, domainId, projectId, teamId;
    let org, domain, project, team;

    if (orgPath) {
      const parts = orgPath.split('::');
      const usecasePath = parts[0] || '';
      const usecaseIdPath = parts[1] || '';
      organizationPath = usecasePath;

      const names = usecasePath.split('@');
      if (names.length >= 4) {
        [org, domain, project, team] = names;
      }

      const ids = usecaseIdPath.split('@').map(Number);
      if (ids.length >= 4) {
        [orgId, domainId, projectId, teamId] = ids;
        levelId = teamId || ids[ids.length - 1] || 0; // Remove default fallback
      } else if (ids.length > 0 && ids[0]) {
        levelId = ids[0];
      }
    }

    return {
      orgPath: organizationPath,
      levelId,
      orgId,
      domainId,
      projectId,
      teamId,
      org,
      domain,
      project,
      team,
    };
  }

  private getUserSignature(): string {
    return this.tokenStorage.getDaUsername() || '<EMAIL>';
  }
  private buildConfigurationFromLabels(): any[] {
    const configurations: any[] = [];
    const hasModel = this.buildAgentNodes.some((node) => node.type === 'model');
    const hasKnowledge = this.buildAgentNodes.some(
      (node) => node.type === 'knowledge',
    );

    configurations.push({
      categoryName: 'Model',
      categoryId: 1,
      configs: [
        {
          configurationName: 'MODEL',
          configurationValue: hasModel ? this.getModelConfigValue() : '',
        },
        { configurationName: 'MAX_TOKEN', configurationValue: '' },
        { configurationName: 'TEMPERATURE', configurationValue: '' },
        { configurationName: 'TOP_P', configurationValue: '' },
        { configurationName: 'FREQUENCY_PENALTY', configurationValue: '' },
        { configurationName: 'PROMPT_PREFIX', configurationValue: '' },
        { configurationName: 'PROMPT_WRAPPER', configurationValue: '' },
      ],
    });

    configurations.push({
      categoryName: 'In Context Learning (ICL)',
      categoryId: 2,
      configs: [
        {
          configurationName: 'RAG',
          configurationValue: hasKnowledge ? 'true' : '',
        },
        { configurationName: 'RAG_MODE', configurationValue: '' },
        {
          configurationName: 'RAG_KNOWLEDGEBASE_NAME',
          configurationValue: hasKnowledge
            ? this.getKnowledgeBaseConfigValue()
            : '',
        },
        {
          configurationName: 'RAG_KNOWLEDGEBASE_MAX_RECORD_COUNT',
          configurationValue: '',
        },
        { configurationName: 'TOKEN_COMPRESSION', configurationValue: '' },
      ],
    });

    const availableGuardrails = this.allToolItems['guardrails'] || [];
    if (availableGuardrails.length > 0) {
      configurations.push({
        categoryName: 'Other',
        categoryId: 3,
        configs: this.getGuardrailConfigs(),
      });
    }
    return configurations;
  }

  private extractPromptId(nodeId: string): string {
    const match = nodeId.match(/prompt-(\d+)/);
    return match ? match[1] : nodeId;
  }

  private getModelConfigValue(): string {
    const modelNode = this.buildAgentNodes.find(
      (node) => node.type === 'model',
    );
    if (modelNode) {
      if (modelNode.originalToolData && modelNode.originalToolData.id) {
        return modelNode.originalToolData.id.toString();
      }
      const modelData = this.allToolItems['models'].find(
        (model) => model.name === modelNode.name,
      );
      if (modelData) return modelData.id.toString();

      const modelDataById = this.allToolItems['models'].find(
        (model) =>
          model.id === modelNode.name || model.id.toString() === modelNode.name,
      );
      if (modelDataById) return modelDataById.id.toString();
    }
    return '32';
  }

  private getKnowledgeBaseConfigValue(): string {
    const knowledgeNodes = this.buildAgentNodes.filter(
      (node) => node.type === 'knowledge',
    );
    if (knowledgeNodes.length > 0) {
      const knowledgeIds = knowledgeNodes
        .map((node) => {
          if (node.originalToolData && node.originalToolData.id) {
            return node.originalToolData.id.toString();
          }
          const knowledgeData = this.allToolItems['knowledge'].find(
            (kb) => kb.name === node.name,
          );
          return knowledgeData ? knowledgeData.id : null;
        })
        .filter((id) => id !== null);
      return knowledgeIds.join(',');
    }
    return '';
  }

  private extractGuardrailId(nodeId: string): string {
    return nodeId.match(/guardrail-(\d+)/)?.[1] || nodeId;
  }

  private getGuardrailConfigs(): any[] {
    const guardrailNodes = this.buildAgentNodes.filter(
      (node) => node.type === 'guardrail',
    );
    const configs: any[] = [];
    configs.push({
      configurationName: 'ENABLE_GUARDRAILS',
      configurationValue: guardrailNodes.length > 0 ? 'true' : 'false',
    });

    const availableGuardrails = this.allToolItems['guardrails'] || [];
    const selectedGuardrailNames = new Set<string>();

    guardrailNodes.forEach((node) => {
      const guardrailId = this.extractGuardrailId(node.id);
      const guardrailData = availableGuardrails.find(
        (gr) =>
          gr.id === guardrailId || gr.id === node.id || gr.name === node.name,
      );

      const configName = guardrailData
        ? `GUARDRAIL_${guardrailData.name
            .toUpperCase()
            .replace(/\s+/g, '_')
            .replace(/[^A-Z0-9_]/g, '')}`
        : `GUARDRAIL_${node.name
            .toUpperCase()
            .replace(/\s+/g, '_')
            .replace(/[^A-Z0-9_]/g, '')}`;
      selectedGuardrailNames.add(configName);
    });

    availableGuardrails.forEach((guardrail) => {
      if (guardrail.name === 'Enable Guardrails') return;
      const configName = `GUARDRAIL_${guardrail.name
        .toUpperCase()
        .replace(/\s+/g, '_')
        .replace(/[^A-Z0-9_]/g, '')}`;
      const isSelected = selectedGuardrailNames.has(configName);
      configs.push({
        configurationName: configName,
        configurationValue: isSelected ? true : false,
      });
    });
    return configs;
  }

  private validateIndividualAgentData(): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!this.agentName || this.agentName.trim() === '')
      errors.push('Agent name is required');
    if (!this.agentDetail || this.agentDetail.trim() === '')
      errors.push('Agent details are required');

    const promptNode = this.buildAgentNodes.find(
      (node) => node.type === 'prompt',
    );
    if (!promptNode) errors.push('Prompt selection is required');

    const modelNode = this.buildAgentNodes.find(
      (node) => node.type === 'model',
    );
    if (!modelNode)
      warnings.push('Model selection is recommended for better performance');

    const knowledgeNode = this.buildAgentNodes.find(
      (node) => node.type === 'knowledge',
    );
    if (!knowledgeNode)
      warnings.push(
        'Knowledge base selection is recommended for enhanced responses',
      );

    const guardrailNode = this.buildAgentNodes.find(
      (node) => node.type === 'guardrail',
    );
    if (!guardrailNode)
      warnings.push(
        'Guardrail selection is recommended for safe AI interactions',
      );

    return { isValid: errors.length === 0, errors, warnings };
  }

  private buildIndividualAgentPayload(): any {
    const { orgPath, levelId } = this.getMetadataFromNavbar();
    const promptNode = this.buildAgentNodes.find(
      (node) => node.type === 'prompt',
    );
    let promptDetails = '';
    let promptTemplate = '';

    if (promptNode) {
      const promptId = this.extractPromptId(promptNode.id);
      const promptData = this.allToolItems['prompts'].find(
        (prompt) =>
          prompt.id === promptId ||
          prompt.id === promptNode.id ||
          prompt.name === promptNode.name,
      );

      if (promptData) {
        promptDetails = promptData.description || promptData.name || '';
        promptTemplate =
          (promptData as any).template ||
          (promptData as any).content ||
          (promptData as any).promptTemplate ||
          promptData.name ||
          '';
      } else {
        promptDetails = promptNode.name;
        promptTemplate = promptNode.name;
      }
    }

    const useCaseCode = this.agentName
      .trim()
      .replace(/\s+/g, '_')
      .toUpperCase();
    const finalUseCaseDetails = this.agentDetail
      ? this.agentDetail.trim()
      : promptDetails;
    const baseOrgPath =
      orgPath || 'ADD_NEW@SENIOR_BUSINESS_ANALYST@TEST_GOPAL@TEAM1';
    const finalOrgPath = `${useCaseCode}@${baseOrgPath}`;

    return {
      levelId,
      code: useCaseCode,
      name: this.agentName.trim(),
      useCaseDetails: finalUseCaseDetails,
      promptTemplate,
      type: null,
      webPortalUseCase: null,
      location: null,
      sourceLanguage: null,
      destinationLanguage: null,
      configuration: this.buildConfigurationFromLabels(),
      userSignature: this.getUserSignature(),
      organizationPath: finalOrgPath,
    };
  }

  private async fetchPromptDetailsForSave(): Promise<void> {
    const promptNode = this.buildAgentNodes.find(
      (node) => node.type === 'prompt',
    );
    if (!promptNode) return;

    let promptData = this.allToolItems['prompts']?.find(
      (p) => p.name === promptNode.name,
    );
    if (!promptData && promptNode.originalToolData) {
      promptData = promptNode.originalToolData;
    }

    if (!promptData) {
      const extractedId = this.extractPromptId(promptNode.id);
      if (extractedId && extractedId !== promptNode.id) {
        try {
          const apiPromptData = await new Promise((resolve, reject) => {
            this.promptsService.getPromptById(extractedId).subscribe({
              next: (data) => resolve(data),
              error: (error) => {
                console.error('Error fetching prompt from API:', error);
                reject(error);
              },
            });
          });

          if (apiPromptData) {
            if (!this.allToolItems['prompts'])
              this.allToolItems['prompts'] = [];
            this.allToolItems['prompts'].push(apiPromptData as any);
          }
        } catch (error) {
          console.error('Error fetching prompt details:', error);
        }
      }
    }
  }

  private async saveIndividualAgent(): Promise<void> {
    if (this.currentAgentType !== 'individual') return;

    const validation = this.validateIndividualAgentData();
    if (!validation.isValid) {
      this.showErrorMessage(
        'Validation Failed',
        'Please fix the following errors:\n' + validation.errors.join('\n'),
      );
      return;
    }

    if (validation.warnings && validation.warnings.length > 0) {
      const warningMessage =
        'The following configurations are recommended:\n' +
        validation.warnings.join('\n') +
        '\n\nDo you want to continue saving without these configurations?';
      this.showWarningMessage('Configuration Recommendations', warningMessage);
      return;
    }

    await this.performIndividualAgentSave();
  }

  private async performIndividualAgentSave(): Promise<void> {
    try {
      const payload = this.buildIndividualAgentPayload();
      this.agentService.individualAgentSave(payload).subscribe({
        next: (response: any) => {
          const successMessage =
            response?.message ||
            `Individual agent "${payload.name || 'Agent'}" has been saved successfully!`;
          this.showSuccessMessage('Agent Saved Successfully', successMessage);

          const newAgentOption = {
            value:
              payload.code || this.agentName.toUpperCase().replace(/\s+/g, ''),
            name: this.agentName,
          };
          const existingIndex = this.promptOptions.findIndex(
            (option) =>
              option.value === newAgentOption.value ||
              option.name === newAgentOption.name,
          );

          if (existingIndex === -1) {
            this.promptOptions.splice(1, 0, newAgentOption);
          } else {
            this.promptOptions[existingIndex] = newAgentOption;
          }

          this.loadAgentDetailsForPlayground();
          setTimeout(() => {
            this.onExecute();
          }, 1000);
        },
        error: (error: any) => {
          console.error('Error saving individual agent:', error);
          const errorMessage =
            error?.error?.message ||
            error?.message ||
            'Error saving individual agent. Please try again.';
          this.showErrorMessage('Save Failed', errorMessage);
        },
      });
    } catch (error) {
      console.error('Error preparing agent data for save:', error);
      this.showErrorMessage(
        'Save Failed',
        'Error preparing agent data for save. Please check console for details.',
      );
    }
  }

  private async saveCollaborativeAgent(): Promise<void> {
    if (this.currentAgentType !== 'collaborative') return;

    // Validate level path is available from header
    const { levelId } = this.getMetadataFromNavbar();
    if (!levelId || levelId === 0) {
      this.showErrorMessage(
        'Organization Path Required',
        'Please select a valid organization path from the header navigation before creating a collaborative agent.',
      );
      return;
    }

    const validation = this.validateCollaborativeAgentData();
    if (!validation.isValid) {
      this.showErrorMessage(
        'Validation Failed',
        'Please fix the following errors:\n' + validation.errors.join('\n'),
      );
      return;
    }

    if (validation.warnings && validation.warnings.length > 0) {
      const warningMessage =
        'The following configurations are recommended:\n' +
        validation.warnings.join('\n') +
        '\n\nDo you want to continue saving without these configurations?';
      this.showWarningMessage('Configuration Recommendations', warningMessage);
      return;
    }

    await this.performCollaborativeAgentSave();
  }

  private async performCollaborativeAgentSave(): Promise<void> {
    try {
      await this.fetchPromptDetailsForSave();
      const payload = this.buildCollaborativeAgentPayloadV2();

      this.agentService.createCollaborativeAgentV2(payload).subscribe({
        next: (response: any) => {
          if (response?.agentId) {
            this.currentAgentId = response.agentId.toString();
          }

          const successMessage =
            response?.message ||
            `Collaborative agent "${payload.name || 'Agent'}" has been saved and sent for approval successfully!`;
          this.showSuccessMessage(
            'Agent Saved and Sent for Approval',
            successMessage,
          );

          const newAgentOption = {
            value: this.agentName.toUpperCase().replace(/\s+/g, ''),
            name: this.agentName,
          };
          const existingIndex = this.promptOptions.findIndex(
            (option) =>
              option.value === newAgentOption.value ||
              option.name === newAgentOption.name,
          );

          if (existingIndex === -1) {
            this.promptOptions.splice(1, 0, newAgentOption);
          } else {
            this.promptOptions[existingIndex] = newAgentOption;
          }

          this.loadAgentDetailsForPlayground();
          setTimeout(() => {
            this.onExecute();
          }, 1000);
        },
        error: (error: any) => {
          console.error('Error saving collaborative agent:', error);
          const errorMessage =
            error?.error?.message ||
            error?.message ||
            'Error saving collaborative agent. Please try again.';
          this.showErrorMessage('Save Failed', errorMessage);
        },
      });
    } catch (error: any) {
      console.error(
        'Error preparing collaborative agent data for save:',
        error,
      );
      const errorMessage =
        error?.message ||
        'Error preparing collaborative agent data for save. Please check console for details.';
      this.showErrorMessage('Save Failed', errorMessage);
    }
  }

  private validateCollaborativeAgentData(): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!this.agentName || this.agentName.trim().length === 0)
      errors.push('Agent name is required');
    if (!this.agentDetail || this.agentDetail.trim().length === 0)
      errors.push('Agent details are required');

    const promptNodes = this.buildAgentNodes.filter(
      (node) => node.type === 'prompt',
    );
    if (promptNodes.length === 0) errors.push('Prompt selection is required');

    const modelNodes = this.buildAgentNodes.filter(
      (node) => node.type === 'model',
    );
    if (modelNodes.length === 0) errors.push('Model selection is required');

    const knowledgeNodes = this.buildAgentNodes.filter(
      (node) => node.type === 'knowledge',
    );
    if (knowledgeNodes.length === 0)
      warnings.push(
        'Knowledge base selection is recommended for enhanced responses',
      );

    const toolNodes = this.buildAgentNodes.filter(
      (node) => node.type === 'tool',
    );
    if (toolNodes.length === 0)
      warnings.push(
        'Tool selection is recommended for collaborative agent capabilities',
      );

    return { isValid: errors.length === 0, errors, warnings };
  }

  private buildCollaborativeAgentPayloadV2(): any {
    const { levelId } = this.getMetadataFromNavbar();

    const modelNodes = this.buildAgentNodes.filter(
      (node) => node.type === 'model',
    );
    const modelRefs = modelNodes.map((node) => {
      const modelData = this.allToolItems['models'].find(
        (m) => m.name === node.name,
      );
      return modelData?.id || 40;
    });
    const uniqueModelRefs = [...new Set(modelRefs)];

    const knowledgeNodes = this.buildAgentNodes.filter(
      (node) => node.type === 'knowledge',
    );
    const knowledgeIds = knowledgeNodes
      .map((node) => {
        if (node.originalToolData && node.originalToolData.id) {
          return node.originalToolData.id;
        }
        const knowledgeData = this.allToolItems['knowledge'].find(
          (k) => k.name === node.name,
        );
        return knowledgeData?.id;
      })
      .filter((id) => id);
    const uniqueKnowledgeIds = [...new Set(knowledgeIds)];

    const promptNode = this.buildAgentNodes.find(
      (node) => node.type === 'prompt',
    );
    let promptData = null;

    if (promptNode) {
      if (promptNode.originalToolData) {
        promptData = promptNode.originalToolData;
      } else {
        promptData = this.allToolItems['prompts'].find(
          (p) => p.name === promptNode.name,
        );
        if (!promptData) {
          const extractedId = this.extractPromptId(promptNode.id);
          promptData = this.allToolItems['prompts'].find(
            (p) =>
              String(p.id) === extractedId ||
              String(p.id) === String(parseInt(extractedId)),
          );
        }
      }
    }

    const originalPromptData = promptData?.originalPromptData || promptData;
    const sourceData = originalPromptData || promptData;
    const role = sourceData?.role || 'Python Developer';
    const goal =
      sourceData?.goal ||
      this.agentDetail ||
      'Analyze data and provide insights using Python';
    const backstory =
      sourceData?.backstory ||
      this.agentDetail ||
      'You are an experienced data analyst with strong Python skills.';
    const description =
      sourceData?.description ||
      sourceData?.descriptionConsolidated ||
      this.agentDetail ||
      '%1$s';
    const expectedOutput =
      sourceData?.expectedOutput ||
      sourceData?.expectedOutputConsolidated ||
      'Output is :';

    const toolNodes = this.buildAgentNodes.filter(
      (node) => node.type === 'tool',
    );
    const builtInTools: number[] = [];
    const userTools: number[] = [];

    toolNodes.forEach((node) => {
      const toolData = this.allToolItems['tools'].find(
        (t) => t.name === node.name,
      );
      let toolId: string | number = toolData?.id || '3';
      let numericToolId: number = 3;

      if (typeof toolId === 'string') {
        const numericMatch = toolId.match(/\d+$/);
        if (numericMatch) {
          numericToolId = parseInt(numericMatch[0]);
        }
      } else if (typeof toolId === 'number') {
        numericToolId = toolId;
      }

      const isUserTool =
        typeof toolData?.id === 'string' && toolData?.id.startsWith('user-');
      if (isUserTool) {
        userTools.push(numericToolId);
      } else {
        builtInTools.push(numericToolId);
      }
    });

    const agentConfigs = {
      temperature: 0.3,
      topP: 0.95,
      maxToken: '4000',
      modelRef: uniqueModelRefs,
      knowledgeBaseRef: uniqueKnowledgeIds,
      maxIter: null,
      maxRpm: 5,
      maxExecutionTime: 5,
      allowDelegation: true,
      allowCodeExecution: true,
      isSafeCodeExecution: true,
      toolRef: builtInTools,
      userToolRef: userTools,
    };

    const userSignature = this.getUserSignature();

    // Ensure levelId is properly retrieved from header without default fallback
    if (!levelId || levelId === 0) {
      throw new Error(
        'Organization path is required from header navigation. Please select a valid organization path before creating a collaborative agent.',
      );
    }

    return {
      agentDetails: this.agentDetail.trim(),
      name: this.agentName.trim(),
      role,
      goal,
      backstory,
      description,
      expectedOutput,
      agentConfigs,
      createdBy: userSignature,
      levelId: levelId,
      modifiedBy: userSignature,
    };
  }

  private buildCollaborativeAgentChangeRequestPayload(): any {
    const basePayload = this.buildCollaborativeAgentPayloadV2();
    return {
      id: parseInt(this.currentAgentId!),
      ...basePayload,
      isDeleted: false,
    };
  }

  private buildIndividualAgentUpdatePayload(): any {
    const basePayload = this.buildIndividualAgentPayload();
    return {
      ...basePayload,
      useCaseId: parseInt(this.currentAgentId!),
      configuration: basePayload.configuration.map((category: any) => ({
        ...category,
        configs: category.configs.map((config: any) => ({
          ...config,
          configId:
            this.agentConfigIds.get(
              `${category.categoryId}-${config.configurationName}`,
            ) || undefined,
        })),
      })),
    };
  }

  private async updateIndividualAgent(): Promise<void> {
    if (this.currentAgentType !== 'individual' || !this.currentAgentId) return;

    const validation = this.validateIndividualAgentData();
    if (!validation.isValid) {
      console.error('Validation failed:', validation.errors);
      this.showErrorMessage(
        'Validation Failed',
        'Please fix the following errors:\n' + validation.errors.join('\n'),
      );
      return;
    }

    try {
      const payload = this.buildIndividualAgentUpdatePayload();
      this.agentService.individualAgentEdit(payload).subscribe({
        next: (response: any) => {
          const successMessage =
            response?.message ||
            `Individual agent "${payload.name || 'Agent'}" has been updated successfully!`;
          this.showSuccessMessage('Agent Updated Successfully', successMessage);
          this.loadAgentDetailsForPlayground();
          setTimeout(() => {
            this.onExecute();
          }, 1000);
        },
        error: (error: any) => {
          console.error('Error updating individual agent:', error);
          const errorMessage =
            error?.error?.message ||
            error?.message ||
            'Error updating individual agent. Please try again.';
          this.showErrorMessage('Update Failed', errorMessage);
        },
      });
    } catch (error) {
      console.error('Error preparing agent data for update:', error);
      this.showErrorMessage(
        'Update Failed',
        'Error preparing agent data for update. Please check console for details.',
      );
    }
  }

  private async submitCollaborativeAgentChangeRequest(): Promise<void> {
    if (this.currentAgentType !== 'collaborative' || !this.currentAgentId)
      return;

    // Validate level path is available from header
    const { levelId } = this.getMetadataFromNavbar();
    if (!levelId || levelId === 0) {
      this.showErrorMessage(
        'Organization Path Required',
        'Please select a valid organization path from the header navigation before updating a collaborative agent.',
      );
      return;
    }

    const validation = this.validateCollaborativeAgentData();
    if (!validation.isValid) {
      console.error('Validation failed:', validation.errors);
      this.showErrorMessage(
        'Validation Failed',
        'Please fix the following errors:\n' + validation.errors.join('\n'),
      );
      return;
    }

    try {
      const payload = this.buildCollaborativeAgentChangeRequestPayload();
      this.agentService
        .submitCollaborativeAgentChangeRequest(payload)
        .subscribe({
          next: (response: any) => {
            const successMessage =
              response?.message ||
              `Change request for agent "${payload.name || 'Agent'}" has been submitted successfully!`;
            this.showSuccessMessage('Change Request Submitted', successMessage);
            this.loadAgentDetailsForPlayground();
            setTimeout(() => {
              this.onExecute();
            }, 1000);
          },
          error: (error: any) => {
            console.error(
              'Error submitting collaborative agent change request:',
              error,
            );
            const errorMessage =
              error?.error?.message ||
              error?.message ||
              'Failed to submit change request. Please try again.';
            this.showErrorMessage('Change Request Failed', errorMessage);
          },
        });
    } catch (error: any) {
      console.error('Error preparing change request:', error);
      const errorMessage =
        error?.message ||
        'Failed to prepare change request. Please check the console for details.';
      this.showErrorMessage('Change Request Failed', errorMessage);
    }
  }

  public onApprovalRequested(): void {
    // This method is kept for compatibility but no longer used for collaborative agents
  }

  async onPrimaryButtonClick(): Promise<void> {
    if (this.isExecuteMode) return;

    if (this.isViewMode) {
      this.onExecute();
      return;
    }

    if (this.isEditMode) {
      if (this.currentAgentType === 'individual') {
        await this.updateIndividualAgent();
      } else if (this.currentAgentType === 'collaborative') {
        await this.submitCollaborativeAgentChangeRequest();
      }
      return;
    }

    const validation =
      this.currentAgentType === 'individual'
        ? this.validateIndividualAgentData()
        : this.validateCollaborativeAgentData();
    if (!validation.isValid) {
      this.showErrorMessage(
        'Validation Failed',
        'Please fix the following errors before saving:\n' +
          validation.errors.join('\n'),
      );
      return;
    }

    if (this.currentAgentType === 'individual') {
      await this.saveIndividualAgent();
    } else if (this.currentAgentType === 'collaborative') {
      await this.saveCollaborativeAgent();
    }
  }

  onExecute(): void {
    this.isExecuteMode = true;
    this.showChatInterface = true;
    this.recalculateAllNodePositions();

    setTimeout(() => {
      this.cdr.detectChanges();
      if (this.canvasBoardComponent) {
        this.canvasBoardComponent.updateNodeConnectionPoints();
      }
    }, 200);

    this.loadAgentDetailsForPlayground();
    if (this.agentName) {
      setTimeout(() => {
        this.autoSelectCurrentAgent();
      }, 1500);
    }

    this.chatMessages = [
      {
        from: 'ai',
        text: `Hi there! I am ${this.agentName || 'your build agent'}. How can I help you today?`,
      },
    ];

    setTimeout(() => {
      const agentId = 'build-agent-' + Date.now();
      this.toolExecutionService.startExecution(agentId, this.chatMessages);
      this.executionSubscription = this.toolExecutionService
        .getExecutionState()
        .subscribe((state) => {
          if (state.isExecuting && state.toolId === agentId) {
            this.chatMessages = state.chatMessages;
          }
        });
    }, 100);
  }

  handleChatMessage(message: string): void {
    if (!this.selectedPrompt || this.selectedPrompt === 'default') {
      this.showAgentError(
        'Please select an agent from the dropdown before testing.',
      );
      return;
    }

    let displayMessage = message;
    if (this.agentFilesUploadedData.length > 0) {
      const fileNames = this.agentFilesUploadedData
        .map((file) => file.documentName)
        .join(', ');
      displayMessage = `${message}\n\n📎 Attached files: ${fileNames}`;
    }

    this.chatMessages = [
      ...this.chatMessages,
      { from: 'user', text: displayMessage },
    ];
    this.isProcessingChat = true;

    const isConversational =
      this.agentPlaygroundForm.get('isConversational')?.value || false;
    const isUseTemplate =
      this.agentPlaygroundForm.get('isUseTemplate')?.value || false;
    const agentMode =
      this.agentCode || this.selectedAgentMode || this.selectedPrompt;

    let useCaseIdentifier = this.selectedUseCaseIdentifier;
    if (!useCaseIdentifier) {
      const orgPath = this.buildOrganizationPath();
      useCaseIdentifier = `${agentMode}${orgPath}`;
    }

    if (this.agentFilesUploadedData.length > 0) {
      this.processAgentFilesAndSendMessage(
        message,
        agentMode,
        useCaseIdentifier,
        isConversational,
        isUseTemplate,
      );
      return;
    }

    this.sendAgentMessageToAPI(
      message,
      agentMode,
      useCaseIdentifier,
      isConversational,
      isUseTemplate,
    );
  }

  private processAgentFilesAndSendMessage(
    message: string,
    mode: string,
    useCaseIdentifier: string,
    isConversational: boolean,
    isUseTemplate: boolean,
  ): void {
    const formData = new FormData();
    this.agentFilesUploadedData.forEach((fileData) => {
      if (fileData.file) {
        formData.append('files', fileData.file);
      }
    });

    if (formData.has('files')) {
      this.agentPlaygroundService
        .getFileToContent(formData)
        .pipe(
          switchMap((fileResponse) => {
            const fileContent =
              fileResponse?.fileResponses
                ?.map((response: any) => response.fileContent)
                ?.join('\n') || '';
            this.sendAgentMessageToAPIWithFiles(
              message,
              mode,
              useCaseIdentifier,
              isConversational,
              isUseTemplate,
              fileContent,
            );
            return of(null);
          }),
          catchError((error) => {
            console.error('Error parsing files:', error);
            this.sendAgentMessageToAPI(
              message,
              mode,
              useCaseIdentifier,
              isConversational,
              isUseTemplate,
            );
            return of(null);
          }),
        )
        .subscribe();
    } else {
      this.sendAgentMessageToAPI(
        message,
        mode,
        useCaseIdentifier,
        isConversational,
        isUseTemplate,
      );
    }
  }

  private sendAgentMessageToAPI(
    message: string,
    mode: string,
    useCaseIdentifier: string,
    isConversational: boolean,
    isUseTemplate: boolean,
  ): void {
    if (isConversational) {
      this.agentChatPayload.push({ content: message, role: 'user' });
    }

    const payload = isConversational ? this.agentChatPayload : message;
    const { levelId } = this.getMetadataFromNavbar();

    this.agentPlaygroundService
      .generatePrompt(
        payload,
        mode,
        isConversational,
        isUseTemplate,
        this.agentAttachment,
        useCaseIdentifier,
        '',
        levelId,
      )
      .pipe(
        finalize(() => {
          this.isProcessingChat = false;
          this.isAgentPlaygroundLoading = false;
        }),
        takeUntil(this.agentPlaygroundDestroy),
      )
      .subscribe({
        next: (generatedResponse: any) => {
          if (
            generatedResponse?.response &&
            generatedResponse?.response?.choices
          ) {
            const aiResponseText = generatedResponse.response.choices[0].text;
            this.chatMessages = [
              ...this.chatMessages,
              { from: 'ai', text: aiResponseText },
            ];
            if (isConversational) {
              this.agentChatPayload.push({
                content: aiResponseText,
                role: 'assistant',
              });
            }
          } else {
            console.warn('Unexpected API response format:', generatedResponse);
            this.showAgentError(
              'Received unexpected response format from API.',
            );
          }
        },
        error: (error: any) => {
          console.error('API Error:', error);
          const errorMessage =
            error?.error?.message ||
            'An error occurred while processing your request.';
          this.showAgentError(errorMessage);
          if (isConversational && this.agentChatPayload.length > 0) {
            this.agentChatPayload.pop();
          }
        },
      });
  }

  private sendAgentMessageToAPIWithFiles(
    message: string,
    mode: string,
    useCaseIdentifier: string,
    isConversational: boolean,
    isUseTemplate: boolean,
    fileContents: string,
  ): void {
    if (isConversational) {
      this.agentChatPayload.push({ content: message, role: 'user' });
    }
    const payload = isConversational ? this.agentChatPayload : message;
    const { levelId } = this.getMetadataFromNavbar();

    this.agentPlaygroundService
      .generatePrompt(
        payload,
        mode,
        isConversational,
        isUseTemplate,
        this.agentAttachment,
        useCaseIdentifier,
        fileContents,
        levelId,
      )
      .pipe(
        finalize(() => {
          this.isProcessingChat = false;
          this.isAgentPlaygroundLoading = false;
        }),
        takeUntil(this.agentPlaygroundDestroy),
      )
      .subscribe({
        next: (generatedResponse: any) => {
          if (
            generatedResponse?.response &&
            generatedResponse?.response?.choices
          ) {
            const aiResponseText = generatedResponse.response.choices[0].text;
            this.chatMessages = [
              ...this.chatMessages,
              { from: 'ai', text: aiResponseText },
            ];
            if (isConversational) {
              this.agentChatPayload.push({
                content: aiResponseText,
                role: 'assistant',
              });
            }
          } else {
            console.warn('Unexpected API response format:', generatedResponse);
            this.showAgentError(
              'Received unexpected response format from API.',
            );
          }
        },
        error: (error: any) => {
          console.error('API Error:', error);
          const errorMessage =
            error?.error?.message ||
            'An error occurred while processing your request.';
          this.showAgentError(errorMessage);
          if (isConversational && this.agentChatPayload.length > 0) {
            this.agentChatPayload.pop();
          }
        },
      });
  }

  private showAgentError(message: string): void {
    this.chatMessages = [...this.chatMessages, { from: 'ai', text: message }];
  }

  onPlaygroundConversationalToggle(event: boolean): void {
    this.agentPlaygroundForm.get('isConversational')?.setValue(event);
  }

  onPlaygroundTemplateToggle(event: boolean): void {
    this.agentPlaygroundForm.get('isUseTemplate')?.setValue(event);
  }

  onFilesSelected(files: any[]): void {
    this.agentFilesUploadedData = files;
  }

  onAgentConversationalToggle(event: any): void {
    const isConversational = event;
    this.agentPlaygroundForm
      .get('isConversational')
      ?.setValue(isConversational);
    if (
      isConversational &&
      this.agentPlaygroundForm.get('isUseTemplate')?.value
    ) {
      this.agentPlaygroundForm.get('isUseTemplate')?.setValue(false);
    }
  }

  onAgentTemplateToggle(event: any): void {
    const isUseTemplate = event;
    this.agentPlaygroundForm.get('isUseTemplate')?.setValue(isUseTemplate);
    if (
      isUseTemplate &&
      this.agentPlaygroundForm.get('isConversational')?.value
    ) {
      this.agentPlaygroundForm.get('isConversational')?.setValue(false);
    }
  }

  onAgentFileSelected(event: any): void {
    const files = event.target.files;
    if (files && files.length > 0) {
      this.agentFilesUploadedData = [];
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        this.agentFilesUploadedData.push({
          id: `agent_file_${Date.now()}_${i}`,
          documentName: file.name,
          isImage: file.type.startsWith('image/'),
          file: file,
        });
      }
    }
  }

  removeAgentFile(index: number): void {
    this.agentFilesUploadedData.splice(index, 1);
  }

  clearAgentChatData(): void {
    this.chatMessages = [
      {
        from: 'ai',
        text: 'Hi there, how can I help you test your agent today?',
      },
    ];
    this.agentChatPayload = [];
    this.agentFilesUploadedData = [];
    this.agentAttachment = [];
  }

  showResponseModal(message: string, isError: boolean = false): void {
    this.modalMessage = message;
    this.isModalError = isError;
    this.isResponseModalOpen = true;
  }

  closeResponseModal(): void {
    this.isResponseModalOpen = false;
    this.modalMessage = '';
    this.isModalError = false;
  }

  public loadAgentDetailsForPlayground(): void {
    if (this.currentAgentDetails) {
      const agentData = this.currentAgentDetails;
      this.promptOptions = [
        {
          value:
            agentData.useCaseCode ||
            agentData.code ||
            this.currentAgentId ||
            'DEFAULT_AGENT',
          name: agentData.useCaseName || agentData.name || 'Current Agent',
          agentData: agentData,
        } as any,
      ];

      this.selectedPrompt = this.promptOptions[0].value.toString();
      this.selectedAgentMode = agentData.useCaseCode || agentData.code || '';

      if (agentData.organizationPath) {
        this.selectedUseCaseIdentifier = agentData.organizationPath;
      } else {
        const orgPath = this.buildOrganizationPath();
        this.selectedUseCaseIdentifier = `${this.selectedAgentMode}${orgPath}`;
      }
    }

    this.promptOptions.unshift({ value: 'default', name: 'Choose an Agent' });
    this.cdr.detectChanges();
  }

  private autoSelectCurrentAgent(): void {
    if (!this.promptOptions?.length || !this.agentName) return;

    let currentAgentOption = this.promptOptions.find(
      (option) =>
        option.name === this.agentName ||
        option.name.toLowerCase() === this.agentName.toLowerCase(),
    );

    if (!currentAgentOption) {
      const agentValue = this.agentName.toUpperCase().replace(/\s+/g, '');
      currentAgentOption = this.promptOptions.find(
        (option) =>
          option.value.toString() === agentValue ||
          option.value.toString() === this.currentAgentId,
      );
    }

    if (!currentAgentOption) {
      currentAgentOption = {
        value: this.agentName.toUpperCase().replace(/\s+/g, ''),
        name: this.agentName,
      };
    }

    this.selectedPrompt = currentAgentOption.value.toString();
    this.onPromptChanged(currentAgentOption);
    this.cdr.detectChanges();
  }

  onPromptChanged(selectedOption: DropdownOption): void {
    const agentData = (selectedOption as any).agentData;
    const agentCode = agentData?.code || selectedOption.value.toString();

    this.selectedPrompt = selectedOption.name;
    this.agentCode = agentCode;
    this.selectedAgentMode = agentCode;

    if (agentData?.useCaseIdentifier) {
      this.selectedUseCaseIdentifier = agentData.useCaseIdentifier;
    } else {
      this.selectedUseCaseIdentifier = `${agentCode}${this.buildOrganizationPath()}`;
    }

    this.cdr.detectChanges();
  }

  // Exit execute mode
  onExitExecuteMode(): void {
    this.isExecuteMode = false;
    this.showChatInterface = false;

    // Clear execute nodes and restore original canvas nodes
    this.executeNodes = [];
    this.canvasNodes = this.buildAgentNodes.map((node) => ({
      id: node.id,
      type: 'build-agent',
      data: { ...node, width: 90 },
      position: node.position,
    }));

    // Recreate connections based on hierarchy
    this.createAgentFlowConnections(this.buildAgentNodes);

    // Recalculate node positions for normal mode (horizontal layout)
    this.recalculateAllNodePositions();

    // Force connection points update after a delay to ensure DOM is updated
    setTimeout(() => {
      this.cdr.detectChanges();
    }, 200);

    // Clean up execution subscription
    if (this.executionSubscription) {
      this.executionSubscription.unsubscribe();
      this.executionSubscription = undefined;
    }

    // Stop execution service
    this.toolExecutionService.stopExecution();
  }

  onCanvasStateChanged(state: {
    nodes: CanvasNode[];
    edges: CanvasEdge[];
  }): void {
    this.canvasNodes = state.nodes;
    this.canvasEdges = state.edges;

    // Sync build agent nodes with canvas nodes
    this.buildAgentNodes = state.nodes.map((canvasNode) => ({
      id: canvasNode.id,
      name: canvasNode.data.name || canvasNode.data.label,
      type: canvasNode.data.type,
      icon: canvasNode.data.icon,
      position: canvasNode.position,
      originalToolData: canvasNode.data.originalToolData,
    }));

    // Ensure connection points are updated when state changes
    setTimeout(() => {
      if (this.canvasBoardComponent) {
        this.canvasBoardComponent.updateNodeConnectionPoints();
      }
    }, 100);
  }

  // Handle agent name changes from canvas board
  onAgentNameChanged(agentName: string): void {
    this.agentName = agentName;
  }

  // Handle metadata changes from canvas board
  onMetadataChanged(metadata: {
    org: string;
    domain: string;
    project: string;
    team: string;
  }): void {
    this.agentMetadata = metadata;
  }

  // Handle agent details changes from canvas board
  onAgentDetailsChanged(details: {
    name: string;
    useCaseDetails: string;
  }): void {
    this.agentName = details.name;
    this.agentDetail = details.useCaseDetails;
  }

  // Get active tab label for display
  getActiveTabLabel(): string {
    const activeTabObj = this.tabs.find((tab) => tab.id === this.activeTab);
    return activeTabObj ? activeTabObj.label : 'Item';
  }

  // Create new item action (dynamic based on active tab)
  onCreateNewItem(): void {
    // Navigate to appropriate creation page based on active tab
    switch (this.activeTab) {
      case 'prompts':
        this.router.navigate(['/libraries/prompts/create']);
        break;
      case 'models':
        this.router.navigate(['/libraries/models/create']);
        break;
      case 'knowledge':
        this.router.navigate(['/libraries/knowledge-base/create']);
        break;
      case 'tools':
        this.router.navigate(['/libraries/tools/create']);
        break;
      case 'guardrails':
        this.router.navigate(['/libraries/guardrails/create']);
        break;
      default:
        console.warn(`No creation route defined for tab: ${this.activeTab}`);
    }
  }

  // Calculate automatic position based on hierarchy and node type
  private calculateAutoPosition(nodeType: string): { x: number; y: number } {
    if (this.isExecuteMode) {
      // Execute mode - center nodes in the middle of the canvas
      const centerX = 75; // Center position for the canvas (adjust this value to center perfectly)
      const startY = 80; // Start position from top
      const verticalSpacing = 70; // Uniform spacing between nodes

      // Calculate position based on current number of nodes
      const nodeIndex = this.buildAgentNodes.length;
      return {
        x: centerX,
        y: startY + nodeIndex * verticalSpacing,
      };
    }

    // Build mode - Fixed predictable positioning based on hierarchy
    return this.calculateHierarchyBasedPosition(nodeType);
  }

  // Calculate position based on node hierarchy and existing nodes
  private calculateHierarchyBasedPosition(nodeType: string): {
    x: number;
    y: number;
  } {
    const startX = 300; // Fixed starting X position - increased significantly to ensure visibility
    const startY = 350; // Fixed starting Y position - moved further down
    const nodeSpacing = 140; // Space between nodes - reduced for tighter layout

    // Define the flow order: prompt → model → knowledge → guardrail → tool
    const flowOrder = ['prompt', 'model', 'knowledge', 'guardrail', 'tool'];

    // Get current hierarchy position for this node type
    const hierarchyIndex = flowOrder.indexOf(nodeType);

    // Count existing nodes of each type to determine position within type
    const nodesByType: { [key: string]: number } = {};
    this.buildAgentNodes.forEach((node) => {
      nodesByType[node.type] = (nodesByType[node.type] || 0) + 1;
    });

    // Calculate position based on hierarchy and count within type
    const typeCount = nodesByType[nodeType] || 0;

    // Position calculation: first node at start, second to right, third below second, etc.
    let totalNodesBeforeThisType = 0;
    for (let i = 0; i < hierarchyIndex; i++) {
      totalNodesBeforeThisType += nodesByType[flowOrder[i]] || 0;
    }

    const absoluteIndex = totalNodesBeforeThisType + typeCount - 1; // Subtract 1 since we're calculating for the current node

    // Use the same pattern as before but based on hierarchy position
    if (absoluteIndex === 0) {
      // First node - top left
      return { x: startX, y: startY };
    } else if (absoluteIndex === 1) {
      // Second node - to the right of first
      return { x: startX + nodeSpacing, y: startY };
    } else if (absoluteIndex === 2) {
      // Third node - below second
      return { x: startX + nodeSpacing, y: startY + nodeSpacing };
    } else if (absoluteIndex === 3) {
      // Fourth node - to the right of third
      return { x: startX + nodeSpacing * 2, y: startY + nodeSpacing };
    } else if (absoluteIndex === 4) {
      // Fifth node - below fourth
      return { x: startX + nodeSpacing * 2, y: startY + nodeSpacing * 2 };
    } else {
      // Additional nodes - continue in a grid pattern
      const row = Math.floor(absoluteIndex / 3);
      const col = absoluteIndex % 3;
      return {
        x: startX + col * nodeSpacing,
        y: startY + row * nodeSpacing,
      };
    }
  }

  // Simple method to create a connection between two nodes
  private createConnection(sourceId: string, targetId: string): void {
    // Validate that both nodes exist
    const sourceNode = this.buildAgentNodes.find(
      (node) => node.id === sourceId,
    );
    const targetNode = this.buildAgentNodes.find(
      (node) => node.id === targetId,
    );

    if (!sourceNode || !targetNode) {
      console.warn(
        `Cannot create connection: source or target node not found. Source: ${sourceId}, Target: ${targetId}`,
      );
      return;
    }

    // Check if connection already exists
    const existingConnection = this.canvasEdges.find(
      (edge) => edge.source === sourceId && edge.target === targetId,
    );

    if (existingConnection) {
      return; // Connection already exists
    }

    const newEdge: CanvasEdge = {
      id: `edge_${sourceId}_${targetId}`,
      source: sourceId,
      target: targetId,
      animated: false, // Simple static connections
    };

    this.canvasEdges = [...this.canvasEdges, newEdge];
  }

  // Create connections between nodes in the proper agent flow order
  private createAgentFlowConnections(nodes: BuildAgentNodeData[]): void {
    if (nodes.length < 2) return; // Need at least 2 nodes to create connections

    // Clear existing edges first
    this.canvasEdges = [];

    // Define the flow order: prompt → model → knowledge → guardrails → tools
    const flowOrder = ['prompt', 'model', 'knowledge', 'guardrail', 'tool'];

    // Group nodes by type
    const nodesByType: { [key: string]: BuildAgentNodeData[] } = {};
    nodes.forEach((node) => {
      if (!nodesByType[node.type]) {
        nodesByType[node.type] = [];
      }
      nodesByType[node.type].push(node);
    });

    // Create connections following the flow order with proper hierarchy
    let lastNodeOfPreviousType: BuildAgentNodeData | null = null;

    flowOrder.forEach((nodeType) => {
      const currentNodes = nodesByType[nodeType] || [];

      if (currentNodes.length > 0) {
        // Connect the last node of the previous type to the first node of current type
        if (lastNodeOfPreviousType) {
          this.createConnection(lastNodeOfPreviousType.id, currentNodes[0].id);
        }

        // If there are multiple nodes of the same type, connect them in sequence
        for (let i = 0; i < currentNodes.length - 1; i++) {
          this.createConnection(currentNodes[i].id, currentNodes[i + 1].id);
        }

        // Update the last node to be the last node of current type
        lastNodeOfPreviousType = currentNodes[currentNodes.length - 1];
      }
    });

    // Force change detection to ensure edges are updated
    this.cdr.detectChanges();
  }

  // Create consolidated execute nodes from build agent nodes
  private createExecuteNodes(): void {
    const nodesByType: { [key: string]: BuildAgentNodeData[] } = {
      prompt: [],
      model: [],
      knowledge: [],
      tool: [],
      guardrail: [],
    };

    // Group nodes by type
    this.buildAgentNodes.forEach((node) => {
      if (nodesByType[node.type]) {
        nodesByType[node.type].push(node);
      }
    });

    // Create execute nodes for types that have nodes
    this.executeNodes = [];
    const nodeTypes = ['prompt', 'model', 'knowledge', 'tool', 'guardrail'];
    const centerX = 10; // Center position for the canvas
    const startY = 150;
    const verticalSpacing = 70;

    let nodeIndex = 0;
    nodeTypes.forEach((type) => {
      if (nodesByType[type].length > 0) {
        this.executeNodes.push({
          type: type as 'prompt' | 'model' | 'knowledge' | 'tool' | 'guardrail',
          nodes: nodesByType[type],
          position: {
            x: centerX,
            y: startY + nodeIndex * verticalSpacing,
          },
        });
        nodeIndex++;
      }
    });

    // Update canvas nodes for execute mode
    this.canvasNodes = this.executeNodes.map((executeNode) => ({
      id: `execute-${executeNode.type}`,
      type: 'build-agent',
      data: {
        id: `execute-${executeNode.type}`,
        type: executeNode.type,
        name: executeNode.type,
        position: executeNode.position,
        width: 55,
      },
      position: executeNode.position,
    }));

    // Create connections for execute mode (simple vertical flow)
    this.canvasEdges = [];
    for (let i = 0; i < this.executeNodes.length - 1; i++) {
      this.canvasEdges.push({
        id: `execute-edge-${i}`,
        source: `execute-${this.executeNodes[i].type}`,
        target: `execute-${this.executeNodes[i + 1].type}`,
      });
    }
  }

  // Recalculate all node positions (used when switching between normal and execute modes)
  private recalculateAllNodePositions(): void {
    if (this.isExecuteMode) {
      // In execute mode, create consolidated nodes
      this.createExecuteNodes();
      return;
    }

    const nodeWidth = 90; // Build mode node width

    // Force recalculation of all node positions with proper indexing for build mode
    for (let i = 0; i < this.buildAgentNodes.length; i++) {
      // In build mode, use the same predictable positioning pattern
      const startX = 300; // Fixed starting X position - increased significantly to ensure visibility
      const startY = 250; // Fixed starting Y position - also increased
      const nodeSpacing = 140;

      let newPosition: { x: number; y: number };

      if (i === 0) {
        // First node - top left
        newPosition = { x: startX, y: startY };
      } else if (i === 1) {
        // Second node - to the right of first
        newPosition = { x: startX + nodeSpacing, y: startY };
      } else if (i === 2) {
        // Third node - below second
        newPosition = { x: startX + nodeSpacing, y: startY + nodeSpacing };
      } else if (i === 3) {
        // Fourth node - to the right of third
        newPosition = { x: startX + nodeSpacing * 2, y: startY + nodeSpacing };
      } else if (i === 4) {
        // Fifth node - below fourth
        newPosition = {
          x: startX + nodeSpacing * 2,
          y: startY + nodeSpacing * 2,
        };
      } else {
        // Additional nodes - continue in a grid pattern
        const row = Math.floor(i / 3);
        const col = i % 3;
        newPosition = {
          x: startX + col * nodeSpacing,
          y: startY + row * nodeSpacing,
        };
      }

      this.buildAgentNodes[i].position = newPosition;
    }

    // Update canvas nodes to match
    for (let i = 0; i < this.canvasNodes.length; i++) {
      const canvasNode = this.canvasNodes[i];
      const buildAgentNode = this.buildAgentNodes.find(
        (n) => n.id === canvasNode.id,
      );
      if (buildAgentNode) {
        this.canvasNodes[i].position = buildAgentNode.position;
        this.canvasNodes[i].data.position = buildAgentNode.position;
        this.canvasNodes[i].data.width = nodeWidth; // Update node width
      }
    }

    // Force change detection with a slight delay to ensure smooth transition
    setTimeout(() => {
      this.canvasNodes = [...this.canvasNodes];
      this.buildAgentNodes = [...this.buildAgentNodes];
      this.cdr.detectChanges();
    }, 100);
  }

  // Get execute node data for a given canvas node
  getExecuteNodeData(node: CanvasNode): ExecuteNodeData | undefined {
    if (!this.isExecuteMode) return undefined;

    const nodeType = node.id.replace('execute-', '');
    return this.executeNodes.find(
      (executeNode) => executeNode.type === nodeType,
    );
  }

  // Generate a unique node ID - similar to workflow editor
  generateNodeId(): string {
    return `node_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
  }

  // Load agent data from catalogue
  private loadAgentData(agentId: string): void {
    // For collaborative agents, don't load labels API - proceed directly
    if (this.currentAgentType === 'collaborative') {
      this.loadAgentDataAfterLabels(agentId);
      return;
    }

    // For individual agents, ensure labels data is loaded first
    if (!this.labelsCache) {
      this.agentService.getLabels().subscribe({
        next: (response: any) => {
          this.labelsCache = response;

          // Load all data from cached labels
          this.loadModelsFromCache();
          this.loadKnowledgeBaseFromCache();
          this.loadGuardrailsFromCache();

          // Now load the agent data
          this.loadAgentDataAfterLabels(agentId);
        },
        error: (error) => {
          console.error('Error loading labels for agent mapping:', error);
          // Proceed anyway with existing data
          this.loadAgentDataAfterLabels(agentId);
        },
      });
      return;
    }

    this.loadAgentDataAfterLabels(agentId);
  }

  // Load agent data after ensuring labels are loaded
  private loadAgentDataAfterLabels(agentId: string): void {
    if (this.currentAgentType === 'collaborative') {
      this.agentService.getCollaborativeAgentDetailsById(agentId).subscribe({
        next: (response) => {
          // Handle the new API response format
          let agentData = null;
          if (response && response.agentDetail) {
            // New API format: data is in agentDetail property
            agentData = response.agentDetail;
          } else if (
            response &&
            (response.agentDetails || response.agentDetail)
          ) {
            // Fallback to old format
            agentData = response.agentDetails || response.agentDetail;
          } else if (response && response.data) {
            // Another possible format
            agentData = response.data;
          }

          if (agentData) {
            // Store agent details for playground display
            this.currentAgentDetails = {
              name: agentData.name || 'Unnamed Agent',
              description:
                agentData.description || agentData.agentDetails || '',
              role: agentData.role || '',
              goal: agentData.goal || '',
              backstory: agentData.backstory || '',
              expectedOutput: agentData.expectedOutput || '',
            };

            // Create a response object in the expected format for mapping
            const mappedResponse = {
              agentDetail: agentData,
              agentDetails: agentData, // Keep both for compatibility
            };
            this.mapCollaborativeAgentDataToCanvas(mappedResponse);
          } else {
            console.warn(
              'No collaborative agent data found in response:',
              response,
            );
            this.showErrorMessage(
              'Agent Not Found',
              'No agent data found. The agent might not exist or you might not have permission to view it.',
            );
          }
        },
        error: (error) => {
          console.error('Error loading collaborative agent data:', error);
          this.showErrorMessage(
            'Load Failed',
            'Failed to load collaborative agent data. Please check the console for details.',
          );
        },
      });
    } else {
      this.agentService.getAgentById(agentId).subscribe({
        next: (response) => {
          if (response && (response.useCaseName || response.name)) {
            // Store agent details for playground display with all necessary data
            this.currentAgentDetails = {
              name: response.name || response.useCaseName || 'Unnamed Agent',
              description:
                response.description || response.useCaseDescription || '',
              role: response.role || '',
              goal: response.goal || '',
              backstory: response.backstory || '',
              expectedOutput: response.expectedOutput || '',
              // Add playground-specific data from the API response
              useCaseCode: response.useCaseCode || response.code || '',
              useCaseName: response.useCaseName || response.name || '',
              useCaseId: response.useCaseId || response.id || '',
              organizationPath: response.organizationPath || '',
              // Extract config data for mode and useCaseIdentifier
              config: response.config || [],
            };

            this.mapAgentDataToCanvas(response);
          } else {
            console.warn(
              'No individual agent data found in response:',
              response,
            );
            this.showErrorMessage(
              'Agent Not Found',
              'No agent data found. The agent might not exist or you might not have permission to view it.',
            );
          }
        },
        error: (error) => {
          console.error('Error loading individual agent data:', error);
          this.showErrorMessage(
            'Load Failed',
            'Failed to load individual agent data. Please check the console for details.',
          );
        },
      });
    }
  }

  // Map agent data to canvas board
  private mapAgentDataToCanvas(agentData: any): void {
    if (!agentData) {
      console.error('No individual agent data provided');
      return;
    }

    // Set basic agent information using the actual API response structure
    // In duplicate mode, clear the name field so user can enter a new name
    if (this.isDuplicateMode) {
      this.agentName = ''; // Clear name for duplicate mode
    } else {
      if (agentData.useCaseName) {
        this.agentName = agentData.useCaseName;
      } else if (agentData.name) {
        this.agentName = agentData.name;
      }
    }

    // Extract agent code for API calls
    if (agentData.useCaseCode) {
      this.agentCode = agentData.useCaseCode;
    } else if (agentData.code) {
      this.agentCode = agentData.code;
    } else {
      // Fallback: generate code from agent name
      this.agentCode = this.agentName.trim().replace(/\s+/g, '_').toUpperCase();
    }

    if (agentData.useCaseDetails) {
      this.agentDetail = agentData.useCaseDetails;
    } else if (agentData.description) {
      this.agentDetail = agentData.description;
    } else if (agentData.agentDetail) {
      this.agentDetail = agentData.agentDetail;
    }

    // Set metadata from the API response
    if (agentData.organizationPath) {
      const pathParts = agentData.organizationPath.split('@');
      if (pathParts.length >= 5) {
        this.agentMetadata = {
          org: pathParts[1] || agentData.org || '',
          domain: pathParts[2] || agentData.domain || '',
          project: pathParts[3] || agentData.project || '',
          team: pathParts[4] || agentData.team || '',
        };
      }
    } else {
      this.agentMetadata = {
        org: agentData.org || '',
        domain: agentData.domain || '',
        project: agentData.project || '',
        team: agentData.team || '',
      };
    }

    // Map individual agent data to canvas nodes based on config array
    // Use a temporary array to collect nodes in hierarchy order
    const nodesByType: { [key: string]: BuildAgentNodeData[] } = {
      prompt: [],
      model: [],
      knowledge: [],
      guardrail: [],
      tool: [],
    };
    let nodeCounter = 1;

    if (agentData.prompt || agentData.useCaseDetails || agentData.useCaseName) {
      let promptName = agentData.prompt || 'Default Prompt';

      // Try to find the exact prompt by name from the loaded prompts
      let promptData = this.allToolItems['prompts']?.find(
        (p) =>
          p.name === promptName ||
          p.name.toLowerCase() === promptName.toLowerCase(),
      );

      // If not found by name, use the first available prompt as fallback
      if (!promptData && this.allToolItems['prompts']?.length > 0) {
        promptData = this.allToolItems['prompts'][0];
        promptName = promptData.name; // Use the actual prompt name from the data
      }

      // Always create a prompt node (essential for individual agents)
      const promptNode: BuildAgentNodeData = {
        id: `prompt-${nodeCounter++}`,
        name: promptName,
        type: 'prompt',
        position: { x: 0, y: 0 }, // Will be calculated later
      };
      nodesByType['prompt'].push(promptNode);
    }

    // Process the config array to extract model, tools, knowledge bases, etc.
    if (agentData.config && Array.isArray(agentData.config)) {
      agentData.config.forEach((category: any) => {
        if (category.config && Array.isArray(category.config)) {
          category.config.forEach((configItem: any) => {
            // Store configId for update operations
            if (configItem.configId) {
              const configKey = `${category.categoryId}-${configItem.configKey}`;
              this.agentConfigIds.set(configKey, configItem.configId);
            }
            // Handle MODEL configuration
            if (configItem.configKey === 'MODEL' && configItem.configValue) {
              const modelId = configItem.configValue;

              const modelData = this.allToolItems['models']?.find(
                (m) =>
                  m.id === modelId ||
                  m.id === modelId.toString() ||
                  m.id.toString() === modelId,
              );

              if (modelData) {
                nodesByType['model'].push({
                  id: `model-${nodeCounter++}`,
                  type: 'model',
                  name: modelData.name,
                  icon: undefined, // Let node component use Lucide icon
                  position: { x: 0, y: 0 }, // Will be calculated later
                  originalToolData: modelData, // Store the original tool data for ID retrieval
                });
              } else {
                // Create a placeholder model node if not found
                nodesByType['model'].push({
                  id: `model-${nodeCounter++}`,
                  type: 'model',
                  name: `Model ID: ${modelId}`,
                  icon: undefined, // Let node component use Lucide icon
                  position: { x: 0, y: 0 }, // Will be calculated later
                  originalToolData: {
                    id: modelId,
                    name: `Model ID: ${modelId}`,
                  }, // Store the ID for retrieval
                });
              }
            }

            // Handle RAG_KNOWLEDGEBASE_NAME configuration (can contain comma-separated IDs)
            if (
              configItem.configKey === 'RAG_KNOWLEDGEBASE_NAME' &&
              configItem.configValue
            ) {
              const kbValue = configItem.configValue.toString(); // Ensure it's a string
              const kbIds = kbValue
                .split(',')
                .map((id: string) => id.trim())
                .filter((id: string) => id); // Split by comma and clean

              // Process each knowledge base ID separately to create individual nodes
              kbIds.forEach((kbId: string) => {
                const knowledgeData = this.allToolItems['knowledge']?.find(
                  (k) => {
                    return (
                      k.id === kbId ||
                      k.id === kbId.toString() ||
                      k.id.toString() === kbId ||
                      k.name === kbId ||
                      k.name.toLowerCase() === kbId.toLowerCase()
                    );
                  },
                );

                if (knowledgeData) {
                  nodesByType['knowledge'].push({
                    id: `knowledge-${nodeCounter++}`,
                    type: 'knowledge',
                    name: knowledgeData.name,
                    icon: undefined, // Let node component use Lucide icon
                    position: { x: 0, y: 0 }, // Will be calculated later
                    originalToolData: knowledgeData, // Store the original tool data for ID retrieval
                  });
                } else {
                  // Create a placeholder knowledge node if not found in labels
                  // Try to get the name from labels API directly
                  let kbName = `Knowledge Base ID: ${kbId}`;
                  if (this.labelsCache && this.labelsCache.categoryLabels) {
                    const iclCategory = this.labelsCache.categoryLabels.find(
                      (cat: any) => cat.categoryId === 2,
                    );
                    if (iclCategory) {
                      const kbLabel = iclCategory.labels.find(
                        (label: any) =>
                          label.labelCode === 'RAG_KNOWLEDGEBASE_NAME',
                      );
                      if (kbLabel && kbLabel.labelValues) {
                        const kbOptions = this.agentService.parseLabelValues(
                          kbLabel.labelValues,
                        );
                        const kbOption = kbOptions.find(
                          (opt) => opt.value === kbId,
                        );
                        if (kbOption) {
                          kbName = kbOption.name;
                        }
                      }
                    }
                  }

                  nodesByType['knowledge'].push({
                    id: `knowledge-${nodeCounter++}`,
                    type: 'knowledge',
                    name: kbName,
                    icon: undefined, // Let node component use Lucide icon
                    position: { x: 0, y: 0 }, // Will be calculated later
                    originalToolData: { id: kbId, name: kbName }, // Store the ID for retrieval
                  });
                }
              });
            }

            // Handle GUARDRAIL configurations (only show enabled guardrails)
            if (
              configItem.configKey &&
              configItem.configKey.startsWith('GUARDRAIL_') &&
              (configItem.configValue === 'true' ||
                configItem.configValue === true)
            ) {
              const guardrailData = this.allToolItems['guardrails']?.find(
                (g) => (g as any).code === configItem.configKey,
              );

              if (guardrailData) {
                nodesByType['guardrail'].push({
                  id: `guardrail-${nodeCounter++}`,
                  type: 'guardrail',
                  name: guardrailData.name,
                  icon: undefined, // Let node component use Lucide icon
                  position: { x: 0, y: 0 }, // Will be calculated later
                });
              } else {
                // Create placeholder guardrail node
                const guardrailName = configItem.configKey
                  .replace('GUARDRAIL_', '')
                  .replace(/_/g, ' ');
                nodesByType['guardrail'].push({
                  id: `guardrail-${nodeCounter++}`,
                  type: 'guardrail',
                  name: guardrailName,
                  icon: undefined, // Let node component use Lucide icon
                  position: { x: 0, y: 0 }, // Will be calculated later
                });
              }
            }
          });
        }
      });
    }

    // Assemble nodes in hierarchy order and calculate positions
    const nodes: BuildAgentNodeData[] = [];
    const flowOrder = ['prompt', 'model', 'knowledge', 'guardrail', 'tool'];

    // Temporarily set buildAgentNodes to empty array for position calculation
    this.buildAgentNodes = [];

    flowOrder.forEach((nodeType) => {
      nodesByType[nodeType].forEach((node) => {
        // Calculate position based on current hierarchy
        node.position = this.calculateHierarchyBasedPosition(nodeType);
        nodes.push(node);
        // Add to buildAgentNodes for next position calculation
        this.buildAgentNodes.push(node);
      });
    });

    // Update build agent nodes with final array
    this.buildAgentNodes = nodes;

    // Update canvas nodes for display
    this.canvasNodes = nodes.map((node) => ({
      id: node.id,
      type: 'build-agent',
      data: { ...node, width: this.isExecuteMode ? 55 : 120 },
      position: node.position,
    }));

    // Update canvas board with the loaded data first
    this.cdr.detectChanges();

    // Wait for DOM to update, then create connections
    setTimeout(() => {
      // Clear existing edges when loading new agent data
      this.canvasEdges = [];

      // Create connections between nodes in the proper flow order
      this.createAgentFlowConnections(nodes);

      // Force update of connection points after nodes are positioned
      if (this.canvasBoardComponent) {
        this.canvasBoardComponent.updateNodeConnectionPoints();
      }

      // Trigger change detection again to ensure connections are rendered
      this.cdr.detectChanges();
    }, 200);

    // In duplicate mode, clear the agent ID after loading data to ensure new agent creation
    if (this.isDuplicateMode) {
      this.currentAgentId = null;
    }

    // If in execute mode, automatically initialize playground
    if (this.isExecuteMode) {
      // Auto-select this agent in the dropdown when execute mode loads
      this.autoSelectCurrentAgentInDropdown();
      setTimeout(() => {
        this.initializeExecuteMode();
      }, 500);
    }

    // If in edit mode, ensure connections are properly established
    if (this.isEditMode) {
      setTimeout(() => {
        this.ensureConnectionsInEditMode();
      }, 500);
    }
  }

  // Map collaborative agent data to canvas board
  private mapCollaborativeAgentDataToCanvas(response: any): void {
    // Handle different response structures
    let agentData;
    if (
      response.agentDetails &&
      Array.isArray(response.agentDetails) &&
      response.agentDetails.length > 0
    ) {
      agentData = response.agentDetails[0];
    } else if (response.agentDetail) {
      agentData = response.agentDetail;
    } else if (response.data) {
      agentData = response.data;
    } else {
      agentData = response;
    }

    if (!agentData) {
      console.error('No agent data found in response');
      return;
    }

    // Set basic agent information
    // In duplicate mode, clear the name field so user can enter a new name
    if (this.isDuplicateMode) {
      this.agentName = ''; // Clear name for duplicate mode
    } else {
      if (agentData.name) {
        this.agentName = agentData.name;
      }
    }

    // Extract agent code for API calls
    if (agentData.code) {
      this.agentCode = agentData.code;
    } else if (agentData.useCaseCode) {
      this.agentCode = agentData.useCaseCode;
    } else {
      // Fallback: generate code from agent name
      this.agentCode = this.agentName.trim().replace(/\s+/g, '_').toUpperCase();
    }

    // Set agent details - check multiple possible field names
    if (agentData.agentDetails) {
      this.agentDetail = agentData.agentDetails;
    } else if (agentData.agentDetail) {
      this.agentDetail = agentData.agentDetail;
    } else if (agentData.description) {
      this.agentDetail = agentData.description;
    }

    // Set metadata from the API response
    this.agentMetadata = {
      org: agentData.org || '',
      domain: agentData.domain || '',
      project: agentData.project || '',
      team: agentData.team || '',
    };

    // Map collaborative agent data to canvas nodes
    // Use a temporary array to collect nodes in hierarchy order
    const nodesByType: { [key: string]: BuildAgentNodeData[] } = {
      prompt: [],
      model: [],
      knowledge: [],
      guardrail: [],
      tool: [],
    };
    let nodeCounter = 1;

    // Add prompt node first - handle different prompt structures for different agent types
    const shouldCreatePromptNode =
      (this.currentAgentType === 'individual' && agentData.prompt) ||
      (this.currentAgentType === 'collaborative' &&
        (agentData.goal || agentData.role || agentData.description));

    if (shouldCreatePromptNode) {
      // Get a fallback prompt data for icon and structure
      let promptData =
        this.allToolItems['prompts']?.find(
          (p) => (p as any).promptType === 'zero shot',
        ) || this.allToolItems['prompts']?.[0];

      // Create a default prompt data if none found
      if (!promptData) {
        promptData = {
          id: 'default-prompt',
          name: 'Default Prompt',
          type: 'prompt',
          icon: this.getIconForType('prompt'),
          description: 'Default prompt',
        };
      }

      // Determine prompt node name and data based on agent type
      let promptNodeName;
      let promptNodeData;

      if (this.currentAgentType === 'collaborative') {
        // For collaborative agents: use goal as primary display, store all prompt-related fields
        promptNodeName =
          agentData.goal ||
          agentData.role ||
          agentData.description ||
          'Collaborative Agent Prompt';

        // Truncate goal if too long for display
        if (promptNodeName.length > 150) {
          promptNodeName = promptNodeName.substring(0, 150) + '...';
        }

        promptNodeData = {
          ...promptData,
          // Store all collaborative agent prompt fields
          goal: agentData.goal,
          role: agentData.role,
          description: agentData.description,
          expectedOutput: agentData.expectedOutput,
          backstory: agentData.backstory,
          agentType: 'collaborative',
        };
      } else {
        // For individual agents: use prompt field, store the full prompt text
        promptNodeName = agentData.prompt || 'Individual Agent Prompt';

        // Truncate prompt if too long for display
        if (promptNodeName.length > 150) {
          promptNodeName = promptNodeName.substring(0, 150) + '...';
        }

        promptNodeData = {
          ...promptData,
          // Store the full prompt text for individual agents
          prompt: agentData.prompt,
          fullPromptText: agentData.prompt,
          agentType: 'individual',
        };
      }

      const promptNode: BuildAgentNodeData = {
        id: `prompt-${nodeCounter++}`,
        name: promptNodeName,
        type: 'prompt',
        icon: promptData.icon || this.getIconForType('prompt'),
        position: { x: 0, y: 0 }, // Will be calculated later
        originalToolData: promptNodeData,
      };

      nodesByType['prompt'].push(promptNode);
    } else if (agentData.agentDetail || agentData.name) {
      // Fallback: Create a default prompt node if no prompt data found
      const promptData =
        this.allToolItems['prompts']?.find(
          (p) => (p as any).promptType === 'zero shot',
        ) || this.allToolItems['prompts']?.[0];

      if (promptData) {
        let promptNodeName;
        let fallbackPromptData;

        if (this.currentAgentType === 'collaborative') {
          // For collaborative agents, try to use available fields or fallback to prompt name
          promptNodeName =
            agentData.goal ||
            agentData.role ||
            agentData.description ||
            promptData.name;
          fallbackPromptData = {
            ...promptData,
            goal: agentData.goal,
            role: agentData.role,
            description: agentData.description,
            expectedOutput: agentData.expectedOutput,
            agentType: 'collaborative',
          };
        } else {
          // For individual agents, use prompt field or fallback to prompt name
          promptNodeName = agentData.prompt || promptData.name;
          fallbackPromptData = {
            ...promptData,
            prompt: agentData.prompt,
            fullPromptText: agentData.prompt,
            agentType: 'individual',
          };
        }

        const promptNode: BuildAgentNodeData = {
          id: `prompt-${nodeCounter++}`,
          name: promptNodeName,
          type: 'prompt',
          icon: promptData.icon || this.getIconForType('prompt'),
          position: { x: 0, y: 0 }, // Will be calculated later
          originalToolData: fallbackPromptData,
        };
        nodesByType['prompt'].push(promptNode);
      }
    }

    // Add model nodes - handle both old and new API formats
    let modelReferences = [];

    // New API format: agentConfigs.modelRef (array of model IDs or objects)
    if (agentData.agentConfigs && agentData.agentConfigs.modelRef) {
      const modelRefs = Array.isArray(agentData.agentConfigs.modelRef)
        ? agentData.agentConfigs.modelRef
        : [agentData.agentConfigs.modelRef];

      // Handle both ID arrays and object arrays
      modelReferences = modelRefs.map((ref: any) => {
        if (typeof ref === 'number' || typeof ref === 'string') {
          return { modelId: ref };
        }
        return ref;
      });
    }
    // Old API format: modelDetails
    else if (agentData.modelDetails) {
      modelReferences = [agentData.modelDetails];
    }

    modelReferences.forEach((modelRef: any) => {
      const modelId = modelRef.modelId || modelRef.id;

      const modelData = this.allToolItems['models']?.find(
        (m) =>
          m.id === modelId ||
          m.id === modelId.toString() ||
          m.id.toString() === modelId,
      );

      if (modelData) {
        nodesByType['model'].push({
          id: `model-${nodeCounter++}`,
          type: 'model',
          name: modelData.name,
          icon: undefined, // Let node component use Lucide icon
          position: { x: 0, y: 0 }, // Will be calculated later
          originalToolData: modelData, // Store original data for retrieval
        });
      } else {
        // Create a placeholder model node with the model name from API
        const modelName =
          modelRef.model ||
          modelRef.modelDeploymentName ||
          `Model ID: ${modelId}`;
        nodesByType['model'].push({
          id: `model-${nodeCounter++}`,
          type: 'model',
          name: modelName,
          icon: undefined, // Let node component use Lucide icon
          position: { x: 0, y: 0 }, // Will be calculated later
          originalToolData: { id: modelId, name: modelName }, // Store for retrieval
        });
      }
    });

    // Add knowledge base nodes - handle both old and new API formats
    let knowledgeReferences = [];

    // New API format: agentConfigs.knowledgeBaseRef (array of KB IDs or objects)
    if (agentData.agentConfigs && agentData.agentConfigs.knowledgeBaseRef) {
      const kbRefs = Array.isArray(agentData.agentConfigs.knowledgeBaseRef)
        ? agentData.agentConfigs.knowledgeBaseRef
        : [agentData.agentConfigs.knowledgeBaseRef];

      // Handle both ID arrays and object arrays
      knowledgeReferences = kbRefs.map((ref: any) => {
        if (typeof ref === 'number' || typeof ref === 'string') {
          return { knowledgeBaseId: ref };
        }
        return ref;
      });
    }
    // Old API format: indexCollectionName
    else if (
      agentData.indexCollectionName &&
      Array.isArray(agentData.indexCollectionName)
    ) {
      knowledgeReferences = agentData.indexCollectionName.map(
        (name: string) => ({ indexCollectionName: name }),
      );
    }

    knowledgeReferences.forEach((kbRef: any) => {
      const kbId = kbRef.knowledgeBaseId || kbRef.id;
      const collectionName = kbRef.indexCollectionName || kbRef.name;

      // Try to find by ID first, then by name
      const knowledgeData = this.allToolItems['knowledge']?.find(
        (k) =>
          (kbId &&
            (k.id === kbId ||
              k.id === kbId.toString() ||
              k.id.toString() === kbId)) ||
          (collectionName &&
            (k.name === collectionName ||
              k.name.toLowerCase() === collectionName.toLowerCase())),
      );

      if (knowledgeData) {
        nodesByType['knowledge'].push({
          id: `knowledge-${nodeCounter++}`,
          type: 'knowledge',
          name: knowledgeData.name,
          icon: undefined, // Let node component use Lucide icon
          position: { x: 0, y: 0 }, // Will be calculated later
          originalToolData: knowledgeData, // Store original data for retrieval
        });
      } else {
        // Create a placeholder knowledge node
        const kbName = collectionName || `Knowledge Base ID: ${kbId}`;
        nodesByType['knowledge'].push({
          id: `knowledge-${nodeCounter++}`,
          type: 'knowledge',
          name: kbName,
          icon: undefined, // Let node component use Lucide icon
          position: { x: 0, y: 0 }, // Will be calculated later
          originalToolData: { id: kbId, name: kbName }, // Store for retrieval
        });
      }
    });

    // Add tool nodes - handle both old and new API formats
    let toolReferences = [];
    let userToolReferences = [];

    // New API format: agentConfigs.toolRef and agentConfigs.userToolRef (arrays of tool IDs or objects)
    if (agentData.agentConfigs) {
      if (agentData.agentConfigs.toolRef) {
        const toolRefs = Array.isArray(agentData.agentConfigs.toolRef)
          ? agentData.agentConfigs.toolRef
          : [agentData.agentConfigs.toolRef];

        // Handle both ID arrays and object arrays
        toolReferences = toolRefs.map((ref: any) => {
          if (typeof ref === 'number' || typeof ref === 'string') {
            return { toolId: ref };
          }
          return ref;
        });
      }
      if (agentData.agentConfigs.userToolRef) {
        const userToolRefs = Array.isArray(agentData.agentConfigs.userToolRef)
          ? agentData.agentConfigs.userToolRef
          : [agentData.agentConfigs.userToolRef];

        // Handle both ID arrays and object arrays
        userToolReferences = userToolRefs.map((ref: any) => {
          if (typeof ref === 'number' || typeof ref === 'string') {
            return { toolId: ref };
          }
          return ref;
        });
      }
    }
    // Old API format: tools and userTools
    else {
      if (agentData.tools && Array.isArray(agentData.tools)) {
        toolReferences = agentData.tools;
      }
      if (agentData.userTools && Array.isArray(agentData.userTools)) {
        userToolReferences = agentData.userTools;
      }
    }

    // Process built-in tools
    toolReferences.forEach((tool: any) => {
      const toolId = tool.toolId || tool.id;

      const toolData = this.allToolItems['tools']?.find(
        (t) =>
          t.id === `builtin-${toolId}` ||
          t.id === toolId ||
          t.id === toolId.toString() ||
          t.id.toString() === toolId,
      );

      if (toolData) {
        nodesByType['tool'].push({
          id: `tool-${nodeCounter++}`,
          type: 'tool',
          name: toolData.name,
          icon: undefined, // Let node component use Lucide icon
          position: { x: 0, y: 0 }, // Will be calculated later
          originalToolData: toolData, // Store original data for retrieval
        });
      } else {
        // Create a placeholder tool node with the tool name from API
        const toolName = tool.toolName || `Tool ID: ${toolId}`;
        nodesByType['tool'].push({
          id: `tool-${nodeCounter++}`,
          type: 'tool',
          name: toolName,
          icon: undefined, // Let node component use Lucide icon
          position: { x: 0, y: 0 }, // Will be calculated later
          originalToolData: { id: toolId, name: toolName }, // Store for retrieval
        });
      }
    });

    // Process user tools
    userToolReferences.forEach((userTool: any) => {
      const userToolId = userTool.toolId || userTool.id;

      // Look for user tools (they have 'user-' prefix in our system)
      const userToolData = this.allToolItems['tools']?.find(
        (t) =>
          t.id === `user-${userToolId}` ||
          t.id === userToolId ||
          t.id === userToolId.toString() ||
          t.id.toString() === userToolId,
      );

      if (userToolData) {
        nodesByType['tool'].push({
          id: `tool-${nodeCounter++}`,
          type: 'tool',
          name: userToolData.name,
          icon: undefined, // Let node component use Lucide icon
          position: { x: 0, y: 0 }, // Will be calculated later
          originalToolData: userToolData, // Store original data for retrieval
        });
      } else {
        // Create a placeholder user tool node
        const toolName = userTool.toolName || `User Tool ID: ${userToolId}`;
        nodesByType['tool'].push({
          id: `tool-${nodeCounter++}`,
          type: 'tool',
          name: toolName,
          icon: undefined, // Let node component use Lucide icon
          position: { x: 0, y: 0 }, // Will be calculated later
          originalToolData: { id: userToolId, name: toolName }, // Store for retrieval
        });
      }
    });

    // Add a default prompt node if none exists (collaborative agents should have prompts)
    if (nodesByType['prompt'].length === 0) {
      const defaultPrompt = this.allToolItems['prompts']?.find((p) => {
        const promptType = (p as any).type?.trim(); // Cast to any to access the prompt type property
        return promptType === 'zero shot';
      });
      if (defaultPrompt) {
        // For collaborative agents, use goal as the prompt node name, fallback to prompt data name
        const promptNodeName =
          this.currentAgentType === 'collaborative'
            ? agentData.goal || defaultPrompt.name
            : defaultPrompt.name;

        nodesByType['prompt'].push({
          id: `prompt-${nodeCounter++}`,
          type: 'prompt',
          name: promptNodeName,
          icon: defaultPrompt.icon || this.getIconForType('prompt'),
          position: { x: 0, y: 0 }, // Will be calculated later
          originalToolData: defaultPrompt, // Store original data for retrieval
        });
      }
    }

    // Assemble nodes in hierarchy order and calculate positions
    const nodes: BuildAgentNodeData[] = [];
    const flowOrder = ['prompt', 'model', 'knowledge', 'guardrail', 'tool'];

    // Temporarily set buildAgentNodes to empty array for position calculation
    this.buildAgentNodes = [];

    flowOrder.forEach((nodeType) => {
      nodesByType[nodeType].forEach((node) => {
        // Calculate position based on current hierarchy
        node.position = this.calculateHierarchyBasedPosition(nodeType);
        nodes.push(node);
        // Add to buildAgentNodes for next position calculation
        this.buildAgentNodes.push(node);
      });
    });

    // Update build agent nodes with final array
    this.buildAgentNodes = nodes;

    // Update canvas nodes for display
    this.canvasNodes = nodes.map((node) => ({
      id: node.id,
      type: 'build-agent',
      data: { ...node, width: this.isExecuteMode ? 55 : 120 },
      position: node.position,
    }));

    // Update canvas board with the loaded data first
    this.cdr.detectChanges();

    // Wait for DOM to update, then create connections
    setTimeout(() => {
      // Clear existing edges when loading new agent data
      this.canvasEdges = [];

      // Create connections between nodes in the proper flow order
      this.createAgentFlowConnections(nodes);

      // Force update of connection points after nodes are positioned
      if (this.canvasBoardComponent) {
        this.canvasBoardComponent.updateNodeConnectionPoints();
      }

      // Trigger change detection again to ensure connections are rendered
      this.cdr.detectChanges();
    }, 200);

    // In duplicate mode, clear the agent ID after loading data to ensure new agent creation
    if (this.isDuplicateMode) {
      this.currentAgentId = null;
    }

    // If in execute mode, automatically initialize playground
    if (this.isExecuteMode) {
      setTimeout(() => {
        this.initializeExecuteMode();
      }, 500);
    }

    // If in edit mode, ensure connections are properly established
    if (this.isEditMode) {
      setTimeout(() => {
        this.ensureConnectionsInEditMode();
      }, 500);
    }
  }

  // Simple method to auto-select current agent in dropdown
  private autoSelectCurrentAgentInDropdown(): void {
    if (!this.agentName) {
      return;
    }

    // Load agent details for playground
    this.loadAgentDetailsForPlayground();

    // Try to auto-select after a short delay
    setTimeout(() => {
      if (this.promptOptions && this.promptOptions.length > 0) {
        // Find the current agent in the dropdown options
        const currentAgentOption = this.promptOptions.find(
          (option) =>
            option.name === this.agentName ||
            option.name.toLowerCase() === this.agentName.toLowerCase(),
        );

        if (currentAgentOption) {
          this.selectedPrompt = currentAgentOption.name; // Use name for dropdown display
          this.agentCode = currentAgentOption.value.toString(); // Store agent code
          this.onPromptChanged(currentAgentOption);
          this.cdr.detectChanges();
        }
      }
    }, 1500); // Give more time for the dropdown to load
  }

  // Initialize execute mode - automatically open playground with agent selected
  private initializeExecuteMode(): void {
    // Ensure agent code is set for API calls
    if (!this.agentCode && this.agentName) {
      this.agentCode = this.agentName.trim().replace(/\s+/g, '_').toUpperCase();
    }

    // Recalculate node positions for execute mode (vertical layout)
    this.recalculateAllNodePositions();

    // Initialize the chat interface with personalized greeting
    this.chatMessages = [
      {
        from: 'ai',
        text: `Hi there! I am ${this.agentName || 'your build agent'}. How can I help you today?`,
      },
    ];

    // Force UI update
    this.cdr.detectChanges();
  }

  // Method to handle agent selection from card click
  public selectAgentFromCard(agentData: any): void {
    // Store the selected agent data
    this.autoSelectedAgentFromCard = agentData;

    // Set agent properties
    this.agentName = agentData.name;
    this.agentCode = agentData.code;
    this.selectedAgentMode = agentData.code;
    this.selectedUseCaseIdentifier = agentData.useCaseIdentifier;

    // Auto-select in dropdown (keep dropdown enabled)
    this.selectedPrompt = agentData.name;

    // Update the prompt options to include the selected agent if not already present
    if (this.promptOptions && this.promptOptions.length > 0) {
      const existingOption = this.promptOptions.find(
        (option) =>
          option.value === agentData.code || option.name === agentData.name,
      );

      if (!existingOption) {
        // Add the selected agent to the options
        this.promptOptions.push({
          value: agentData.code,
          name: agentData.name,
          agentData: agentData,
        } as any);
      }
    }

    // Force change detection
    this.cdr.detectChanges();
  }

  // Navigation
  goBack(): void {
    this.router.navigate(['/agents']);
  }

  // Test method to check current agent details for playground
  public testCurrentAgentDetails(): void {
    // Method kept for compatibility - details available in component properties
  }

  // Popup handling methods
  showSuccessMessage(title: string, message: string): void {
    this.popupTitle = title;
    this.popupMessage = message;
    this.showSuccessPopup = true;
  }

  // Show approval success message (redirects to agents page after confirmation)
  showApprovalSuccessMessage(title: string, message: string): void {
    this.popupTitle = title;
    this.popupMessage = message;
    this.isApprovalSuccess = true; // Set flag for approval success
    this.showSuccessPopup = true;
  }

  showErrorMessage(title: string, message: string): void {
    this.popupTitle = title;
    this.popupMessage = message;
    this.showErrorPopup = true;
  }

  showWarningMessage(title: string, message: string): void {
    this.popupTitle = title;
    this.popupMessage = message;
    this.showWarningPopup = true;
  }

  closeSuccessPopup(): void {
    this.showSuccessPopup = false;
    this.isApprovalSuccess = false;
    this.popupTitle = '';
    this.popupMessage = '';
  }

  // Handle success popup confirmation
  onSuccessConfirm(): void {
    const wasApprovalSuccess = this.isApprovalSuccess;
    this.closeSuccessPopup();

    // If this was an approval success, redirect to agents page
    if (wasApprovalSuccess) {
      this.router.navigate(['/build/agents']);
    }
    // Otherwise, playground is already opened automatically after save
  }

  closeErrorPopup(): void {
    this.showErrorPopup = false;
    this.popupTitle = '';
    this.popupMessage = '';
  }

  closeWarningPopup(): void {
    this.showWarningPopup = false;
    this.popupTitle = '';
    this.popupMessage = '';
  }

  // Handle warning popup confirmation - proceed with save
  onWarningConfirm(): void {
    this.closeWarningPopup();

    // Proceed with save based on agent type
    if (this.currentAgentType === 'individual') {
      this.performIndividualAgentSave();
    } else if (this.currentAgentType === 'collaborative') {
      this.performCollaborativeAgentSave();
    }
  }

  // Handle warning popup cancel - don't save
  onWarningCancel(): void {
    this.closeWarningPopup();
  }

  // Ensure connections are properly established in edit mode
  private ensureConnectionsInEditMode(): void {
    if (this.handleEditModeConnections && this.buildAgentNodes.length > 1) {
      // Always recreate connections in edit mode to ensure they're properly established
      this.createAgentFlowConnections(this.buildAgentNodes);

      // Force update of connection points
      if (this.canvasBoardComponent) {
        this.canvasBoardComponent.updateNodeConnectionPoints();
      }

      // Trigger change detection
      this.cdr.detectChanges();

      // Reset the flag after handling
      this.handleEditModeConnections = false;
    }
  }
}
