<div
  #nodeElement
  class="build-agent-node node"
  [class.selected]="isCurrentlySelected"
  [class.dragging]="isDragging"
  [class.disabled]="!mouseInteractionsEnabled"
  [class.execute-mode]="canvasMode === 'execute'"
  [attr.data-node-id]="isExecuteMode ? 'execute-' + executeNodeData?.type : currentNodeData.id"
  [attr.data-node-type]="isExecuteMode ? executeNodeData?.type : currentNodeData.type"
  cdkDrag
  #dragRef="cdkDrag"
  [cdkDragDisabled]="!mouseInteractionsEnabled || canvasMode === 'execute'"
  cdkDragBoundary=".canvas-container"
  [cdkDragFreeDragPosition]="nodePosition"
  (cdkDragEnded)="onDragEnded($event)"
  (mousedown)="onNodeMouseDown($event)"
  (click)="onNodeClick()"
  (dblclick)="onNodeDoubleClick()"
  (mouseenter)="onMouseEnter()"
  (mouseleave)="onMouseLeave()"
>
  <!-- Execute Mode: Circular Icon Only -->
  <div class="node-content-execute" *ngIf="canvasMode === 'execute'">
    <!-- Ava icons for all types -->
    <ava-icon
      [iconName]="iconName"
      iconSize="20"
      iconColor="white">
    </ava-icon>

    <!-- Tooltip for execute mode -->
    <div
      class="execute-tooltip"
      *ngIf="showTooltip && executeTooltipText"
      [innerHTML]="executeTooltipText">
    </div>
  </div>

  <!-- Build Mode: New Design with Icon Section and Label Section -->
  <div class="node-content-build" *ngIf="canvasMode !== 'execute'">
    <!-- Icon Section (Circular) -->
    <div class="node-icon-section">
      <!-- Ava icons for all types -->
      <ava-icon
        [iconName]="iconName"
        iconSize="24"
        iconColor="white">
      </ava-icon>
    </div>

    <!-- Label Section (Rectangular) -->
    <div class="node-label-section">
      <span class="node-label">{{ currentNodeData.name }}</span>
    </div>
  </div>

  <!-- Delete Button (only show on hover/selected and not in execute mode) -->
  <button
    class="delete-btn"
    *ngIf="canvasMode !== 'execute'"
    (click)="onDeleteClick($event)"
    title="Delete node"
  >
    <svg
      width="12"
      height="12"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="2"
    >
      <line x1="18" y1="6" x2="6" y2="18"></line>
      <line x1="6" y1="6" x2="18" y2="18"></line>
    </svg>
  </button>

  <!-- Single connection point is now handled via CSS ::before pseudo-element -->
</div>
