<p class="page-title">{{labels.pageTitle}} 🛠️</p>
<div class="create-models-container">
  <form [formGroup]="modelForm">
    <div class="form-layout">
      <!-- Left Column -->
      <div class="left-column">
        <!-- Model Details Card -->
        <div class="card-content">
          <ava-textbox [label]="labels.name" [id]="'modelName'" [placeholder]="labels.placeholderModelName"
            [formControl]="getControl('modelDeploymentName')" [error]="getFieldError('modelDeploymentName')"
            [required]="true">
          </ava-textbox>
          <ava-textarea [label]="labels.description" [placeholder]="labels.placeholderModelDescription"
            [formControl]="getControl('modelDescription')" formControlName="modelDescription" [required]="true"
            [error]="getFieldError('modelDescription')">
          </ava-textarea>
        </div>
      </div>

      <!-- Right Column -->
      <div class="right-column">
        <!-- Content wrapper to match left column height -->
        <div class="right-column-content">
          <div class="solid-card">
            <div class="card-content">
              <div class="header-with-save">
                <h4>{{labels.chooseEngineAndModel}}</h4>
                <ava-button [disabled]="!(modelForm.valid && selectedAiEngineId && selectedModelId)" *ngIf="!isEditMode"
                  label="Save" variant="primary" [customStyles]="{
                    background:
                      'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                    '--button-effect-color': '33, 90, 214',
                  }" (userClick)="onSave()">
                </ava-button>
                <ava-button *ngIf="isEditMode" label="Back" variant="secondary" (userClick)="onCancel()">
                </ava-button>
              </div>
              <div class="lists-container">
                <div class="model-type-selection">
                  <p class="selection-description">
                    {{labels.chooseModelType}}
                  </p>
                  <div class="list-container" [ngClass]="{ 'ava-list-disabled': isEditMode || mode === 'view' }">
                    <ava-list title="{{labels.chooseModelType}}" [items]="modelTypeList"
                      [selectedItemId]="selectedModelTypeId" (onOptionSelected)="onModelTypeSelected($event)">
                    </ava-list>
                  </div>
                </div>
                <div class="engine-selection">
                  <p class="selection-description">
                    {{labels.preferredAiEngine}}
                  </p>
                  <div class="list-container" [ngClass]="{ 'ava-list-disabled': isEditMode || mode === 'view' }">
                    <ava-list title="{{labels.chooseAiEngine}}" [items]="aiEngines"
                      [selectedItemId]="selectedAiEngineId" (onOptionSelected)="onAiEngineSelected($event)">
                    </ava-list>
                  </div>
                </div>
                <div class="model-selection">
                  <p class="selection-description">
                    {{labels.choosePreferredModel}}
                  </p>
                  <div class="list-container" [ngClass]="{ 'ava-list-disabled': isEditMode || mode === 'view' }">
                    <ava-list 
                      [emptyLabel]="(modelNames.length === 0 && selectedModelTypeId && selectedAiEngineId) ? 'No models available for the selected type and engine.' : labels.choosePreferredAIEngine"
                      title="{{labels.chooseModel}}"
                      [items]="modelNames" [selectedItemId]="selectedModelId"
                      (onOptionSelected)="onModelSelected($event)">
                    </ava-list>
                    <div *ngIf="modelNames.length === 0 && selectedModelTypeId && selectedAiEngineId" class="no-model-message">
                      No models available for the selected type and engine.
                    </div>
                  </div>
                </div>
              </div>

              <hr>
              <h3>{{labels.modelConfiguration}}</h3>
              <div class="message-wrapper">
                <p class="configuration-message" *ngIf="selectedAiEngineId==null || selectedModelTypeId==null">
                  {{labels.pleaseSelectMessage}}
                </p>
              </div>
              <div class="parameters-container">
                <div class="parameter-form">
                  <!-- Azure OpenAI Fields -->
                  <div *ngIf="inputVisibility.AzureOpenAI" class="param-row">
                    <div class="param-field">
                      <ava-textbox [label]="labels.baseurl" [id]="'baseurl'" [placeholder]="labels.placeholderBaseUrl"
                        [formControl]="getControl('baseurl')" [error]="getFieldError('baseurl')" [required]="true">
                      </ava-textbox>
                    </div>
                    <div class="param-field">
                      <ava-textbox [label]="labels.llmDeploymentName" [id]="'llmDeploymentName'"
                        [placeholder]="labels.placeholderLlmDeploymentName" [error]="getFieldError('llmDeploymentName')"
                        [type]="'text'" [formControl]="getControl('llmDeploymentName')" [required]="true">
                      </ava-textbox>
                    </div>
                    <div class="param-field">
                      <ava-textbox [label]="labels.apiKey" [id]="'apiKey'" [placeholder]="labels.placeholderApiKey"
                        [error]="getFieldError('apiKey')" [formControl]="getControl('apiKey')" [required]="true">
                      </ava-textbox>
                    </div>
                    <div class="param-field">
                      <ava-dropdown class="version-top" dropdownTitle="{{labels.dropdownTitleApiVersion}}"
                        [options]="apiVersionOptions" [formControl]="getControl('apiVersion')"
                        [error]="getFieldError('apiVersion')" [required]="true" label="{{labels.apiVersion}}"
                        [selectedValue]="modelForm.get('apiVersion')?.value">
                      </ava-dropdown>
                    </div>
                  </div>
                  <!-- Amazon Bedrock Fields -->
                  <div *ngIf="inputVisibility.AmazonBedrock" class="param-row">
                    <div class="param-field">
                      <ava-textbox [label]="labels.awsAccessKey" [id]="'awsAccessKey'"
                        [placeholder]="labels.placeholderAwsAccessKey" [type]="'text'"
                        [formControl]="getControl('awsAccessKey')" [error]="getFieldError('awsAccessKey')"
                        [required]="true">
                      </ava-textbox>
                    </div>
                    <div class="param-field">
                      <ava-textbox [label]="labels.awsSecretKey" [id]="'awsSecretKey'"
                        [placeholder]="labels.placeholderAwsSecretKey" [formControl]="getControl('awsSecretKey')"
                        [error]="getFieldError('awsSecretKey')" [required]="true">
                      </ava-textbox>
                    </div>
                    <div class="param-field">
                      <ava-textbox [label]="labels.awsRegion" [id]="'awsRegion'"
                        [placeholder]="labels.placeholderAwsRegion" [type]="'text'"
                        [formControl]="getControl('awsRegion')" [error]="getFieldError('awsRegion')" [required]="true">
                      </ava-textbox>
                    </div>
                    <div class="param-field">
                      <ava-textbox [label]="labels.bedrockModelId" [id]="'bedrockModelId'"
                        [placeholder]="labels.placeholderBedRockModel" [type]="'text'"
                        [formControl]="getControl('bedrockModelId')" [error]="getFieldError('bedrockModelId')"
                        [required]="true">
                      </ava-textbox>
                    </div>
                  </div>
                  <!-- Google AI Fields -->
                  <div *ngIf="inputVisibility.GoogleAI" class="param-row">
                    <div class="param-field">
                      <ava-textbox [label]="labels.gcpProjectId" [id]="'gcpProjectId'"
                        [placeholder]="labels.placeholderGcpProjectId" [type]="'text'"
                        [formControl]="getControl('gcpProjectId')" [error]="getFieldError('gcpProjectId')"
                        [required]="true">
                      </ava-textbox>
                    </div>
                    <div class="param-field">
                      <ava-textbox [label]="labels.gcpLocation" [id]="'gcpLocation'"
                        [placeholder]="labels.placeholderGcpLocation" [type]="'text'"
                        [formControl]="getControl('gcpLocation')" [required]="true"
                        [error]="getFieldError('gcpLocation')">
                      </ava-textbox>
                    </div>
                    <div class="param-field">
                      <ava-textbox [label]="labels.vertexAIEndpoint" [id]="'vertexAIEndpoint'"
                        [placeholder]="labels.placeholderVertextAIEndPoint" [type]="'url'"
                        [formControl]="getControl('vertexAIEndpoint')" [error]="getFieldError('vertexAIEndpoint')"
                        [required]="true">
                      </ava-textbox>
                    </div>
                  </div>
                  <!-- DaOpenSource AI Fields -->
                  <div *ngIf="inputVisibility.DaOpenSourceAI" class="param-row">
                    <div class="param-field">
                      <ava-textbox [label]="labels.serviceUrl" [id]="'serviceUrl'"
                        [placeholder]="labels.placeholderServiceUrl" [type]="'url'"
                        [formControl]="getControl('serviceUrl')" [error]="getFieldError('serviceUrl')"
                        [required]="true">
                      </ava-textbox>
                    </div>
                    <div class="param-field">
                      <ava-textbox [label]="labels.apiKeyEncoded" [id]="'apiKeyEncoded'"
                        [placeholder]="labels.placeholderEncodedApiKey" [formControl]="getControl('apiKeyEncoded')"
                        [error]="getFieldError('apiKeyEncoded')" [required]="true">
                      </ava-textbox>
                    </div>
                    <div class="param-field">
                      <ava-textbox [label]="labels.headerName" [id]="'headerName'"
                        [placeholder]="labels.placeholderHeaderName" [formControl]="getControl('headerName')"
                        [error]="getFieldError('headerName')" [required]="true">
                      </ava-textbox>
                    </div>
                  </div>
                  <!-- BNY Fields -->
                  <div *ngIf="inputVisibility.BNY" class="param-row">
                    <!-- Add BNY specific fields here when needed -->

                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>
<!-- Success Popup -->
<ava-popup [show]="showSuccessPopup" [title]="popupTitle" [message]="popupMessage" [showHeaderIcon]="true"
  headerIconName="{{iconName}}" iconColor="#28a745" [showClose]="true" [showCancel]="false"
  [confirmButtonVariant]="'primary'" [confirmButtonBackground]="'#28a745'" (confirm)="onSuccessConfirm()"
  (closed)="closeSuccessPopup()">
</ava-popup>