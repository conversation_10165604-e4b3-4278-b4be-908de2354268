import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ToolsComponent } from './tools.component';
import { CommonModule } from '@angular/common';
import { RouterTestingModule } from '@angular/router/testing';
import { CreateCardComponent } from '../../../shared/components/create-card/create-card.component';
import { DataCardComponent } from '../../../shared/components/data-card/data-card.component';
import { FilterBarComponent } from '../../../shared/components/filter-bar/filter-bar.component';
import { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';
import { FilterService } from '../../../shared/services/filter.service';
import { PaginationService } from '../../../shared/services/pagination.service';
import { ToolsService } from '../../../shared/services/tools.service';
import { of } from 'rxjs';
import toolsText from './constants/tools.json';

describe('ToolsComponent', () => {
  let component: ToolsComponent;
  let fixture: ComponentFixture<ToolsComponent>;

  const mockToolsService = {
    getUserToolsList: jasmine.createSpy('getUserToolsList').and.returnValue(
      of({
        tools: [
          {
            toolId: 1,
            toolName: 'Tool 1',
            toolDescription: 'Description 1',
            createTimestamp: '2023-01-01',
          },
          {
            toolId: 2,
            toolName: 'Tool 2',
            toolDescription: 'Description 2',
            createTimestamp: '2023-01-02',
          },
        ],
      }),
    ),
    deleteTool: jasmine.createSpy('deleteTool').and.returnValue(of({})),
  };

  const mockFilterService = {
    getFilterConfig: jasmine.createSpy('getFilterConfig').and.returnValue({}),
    filterData: jasmine
      .createSpy('filterData')
      .and.callFake((data, filters, filterPropertyMap) => {
        return data.filter((item) => {
          return Object.keys(filters).every((key) => {
            const filterValue = filters[key];
            const itemValue = item[this.filterPropertyMap[key]];
            return itemValue === filterValue;
          });
        });
      }),
  };

  const mockPaginationService = {
    getPaginatedItems: jasmine
      .createSpy('getPaginatedItems')
      .and.callFake((items, currentPage, itemsPerPage) => {
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        return {
          displayedItems: items.slice(startIndex, endIndex),
          totalPages: Math.ceil(items.length / itemsPerPage),
        };
      }),
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CommonModule, RouterTestingModule],
      declarations: [
        ToolsComponent,
        SearchBar,
        CreateCardComponent,
        DataCardComponent,
        FilterBarComponent,
        PageFooterComponent,
      ],
      providers: [
        { provide: ToolsService, useValue: mockToolsService },
        { provide: FilterService, useValue: mockFilterService },
        { provide: PaginationService, useValue: mockPaginationService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ToolsComponent);
    component = fixture.componentInstance;
    component.filterPropertyMap = {
      userType: 'userType',
      client: 'client',
      department: 'department',
      category: 'category',
      role: 'role',
      project: 'project',
    };
    component.labels = toolsText.labels;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // Test cases for rendering
  it('should render search bar', () => {
    const searchBar = fixture.nativeElement.querySelector('app-search-bar');
    expect(searchBar).toBeTruthy();
  });

  it('should render create card on first page', () => {
    component.currentPage = 1;
    fixture.detectChanges();
    const createCard = fixture.nativeElement.querySelector('app-create-card');
    expect(createCard).toBeTruthy();
  });

  it('should not render create card on pages other than first', () => {
    component.currentPage = 2;
    fixture.detectChanges();
    const createCard = fixture.nativeElement.querySelector('app-create-card');
    expect(createCard).toBeFalsy();
  });

  // Test cases for interactions
  it('should toggle filter bar visibility', () => {
    const initialVisibility = component.isFilterBarVisible;
    component.toggleFilterBar();
    expect(component.isFilterBarVisible).toBe(!initialVisibility);
  });

  it('should navigate to create tool page on create card click', () => {
    spyOn(component['router'], 'navigate');
    component.onCreateTool();
    expect(component['router'].navigate).toHaveBeenCalledWith([
      '/libraries/tools/create',
    ]);
  });

  it('should navigate to edit tool page on card click', () => {
    spyOn(component['router'], 'navigate');
    component.onCardClicked('1');
    expect(component['router'].navigate).toHaveBeenCalledWith(
      ['/libraries/tools/edit', '1'],
      { queryParams: { returnPage: 1 } },
    );
  });

  it('should navigate to execute tool page on execute action click', () => {
    spyOn(component['router'], 'navigate');
    component.onActionClicked({ action: 'execute', cardId: '1' });
    expect(component['router'].navigate).toHaveBeenCalledWith(
      ['/libraries/tools/edit', '1'],
      { queryParams: { execute: 'true', returnPage: 1 } },
    );
  });

  it('should call delete tool service on delete action click', () => {
    component.onActionClicked({ action: 'delete', cardId: '1' });
    expect(mockToolsService.deleteTool).toHaveBeenCalledWith(1);
  });

  // Test cases for service interactions
  it('should fetch tools on init', () => {
    expect(mockToolsService.getUserToolsList).toHaveBeenCalled();
    expect(component.allTools.length).toBe(2);
  });

  it('should update displayed tools on page change', () => {
    component.currentPage = 2;
    component.onPageChange(2);
    expect(component.displayedTools.length).toBe(0);
  });

  it('should filter tools based on filter change', () => {
    const filters = { userType: 'Admin' };
    component.allTools = [
      {
        userType: 'Admin',
        id: '1',
        title: 'Tool 1',
        tags: [],
        createdDate: '2023-01-01',
        actions: [],
      },
    ];
    component.onFilterChange(filters);
    expect(component.filteredTools.length).toBe(1);
  });

  it('should reset to all tools when filters are empty', () => {
    component.allTools = [
      {
        id: '1',
        title: 'Tool 1',
        tags: [],
        createdDate: '2023-01-01',
        actions: [],
      },
    ];
    component.filteredTools = [];
    component.onFilterChange({});
    expect(component.filteredTools.length).toBe(1);
  });

  it('should update query params on page change', () => {
    spyOn(component['router'], 'navigate');
    component.onPageChange(2);
    expect(component['router'].navigate).toHaveBeenCalledWith([], {
      relativeTo: component['route'],
      queryParams: { page: 2 },
      queryParamsHandling: 'merge',
    });
  });

  it('should transform API data correctly', () => {
    const apiTools = [
      {
        toolId: 1,
        toolName: 'Tool 1',
        toolDescription: 'Description 1',
        createTimestamp: '2023-01-01',
      },
    ];
    const transformedData = component.transformApiData(apiTools);
    expect(transformedData.length).toBe(1);
    expect(transformedData[0].id).toBe('1');
  });
});
