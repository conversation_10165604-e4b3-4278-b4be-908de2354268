import {
  Component,
  OnInit,
  OnDestroy,
  EventEmitter,
  Output,
  ViewChild,
  AfterViewInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  FormControl,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { ChatMessage } from '../../../../shared/components/chat-window/chat-window.component';
import { ToolExecutionService } from '../../../../shared/services/tool-execution/tool-execution.service';
import { ToolsService } from '../../../../shared/services/tools.service';
import { Subscription } from 'rxjs';
import { PromptEnhanceService } from '../../../../shared/services/prompt-enhance.service';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import {
  CodeEditorComponent,
  CodeLanguage,
  CodeEditorTheme,
  EditorActionButton,
} from '../../../../shared/components/code-editor/code-editor.component';
import toolsText from '../constants/tools.json';
import { PlaygroundComponent } from 'projects/console/src/app/shared/components/playground/playground.component';
import {
  AvaTextareaComponent,
  AvaTextboxComponent,
  ButtonComponent,
  DropdownOption,
} from '@ava/play-comp-library';

interface ExtractedParameter {
  name: string;
  type: string;
}

interface Tool {
  id: number;
  name: string;
}

interface ParameterCollectionState {
  parameters: ExtractedParameter[];
  currentParameterIndex: number;
  collectedInputs: { [key: string]: any };
  isCollecting: boolean;
}

@Component({
  selector: 'app-create-tools',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    AvaTextboxComponent,
    AvaTextareaComponent,
    ButtonComponent,
    PlaygroundComponent,
    CodeEditorComponent,
  ],
  templateUrl: './create-tools.component.html',
  styleUrls: ['./create-tools.component.scss'],
})
export class CreateToolsComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild(CodeEditorComponent) codeEditor!: CodeEditorComponent;

  toolId: number | null = null;
  isEditMode: boolean = false;
  isCloneMode: boolean = false;
  isExecuteMode: boolean = true;
  isFieldsDisabled: boolean = false;
  showChatInterface: boolean = false;
  selectedTool: string | null = null;
  toolForm: FormGroup;
  chatMessages: ChatMessage[] = [];
  isProcessingChat: boolean = false;

  parameterState: ParameterCollectionState = {
    parameters: [],
    currentParameterIndex: 0,
    collectedInputs: {},
    isCollecting: false,
  };

  private executionSubscription: Subscription = new Subscription();
  private waitingForRestartConfirmation: boolean = false;
  public placeholder: any = toolsText.TOOL_PLACEHOLDER.toolClassDef;
  public labels: any = toolsText.labels;
  @Output() promptChange = new EventEmitter<string>();
  public validationOutput: string = '';
  public showValidationOutput: boolean = false;
  public validationOutputEditorConfig = {
    title: 'Tool Compiler',
    language: 'json' as CodeLanguage,
    theme: 'light' as CodeEditorTheme,
    readOnly: true,
    height: '250px',
  };

  public editorActions: EditorActionButton[] = [
    { label: 'Select All', style: 'secondary', customClass: '', icon: '' },
    { label: 'Reset', style: 'secondary', customClass: '', icon: '' },
  ];

  private originalToolData: any = null;
  private codeEditorValueToSet: string | null = null;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private toolExecutionService: ToolExecutionService,
    private toolsService: ToolsService,
    private promptGenerateService: PromptEnhanceService,
    private tokenStorage: TokenStorageService,
  ) {
    this.toolForm = this.fb.group({
      name: [''],
      description: [''],
      toolClassName: [''],
      classDefinition: [''],
    });
  }

  private getUserSignature(): string {
    const userSignature = this.tokenStorage.getDaUsername() || '<EMAIL>';
    return userSignature;
  }

  private formatLocalDateTime(date: Date = new Date()): string {
    // Format date as LocalDateTime for Java backend (without timezone)
    return date.getFullYear() + '-' +
           String(date.getMonth() + 1).padStart(2, '0') + '-' +
           String(date.getDate()).padStart(2, '0') + 'T' +
           String(date.getHours()).padStart(2, '0') + ':' +
           String(date.getMinutes()).padStart(2, '0') + ':' +
           String(date.getSeconds()).padStart(2, '0') + '.' +
           String(date.getMilliseconds()).padStart(3, '0');
  }

  ngOnInit(): void {
    const toolIdParam = this.route.snapshot.paramMap.get('id');
    this.toolId = toolIdParam ? parseInt(toolIdParam, 10) : null;
    const urlPath = this.router.url;
    this.isEditMode = urlPath.includes('/edit/');
    this.isCloneMode = urlPath.includes('/clone/');
    this.isExecuteMode = urlPath.includes('/execute/');
    this.isFieldsDisabled = this.isExecuteMode;

    console.log('ngOnInit - toolId:', this.toolId, 'isEditMode:', this.isEditMode, 'isCloneMode:', this.isCloneMode, 'isExecuteMode:', this.isExecuteMode);

    if ((this.isEditMode || this.isCloneMode || this.isExecuteMode) && this.toolId) {
      this.loadToolData(this.toolId);
      if (this.isExecuteMode) {
        this.initializeChatMessages();
      }
    }

    // Window resize will be handled by HostListener
  }

  ngAfterViewInit(): void {
    // If value was loaded before editor was ready, set it now
    if (this.codeEditor && this.codeEditorValueToSet) {
      this.codeEditor.setValue(this.codeEditorValueToSet);
      this.codeEditorValueToSet = null;
    }
  }

  ngOnDestroy(): void {
    if (this.executionSubscription) {
      this.executionSubscription.unsubscribe();
    }
  }

  private initializeChatMessages(): void {
    this.chatMessages = [
      {
        from: 'ai',
        text: "Hi! Welcome to the tool testing playground. I'll help you test your tool by collecting the required parameters.",
      },
    ];
  }

  onSave(): void {
    const returnPage = this.route.snapshot.queryParamMap.get('returnPage');
    const pageNumber = returnPage ? parseInt(returnPage) : 1;

    if (this.isEditMode && this.toolId) {
      // Transform to new API payload structure for update
      const updatePayload = {
        id: parseInt(this.toolId.toString()),
        name: this.toolForm.get('name')?.value,
        description: this.toolForm.get('description')?.value,
        createdBy: this.originalToolData?.createdBy || this.getUserSignature(),
        modifiedBy: this.getUserSignature(), // Use current user signature for modifiedBy
        isDeleted: false,
        toolConfigs: {
          image: this.originalToolData?.toolImage || '', // Use original image if available
          tool_class_def: this.toolForm.get('classDefinition')?.value,
          tool_class_name: this.toolForm.get('toolClassName')?.value || 'TEST'
        }
      };

      console.log('Update payload being sent:', updatePayload);

      this.toolsService.updateUserTool(updatePayload).subscribe(
        (response) => {
          this.isExecuteMode = true;
          this.showChatInterface = true;
          this.initializeChatMessages();
          this.resetParameterState();
        },
        (error) => {
          console.error('Error updating tool:', error);
          console.error('Error details:', error.error);
          console.error('Status:', error.status);
        },
      );
    } else {
      // For create and clone
      // New payload structure for creating tools
      const createPayload = {
        name: this.toolForm.get('name')?.value,
        description: this.toolForm.get('description')?.value,
        toolConfigs: {
          image: '', // Default empty image for new tools
          tool_class_def: this.toolForm.get('classDefinition')?.value,
          tool_class_name: this.toolForm.get('toolClassName')?.value
        },
        createdBy: this.getUserSignature(),
        modifiedBy: this.getUserSignature()
      };

      console.log('Creating new tool with payload:', createPayload);
      this.toolsService.addNewUserTool(createPayload).subscribe(
        (response) => {
          this.isExecuteMode = true;
          this.showChatInterface = true;
          this.initializeChatMessages();
          this.resetParameterState();
        },
        (error) => {
          console.error('Error creating tool', error);
        },
      );
    }
  }

  onExecute(): void {
    console.log('Executing tool:', this.toolForm.value);
    if (this.toolId) {
      if (this.isExecuteMode && this.showChatInterface) {
        this.extractParameters();
      } else {
        console.log('Entering execute mode, showing chat interface');
        this.isExecuteMode = true;
        this.showChatInterface = true;
        this.initializeChatMessages();
        setTimeout(() => {
          this.extractParameters();
        }, 500);
      }
    }
  }

  private extractParameters(): void {
    const toolClassDef = this.toolForm.get('classDefinition')?.value;
    const toolClassName = this.toolForm.get('toolClassName')?.value;
    const toolName = this.toolForm.get('name')?.value;
    if (!toolClassDef || !toolClassName) {
      this.addChatMessage(
        'ai',
        'Error: Tool class definition or class name is missing.',
      );
      return;
    }
    const useCase = 'PARAMETER_EXTRACTOR';
    const useCaseIdentifier =
      'PARAMETER_EXTRACTOR@ADD@GOPAL@TEST_GOPAL@GOPALTEST';

    this.isProcessingChat = true;
    this.addChatMessage('ai', 'Analyzing your tool to extract parameters...');

    this.promptGenerateService
      .modelApi(toolClassDef, useCase, false, useCaseIdentifier)
      .subscribe({
        next: (response: any) => {
          console.log('Parameter extraction response:', response);
          try {
            const parametersText = response?.response?.choices?.[0]?.text;
            if (!parametersText) {
              throw new Error('No parameters found in response');
            }
            const parametersObj = JSON.parse(parametersText);
            const parameters: ExtractedParameter[] = Object.keys(
              parametersObj,
            ).map((key) => ({
              name: key,
              type: parametersObj[key],
            }));

            if (parameters.length === 0) {
              this.isProcessingChat = false;
              this.addChatMessage(
                'ai',
                'No parameters found for this tool. The tool might not require any input parameters.',
              );
              return;
            }

            this.parameterState = {
              parameters: parameters,
              currentParameterIndex: 0,
              collectedInputs: {},
              isCollecting: true,
            };

            this.isProcessingChat = false;
            this.promptForNextParameter();
          } catch (error) {
            console.error('Error parsing parameters:', error);
            this.isProcessingChat = false;
            this.addChatMessage(
              'ai',
              'Error: Failed to parse extracted parameters. Please check your tool definition.',
            );
          }
        },
        error: (error) => {
          console.error('Parameter extraction error', error);
          this.isProcessingChat = false;
          this.addChatMessage(
            'ai',
            'Error: Failed to extract parameters from your tool. Please check your tool definition.',
          );
        },
      });
  }

  private promptForNextParameter(): void {
    if (
      this.parameterState.currentParameterIndex <
      this.parameterState.parameters.length
    ) {
      const currentParam =
        this.parameterState.parameters[
          this.parameterState.currentParameterIndex
        ];
      const message = `Please enter input for parameter "${currentParam.name}" (type: ${currentParam.type}):`;
      this.addChatMessage('ai', message);
    }
  }

  private addChatMessage(from: 'ai' | 'user', text: string): void {
    this.chatMessages = [...this.chatMessages, { from, text }];
  }

  handleChatMessage(message: string): void {
    if (!this.isExecuteMode) return;
    this.addChatMessage('user', message);

    // Only execute modelApi when user sends a prompt
    this.isProcessingChat = true;
    const useCase = 'SECURITY_ANALYSIS'; // Replace with your actual use case if needed
    const useCaseIdentifier = 'SECURITY_ANALYSIS@TOOL@USER'; // Replace as needed
    this.promptGenerateService.modelApi(
      message,
      useCase,
      false,
      useCaseIdentifier
    ).subscribe({
      next: (response: any) => {
        this.isProcessingChat = false;
        if (response && response.response && response.response.choices && response.response.choices.length > 0) {
          const aiText = response.response.choices[0].text;
          this.addChatMessage('ai', aiText);
        } else {
          this.addChatMessage('ai', 'No response from model API.');
        }
      },
      error: (error) => {
        this.isProcessingChat = false;
        this.addChatMessage('ai', `Model API call failed: ${error?.error?.message || 'Unknown error occurred'}`);
      },
    });
  }

  private handleParameterInput(input: string): void {
    const currentParam =
      this.parameterState.parameters[this.parameterState.currentParameterIndex];
    let processedInput: any = input;

    try {
      switch (currentParam.type) {
        case 'number':
          processedInput = parseFloat(input);
          if (isNaN(processedInput)) {
            this.addChatMessage(
              'ai',
              `Invalid number format. Please enter a valid number for "${currentParam.name}":`,
            );
            return;
          }
          break;
        case 'boolean':
          const lowerInput = input.toLowerCase();
          if (
            lowerInput === 'true' ||
            lowerInput === '1' ||
            lowerInput === 'yes'
          ) {
            processedInput = true;
          } else if (
            lowerInput === 'false' ||
            lowerInput === '0' ||
            lowerInput === 'no'
          ) {
            processedInput = false;
          } else {
            this.addChatMessage(
              'ai',
              `Invalid boolean format. Please enter true/false for "${currentParam.name}":`,
            );
            return;
          }
          break;
        case 'object':
          try {
            processedInput = JSON.parse(input);
          } catch {
            this.addChatMessage(
              'ai',
              `Invalid JSON format. Please enter a valid JSON object for "${currentParam.name}":`,
            );
            return;
          }
          break;
        case 'array':
          try {
            processedInput = JSON.parse(input);
            if (!Array.isArray(processedInput)) {
              throw new Error('Not an array');
            }
          } catch {
            this.addChatMessage(
              'ai',
              `Invalid array format. Please enter a valid JSON array for "${currentParam.name}":`,
            );
            return;
          }
          break;
      }
      this.parameterState.collectedInputs[currentParam.name] = processedInput;
      this.parameterState.currentParameterIndex++;
      if (
        this.parameterState.currentParameterIndex >=
        this.parameterState.parameters.length
      ) {
        this.executeToolWithParameters();
      } else {
        this.promptForNextParameter();
      }
    } catch (error) {
      console.error('Error processing parameter input:', error);
      this.addChatMessage(
        'ai',
        `Error processing input for "${currentParam.name}". Please try again:`,
      );
    }
  }

  private executeToolWithParameters(): void {
    this.parameterState.isCollecting = false;
    this.isProcessingChat = true;

    this.addChatMessage(
      'ai',
      'All parameters collected! Executing your tool...',
    );

    const payload = {
      class_definition: this.toolForm.get('classDefinition')?.value,
      class_name: this.toolForm.get('toolClassName')?.value,
      inputs: this.parameterState.collectedInputs,
    };

    this.toolsService.testTool(payload).subscribe({
      next: (response: any) => {
        console.log('Tool execution response:', response);
        this.isProcessingChat = false;

        if (response.status === 'success') {
          this.addChatMessage(
            'ai',
            `Tool executed successfully! Output: ${response.output}`,
          );
        } else {
          this.addChatMessage(
            'ai',
            `Tool execution failed: ${response.detail || 'Unknown error'}`,
          );
        }
        this.waitingForRestartConfirmation = true;
        setTimeout(() => {
          this.addChatMessage(
            'ai',
            'Would you like to test the tool again with different parameters? (Type "yes" to restart or anything else to continue)',
          );
        }, 1000);
      },
      error: (error) => {
        console.error('Tool execution error:', error);
        this.isProcessingChat = false;
        this.addChatMessage(
          'ai',
          `Tool execution failed: ${error?.error?.message || 'Unknown error occurred'}`,
        );
      },
    });
  }

  onExit(): void {
    if (this.isEditMode) {
      this.resetParameterState();
      console.log('Exited execution mode, returning to edit mode');
    } else {
      const returnPage = this.route.snapshot.queryParamMap.get('returnPage');
      const pageNumber = returnPage ? parseInt(returnPage) : 1;

      this.router.navigate(['/libraries/tools'], {
        queryParams: { page: pageNumber },
      });
    }
  }

  private resetParameterState(): void {
    this.parameterState = {
      parameters: [],
      currentParameterIndex: 0,
      collectedInputs: {},
      isCollecting: false,
    };
  }

  getControl(name: string): FormControl {
    return this.toolForm.get(name) as FormControl;
  }

  getFieldError(fieldName: string): string {
    const field = this.toolForm.get(fieldName);
    if (field && field.invalid && (field.touched || field.dirty)) {
      if (field.errors?.['required']) {
        if (fieldName === 'name') return this.labels.errorToolNameRequired;
        if (fieldName === 'toolClassName') return this.labels.errorToolClassNameRequired;
        if (fieldName === 'description') return this.labels.errorDescriptionRequired;
        if (fieldName === 'classDefinition') return this.labels.errorToolConfigRequired;
        return this.labels.errorRequired || 'This field is required';
      }
      // Add more error types as needed
    }
    return '';
  }

  validateCode = (): void => {
    this.validateTool();
  };

  public validateTool(): void {
    this.showValidationOutput = false;
    this.validationOutput = '';
    const useCase = 'VALIDATE_TOOLS';
    const useCaseIdentifier =
      'VALIDATE_TOOLS@ASCENDION@PLATFORM_ENGINEERING@AVA@DIGITAL_ASCENDER';
    const toolClassDef = this.toolForm.controls['classDefinition'].value;
    this.promptGenerateService
      .modelApi(toolClassDef, useCase, false, useCaseIdentifier)
      .subscribe({
        next: (res: any) => {
          let responseText = res?.response?.choices?.[0]?.text;
          if (!responseText) {
            this.validationOutput = 'Unable to validate, please try again.';
            this.showValidationOutput = true;
            if (this.codeEditor) this.codeEditor.hideProcessingLoader();
            return;
          }
          // Remove markdown code block if present
          responseText = responseText
            .replace(/```json\n?/, '')
            .replace(/```\n?$/, '');
          try {
            const parsed = JSON.parse(responseText);
            // Format as plain text
            let formatted = '';
            if (
              parsed.issues &&
              Array.isArray(parsed.issues) &&
              parsed.issues.length > 0
            ) {
              parsed.issues.forEach((issue: any, idx: number) => {
                formatted += `Issue ${idx + 1}:\n`;
                formatted += `  Type: ${issue.type}\n`;
                formatted += `  Description: ${issue.description}\n`;
                formatted += `  Severity: ${issue.severity}\n`;
                formatted += `  Line Number: ${issue.line_number}\n`;
                formatted += `  Suggestion: ${issue.suggestion}\n\n`;
              });
            } else {
              formatted = 'No issues found.';
            }
            this.validationOutput = formatted;
          } catch (e) {
            this.validationOutput = responseText;
          }
          this.showValidationOutput = true;
          if (this.codeEditor) this.codeEditor.hideProcessingLoader();
        },
        error: (e) => {
          this.validationOutput =
            'Error: ' + (e?.error?.message || 'Unknown error');
          this.showValidationOutput = true;
          if (this.codeEditor) this.codeEditor.hideProcessingLoader();
        },
      });
  }

  loadToolData(toolId: number): void {
    this.toolsService.getUserToolDetails(toolId).subscribe(
      (response) => {
        console.log('API response:', response); // Debug log
        const tool = response.tools && response.tools[0];
        console.log('Tool for patching:', tool); // Debug log
        console.log('Tool ID:', tool?.toolId);
        if (tool) {
          this.originalToolData = tool;
          this.toolForm.patchValue({
            name: this.isCloneMode ? '' : (tool.toolName || ''),
            description: tool.toolDescription || '',
            toolClassName: tool.toolClassName || '',
            classDefinition: tool.toolClassDef || '',
          });
          // Set code editor value, but only if editor is ready
          if (this.codeEditor) {
            this.codeEditor.setValue(tool.toolClassDef || '');
          } else {
            this.codeEditorValueToSet = tool.toolClassDef || '';
          }
        }
      },
      (error) => {
        console.error('Error loading tool data:', error);
      },
    );
  }

  //Drop Down
  promptOptions: DropdownOption[] = [
    { value: 'default', name: 'Choose Prompt' },
    { value: 'ruby-developer', name: 'Senior Ruby Developer' },
    { value: 'python-developer', name: 'Python Developer' },
    { value: 'data-scientist', name: 'Data Scientist' },
    { value: 'frontend-developer', name: 'Frontend Developer' },
  ];

  onPromptChanged(option: DropdownOption) {
    console.log('Prompt changed in parent:', option);
    // your logic to handle selected prompt
  }

  onEditorAction(idx: number) {
    if (!this.codeEditor) return;
    if (idx === 0) this.codeEditor.selectAll();
    if (idx === 1) this.codeEditor.clear();
  }

  handleApproval() {
    if (!this.isEditMode || !this.toolId) {
      console.error('Cannot send for approval: Tool must be saved first');
      return;
    }

    const returnPage = this.route.snapshot.queryParamMap.get('returnPage');
    const pageNumber = returnPage ? parseInt(returnPage) : 1;

    // Create approval payload with the exact structure required
    const approvalPayload = {
      id: parseInt(this.toolId.toString()),
      name: this.toolForm.get('name')?.value,
      description: this.toolForm.get('description')?.value,
      createdBy: this.originalToolData?.createdBy || this.getUserSignature(),
      modifiedBy: this.getUserSignature(), // Use current user signature for modifiedBy
      createdAt: this.originalToolData?.createTimestamp || this.formatLocalDateTime(),
      "put ": this.formatLocalDateTime(), // Using LocalDateTime format without timezone
      isDeleted: true, // Set to true for approval request
      toolConfigs: {
        image: this.originalToolData?.toolImage || '',
        def: this.toolForm.get('classDefinition')?.value,
        CHANGE2: this.toolForm.get('toolClassName')?.value || 'TEST'
      }
    };

    console.log('Sending for approval with payload:', approvalPayload);
    console.log('Approval payload structure:', JSON.stringify(approvalPayload, null, 2));
    console.log('API Endpoint: /v2/api/admin/ava/force/da/userTools/change_request');

    this.toolsService.updateUserTool(approvalPayload).subscribe(
      (response) => {
        console.log('Approval request successful:', response);
        // You can add a success message or redirect here
        this.router.navigate(['/libraries/tools'], {
          queryParams: { page: pageNumber },
        });
      },
      (error) => {
        console.error('Error sending for approval:', error);
        console.error('Error details:', error.error);
        console.error('Status:', error.status);
        console.error('Status text:', error.statusText);
      },
    );
  }
}
