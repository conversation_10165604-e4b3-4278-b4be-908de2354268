<p class="page-title">Tool Creator 🛠️</p>
<div class="create-tools-container">
  <form [formGroup]="toolForm">
    <div
      class="form-layout"
      [ngClass]="{ 'three-column-layout': isExecuteMode && showChatInterface }"
    >
      <!-- Left Column -->
      <div class="left-column">
        <!-- Tool Details Card -->
        <ava-textbox
          [formControl]="getControl('name')"
          [label]="labels.toolName"
          id="toolName"
          name="toolName"
          placeholder="Enter tool name"
          variant="primary"
          size="md"
          [fullWidth]="true"
          [required]="true"
          [readonly]="isFieldsDisabled"
          [error]="getFieldError('name')"
        ></ava-textbox>
        <ava-textbox
          [formControl]="getControl('toolClassName')"
          [label]="labels.toolClassName"
          id="toolClassName"
          name="toolClassName"
          placeholder="Enter tool class"
          variant="primary"
          size="md"
          [fullWidth]="true"
          [required]="true"
          [readonly]="isFieldsDisabled"
          [error]="getFieldError('toolClassName')"
        ></ava-textbox>
        <ava-textarea
          id="description"
          name="description"
          [label]="labels.description"
          [formControl]="getControl('description')"
          placeholder="Enter description"
          [rows]="4"
          size="md"
          [fullWidth]="true"
          [required]="true"
          [readonly]="isFieldsDisabled"
          [error]="getFieldError('description')"
        >
        </ava-textarea>
      </div>

      <!-- Middle Column (previously Right) -->
      <div class="middle-column">
        <!-- Content wrapper to match left column height -->
        <div class="middle-column-content">
          <div class="card-content code-editor">
            <!-- Code Editor Component -->
            <app-code-editor
              #codeEditor
              placeholder="{{placeholder}}"
              title="{{ labels.toolClassDefinition }}"
              language="python"
              [Control]="getControl('classDefinition')"
              customCssClass="tools-monaco-editor"
              (primaryButtonSelected)="validateCode()"
              footerText="{{ labels.note }}"
              [actionButtons]="editorActions"
              (actionButtonClicked)="onEditorAction($event)"
              [readonly]="isFieldsDisabled"
            >
            </app-code-editor>

            <!-- Validation Output JSON Editor -->
            <div *ngIf="showValidationOutput" class="validation-output-section">
              <app-code-editor
              [title]="validationOutputEditorConfig.title"
                [language]="validationOutputEditorConfig.language"
                [theme]="validationOutputEditorConfig.theme"
                [readonly]="validationOutputEditorConfig.readOnly"
                [height]="validationOutputEditorConfig.height"
                [value]="validationOutput"
                customCssClass="validation-json-editor"
              >
              </app-code-editor>
            </div>

            <!-- Buttons at bottom of middle column -->
            <div class="middle-column-buttons">
              <ava-button
                label="{{ labels.exit }}"
                variant="secondary"
                size="small"
                (userClick)="onExit()"
              >
              </ava-button>
              <!-- Show Save in create/clone, Update in edit -->
              <ng-container *ngIf="!isEditMode">
                <ava-button
                  label="{{ labels.save }}"
                  variant="primary"
                  size="small"
                  (userClick)="onSave()"
                >
                </ava-button>
              </ng-container>
              <ng-container *ngIf="isEditMode">
                <ava-button
                  label="{{ labels.update }}"
                  variant="primary"
                  size="small"
                  (userClick)="onSave()"
                >
                </ava-button>
              </ng-container>
            </div>
          </div>
        </div>
      </div>

      <div class="rightEnd-column" *ngIf="isExecuteMode && showChatInterface">
        <app-playground
          [promptOptions]="promptOptions"
          [messages]="chatMessages"
          [isLoading]="isProcessingChat"
          [showChatInteractionToggles]="false"
          [showAiPrincipleToggle]="true"
          (promptChange)="onPromptChanged($event)"
          (messageSent)="handleChatMessage($event)"
          (approvalRequested)="handleApproval()"
        ></app-playground>
      </div>
    </div>
  </form>
</div>
