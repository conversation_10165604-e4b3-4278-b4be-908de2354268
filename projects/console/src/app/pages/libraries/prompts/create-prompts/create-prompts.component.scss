// Layout for role and dropdown side by side
.role-dropdown-row {
  display: flex;
  gap: 16px;
  align-items: flex-end;
  margin-top: 8px;
}

// Match textbox and dropdown style
.role-dropdown-style {
  flex: 1 1 0;
}

.create-prompts-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: transparent;
}

.page-title {
  font-weight: 600;
  font-size: 20px;
}

.form-layout {
  display: flex;
  flex-direction: row;
  gap: 0;
  padding: 0;
  flex: 1;
  overflow: hidden;
  height: 100%;
  border: 1px solid #e1e4e8;
  background: #ffffff;
}

.left-column,
.middle-column,
.chat-column {
  padding: 20px;
  box-sizing: border-box;
}

.left-column {
  width: 40%;
  flex-shrink: 0;
  transition: width 0.3s ease;
  border-right: 1px solid #e1e4e8;

  @media (max-width: 1400px) {
    width: 40%;
  }

  @media (max-width: 1200px) {
    width: 100%;
  }

  app-card {
    flex-shrink: 0;
  }

  app-card:first-of-type {
    flex: 0 0 auto;
  }

  app-card:last-of-type {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .card-content {
      overflow-y: auto;
    }
  }
}

.middle-column {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-column {
  width: 400px;
  min-width: 400px;
  background-color: #f8f8f8;
  border-left: 1px solid #ddd;
  display: flex;
  flex-direction: column;
}

.prompt_header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.freeform-content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

::ng-deep ava-textarea.input-box {
  width: 100%;
  min-height: 200px;
  height: auto;
  resize: vertical;
}

:host ::ng-deep .ava-textarea--primary .ava-textarea__container {
  border-color: #e1e4e8 !important; // Your override color
}


@media (max-width: 600px) {
  ::ng-deep ava-textarea.input-box {
    min-height: 150px;
  }
}

@media (min-width: 1200px) {
  ::ng-deep ava-textarea.input-box {
    min-height: 250px;
  }
}

.regenerate-button-wrapper {
  display: flex;
  justify-content: flex-end; // <-- aligns button to the right
  margin-top: 12px;
}

// .regenerate-button {
//   display: inline-flex;
//   align-items: center;
//   gap: 0px;
//   border-radius: 6px;
//   border: none;
//   cursor: pointer;
// }

.role-textbox {
  width: 40%;
  flex-shrink: 0;

}

.fields-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.fields-row1 {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
  height: 10rem;
  border-bottom: 1px solid #e1e4e8;
}

.add-consideration {
  margin-top: 20px;
  height: 10rem;
  padding-top: 20px;
  border-top: 1px solid #e1e4e8;

}


.field-col {
  flex: 1 1 45%;
  min-width: 400px;
}

.optional-sections {
  margin-top: 30px;

  ava-accordion ::ng-deep .accordion-container .accordion-body {
    padding: 0%;
  }

  ava-accordion ::ng-deep .accordion-container .accordion-content {
    padding-top: 26px;
  }
}

.accordion-section {
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  margin-bottom: 20px;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 400px;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.playground-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
}

.chat-column-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.tabs-wrapper {
  display: flex;
  justify-content: center;
}

.tab-heading {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  text-align: center;
}

.tab-container {
  margin-bottom: 20px;
}

/* ✅ Custom Pill Tabs Styles */
.pill-tabs-container {
  display: inline-flex;
  border-radius: 999px;
  overflow: hidden;
  border: 1px solid #e1e4e8;
  background-color: #f8f9fa;
  padding: 4px;
  gap: 0;
}

.pill-tab-button {
  border-radius: 999px;
  margin: 0;
  border: none;
  font-weight: 500;
  padding: 10px 20px;
  background-color: transparent;
  color: #6c757d;
  transition: all 0.2s ease;
  cursor: pointer;
  font-size: 14px;
  min-width: 80px;
  text-align: center;
}

.pill-tab-button:hover:not(.active) {
  background-color: rgba(101, 102, 205, 0.05);
  color: #6566CD;
}

.pill-tab-button.active {
  background-color: #6566CD;
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(101, 102, 205, 0.3);
}

.item-icon {
  display: none !important; // Hide the icon
}

.example-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  .example-group {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: 1rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e1e4e8;
    position: relative;

    .field-col {
      flex: 1 1 45%;
    }

    .example-remove {
      margin-left: auto;
      margin-top: auto;
    }
  }

  .example-actions {
    display: flex;
    justify-content: flex-start; // Add Example left aligned
    margin-top: 1rem;
  }
}

.examples-accordion-wrapper ::ng-deep .accordion-header {
  min-width: 100% !important;
}

:host ::ng-deep ava-accordion .accordion-container {
  max-width: none !important;
  width: 100% !important; // reinforces full width
}

:host ::ng-deep ava-accordion .accordion-container .accordion-content {
  max-height: 100%;
  overflow-y: auto;
}

::ng-deep #description .ava-textarea__label {
  margin-top: 16px;
}