<p class="page-title">Prompt Creator 🛠️</p>
<div class="create-prompts-container">
  <form [formGroup]="promptForm">
    <div
      class="form-layout"
      [ngClass]="{ 'three-column-layout': isExecuteMode && showChatInterface }"
    >
      <!-- Left Column -->
      <div class="left-column">
        <div class="card-content">
          <ava-textbox
            [formControl]="getControl('name')"
            label="{{ promptLabels.promptName }}"
            id="promptName"
            name="promptName"
            placeholder="{{ promptLabels.pleaseEnterPromptName }} "
            [error]="getFieldError('name')"
            [required]="true"
          >
          </ava-textbox>

          <ava-textarea
            id="description"
            name="description"
            label="{{ promptLabels.description }}"
            [formControl]="getControl('promptDescription')"
            placeholder="{{ promptLabels.placeholderDescription }}"
            [rows]="6"
            size="md"
            [error]="getFieldError('promptDescription')"
            [required]="true"
          >
          </ava-textarea>
        </div>
      </div>

      <!-- Middle Column -->
      <div class="middle-column">
        <div class="prompt_header">
          <h3 class="section-title">{{ promptLabels.promptConfiguration }}</h3>
          <ava-button
            label="Save"
            variant="primary"
            size="medium"
            [customStyles]="{
              background:
                'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
              '--button-effect-color': '33, 90, 214',
            }"
            (userClick)="onSave()"
          >
          </ava-button>
        </div>

        <!-- Toggle Tabs -->
        <div class="tab-container">
          <h3 class="tab-heading">{{ promptLabels.chooseTheTypeOfPrompt }}</h3>
          <div class="tabs-wrapper">
            <div class="pill-tabs-container">
              <app-nav-item
                *ngFor="let tab of promptTabs"
                [label]="tab.label"
                [selected]="selectedTab === tab.value"
                [hasDropdown]="false"
                [dropdownOpen]="false"
                (selectEvent)="onTabSelected(tab.value)"
              >
              </app-nav-item>
            </div>
          </div>
        </div>

        <!-- Tab Content -->
        <div class="tab-content">
          <!-- Freeform Tab -->
          <ng-container *ngIf="selectedTab === 'freeform'">
            <div class="freeform-content-wrapper">
              <ava-textarea
                id="freeformPrompt"
                name="freeformPrompt"
                label="{{ promptLabels.freeformPrompt }}"
                [formControl]="getControl('promptTask')"
                placeholder="Please enter the prompt task"
                [rows]="8"
                size="md"
                [error]="getFieldError('promptTask')"
                [fullWidth]="true"
                [required]="true"
              >
              </ava-textarea>
            </div>
          </ng-container>

          <!-- Template Tab -->
          <ng-container *ngIf="selectedTab === 'template'">
            <div class="template-form">
              <div class="fields-row">
                <div class="field-col">
                  <div class="role-dropdown-row">
                    <ava-textbox
                      [formControl]="getControl('role')"
                      label="{{ promptLabels.role }}"
                      id="role"
                      name="role"
                      placeholder="{{ promptLabels.placeholderRole }}"
                      variant="primary"
                      size="md"
                      [error]="getFieldError('role')"
                      [required]="true"
                      class="role-textbox-flex role-dropdown-style"
                    >
                    </ava-textbox>
                    <ava-dropdown
                      formControlName="promptType"
                      label="Prompt Type"
                      id="promptType"
                      name="promptType"
                      [options]="promptTypeOptions"
                      [selectedValue]="selectedDropdownValue"
                      placeholder="Select Prompt Type"
                      [required]="true"
                      (selectionChange)="onPromptTypeChange($event)"
                      [disabled]="isViewMode"
                      class="prompt-type-dropdown-flex role-dropdown-style"
                    >
                    </ava-dropdown>
                  </div>
                </div>
              </div>
              <div class="fields-row">
                <div class="field-col">
                  <ava-textarea
                    [formControl]="getControl('goal')"
                    label="Goal"
                    id="goal"
                    name="{{ promptLabels.goal }}"
                    placeholder="{{ promptLabels.placeholderGoal }}"
                    size="md"
                    [rows]="4"
                    [error]="getFieldError('goal')"
                    [fullWidth]="true"
                    [required]="true"
                  >
                  </ava-textarea>
                </div>
                <div class="field-col">
                  <ava-textarea
                    id="templateDescription"
                    name="templateDescription"
                    label="{{ promptLabels.description }}"
                    [formControl]="getControl('description')"
                    placeholder="{{ promptLabels.placeholderDescription }}"
                    [rows]="4"
                    size="md"
                    [fullWidth]="true"
                    [error]="getFieldError('description')"
                    [required]="true"
                  >
                  </ava-textarea>
                </div>
              </div>

              <div class="fields-row">
                <div class="field-col">
                  <ava-textarea
                    id="backstory"
                    name="backstory"
                    label="{{ promptLabels.backstory }}"
                    [formControl]="getControl('backstory')"
                    placeholder="{{ promptLabels.placeholderBackStory }}"
                    [rows]="4"
                    size="md"
                    [fullWidth]="true"
                    [error]="getFieldError('backstory')"
                    [required]="true"
                  >
                  </ava-textarea>
                </div>
                <div class="field-col">
                  <ava-textarea
                    [formControl]="getControl('expectedOutput')"
                    label="{{ promptLabels.expectedOutput }}"
                    id="expectedOutput"
                    name="expectedOutput"
                    placeholder="{{ promptLabels.placeholderExpectedOutput }}"
                    size="md"
                    [rows]="4"
                    [fullWidth]="true"
                    [error]="getFieldError('expectedOutput')"
                    [required]="true"
                  >
                  </ava-textarea>
                </div>
              </div>
              <div
                class="fields-row"
                *ngIf="
                  getControl('promptType').value &&
                  getControl('promptType').value.toLowerCase() ===
                    'chain of thought'
                "
              >
                <div class="field-col">
                  <ava-textarea
                    id="intermediateSteps"
                    name="intermediateSteps"
                    label="{{ promptLabels.intermediateSteps }}"
                    [formControl]="getControl('intermediateSteps')"
                    placeholder="{{
                      promptLabels.placeholderIntermediateSteps
                    }}"
                    [rows]="4"
                    size="md"
                    [fullWidth]="true"
                    [error]="getFieldError('intermediateSteps')"
                    [required]="true"
                  >
                  </ava-textarea>
                </div>
              </div>
            </div>
          </ng-container>
          <div class="regenerate-button-wrapper">
            <ava-button
              label="Regenerate"
              variant="secondary"
              size="medium"
              iconName="rotate-cw"
              iconPosition="right"
              [customStyles]="{
                'border': '2px solid transparent',
                'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                'background-origin': 'border-box',
                'background-clip': 'padding-box, border-box',
                '--button-effect-color': '33, 90, 214'
              }">
              (userClick)="regenerate()"
            >
            </ava-button>
          </div>
        </div>

        <!-- Optional Sections -->
        <div class="optional-sections" *ngIf="selectedTab === 'template'">
          <div
            class="examples-accordion-wrapper"
            *ngIf="
              !getControl('promptType').value ||
              getControl('promptType').value.toLowerCase() !== 'zero shot'
            "
          >
            <ava-accordion
              #accordion
              [expanded]="false"
              [animation]="true"
              [controlled]="false"
              iconClosed="chevron-down"
              iconOpen="chevron-up"
              iconPosition="right"
              type="default"
            >
              <div header>
                {{ promptLabels.addExamples }}
              </div>
              <div content>
                <div class="example">
                  <div class="example-content" formArrayName="examples">
                    <div
                      class="example-group"
                      *ngFor="let example of examples.controls; let i = index"
                      [formGroupName]="i"
                    >
                      <div class="field-col">
                        <ava-textarea
                          [formControlName]="'input'"
                          label="Input"
                          name="{{ promptLabels.input }}"
                          id="input"
                          placeholder="{{ promptLabels.placeholderInput }}"
                          variant="primary"
                          [rows]="2"
                          size="md"
                          [fullWidth]="true"
                        >
                        </ava-textarea>
                      </div>
                      <div class="field-col">
                        <ava-textarea
                          [formControlName]="'output'"
                          label="Output"
                          name="{{ promptLabels.output }}"
                          id="output"
                          placeholder="{{ promptLabels.placeholderOutput }}"
                          variant="primary"
                          [rows]="2"
                          size="md"
                          [fullWidth]="true"
                        >
                        </ava-textarea>
                      </div>
                      <!-- Remove button down and right-aligned -->
                      <div class="example-remove" *ngIf="i > 0">
                        <ava-button
                          label="{{ promptLabels.remove }}"
                          variant="secondary"
                          size="medium"
                          iconName="trash"
                          iconPosition="left"
                          (userClick)="removeExample(i)"
                        >
                        </ava-button>
                      </div>
                    </div>
                    <!-- Add button aligned to the left below the example group -->
                    <div class="example-actions">
                      <ava-button
                        label="{{ promptLabels.addExamples }}"
                        variant="secondary"
                        size="medium"
                        iconName="plus"
                        iconPosition="right"
                        (userClick)="addExample()"
                      >
                      </ava-button>
                    </div>
                  </div>
                  <div class="add-consideration">
                    <ava-textarea
                      [formControl]="getControl('addConsideration')"
                      label="{{ promptLabels.additionalConsideration }}"
                      id="addConsideration"
                      name="addConsideration"
                      placeholder="{{
                        promptLabels.placeholderAdditionalConsideration
                      }}"
                      variant="primary"
                      [rows]="2"
                      size="md"
                      [fullWidth]="true"
                    >
                    </ava-textarea>
                  </div>
                </div>
              </div>
            </ava-accordion>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>
<ava-popup
  [show]="showSuccessPopup"
  [title]="popupTitle"
  [message]="popupMessage"
  [showHeaderIcon]="true"
  headerIconName="{{ iconName }}"
  iconColor="#28a745"
  [showClose]="true"
  [showCancel]="false"
  [confirmButtonVariant]="'primary'"
  [confirmButtonBackground]="'#28a745'"
  (confirm)="onSuccessConfirm()"
  (closed)="closeSuccessPopup()"
>
</ava-popup>
