{"labels": {"promptName": "Prompt Name", "description": "Description", "remove": "Remove", "assignFilters": "Assign <PERSON>s", "organization": "Organization", "domain": "Domain", "project": "Project", "team": "Team", "analyze": "Analyze", "role": "Role", "goal": "Goal", "backstory": "Backstory", "expectedOutput": "Expected Output", "intermediateSteps": "Intermediate Steps", "input": "Input", "output": "Output", "exit": "Exit", "save": "Save", "execute": "Execute", "update": "Update", "playGround": "Playground", "promptTesting": "Prompt testing", "instruction": "Instruction", "filters": "Filters", "loadingText": "Loading Prompts...", "createPrompt": "Create Prompt", "additionalConsideration": "Additional Consideration", "promptPurposeLabel": "What do you want the Prompt to do?", "uploadAttachmentLabel": "Attach a file or document", "placeholder": "Please describe what you want the prompt to do...", "addExamples": "Add Examples", "noResults": "No prompts found matching your criteria", "promptConfiguration": "Prompt Configuration", "freeformPrompt": "Freeform Prompt", "chooseTheTypeOfPrompt": "Choose the Type of Prompt", "pleaseEnterPromptName": "Please enter the prompt name", "placeholderDescription": "Please enter the description", "placeholderRole": "Please enter the role", "placeholderGoal": "Please enter the goal", "placeholderBackStory": "Please enter the backstory", "placeholderExpectedOutput": "Please enter the expected output", "placeholderIntermediateSteps": "Please enter the intermediateSteps", "placeholderInput": "Please enter the input", "placeholderOutput": "Please enter the output", "placeholderAdditionalConsideration": "Please enter the additional consideration"}}