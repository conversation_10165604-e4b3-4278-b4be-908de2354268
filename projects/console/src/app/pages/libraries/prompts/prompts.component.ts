import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import {
  takeUntil,
  of,
  map,
  catchError,
  Subject,
  debounceTime,
  distinctUntilChanged,
  startWith,
} from 'rxjs';
import { CardData, CardTag } from '../../../shared/models/card.model';
import { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';
import { PaginationService } from '../../../shared/services/pagination.service';
import { PromptsService } from '../../../shared/services/prompts.service';
import { formatToDisplayDate } from '../../../shared/utils/date-utils';
import promptsLabels from './constants/prompts.json';
import { PROMPTS_BASE_ACTIONS } from './prompts-actions';
import {
  AvaTextboxComponent,
  TextCardComponent,
  DropdownComponent,
  DropdownOption,
  IconComponent,
  PopupComponent,
} from '@ava/play-comp-library';
import { LucideAngularModule } from 'lucide-angular';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-prompts',
  standalone: true,
  imports: [
    CommonModule,
    PageFooterComponent,
    TextCardComponent,
    AvaTextboxComponent,
    DropdownComponent,
    LucideAngularModule,
    IconComponent,
    PopupComponent,
    ReactiveFormsModule,
  ],
  templateUrl: './prompts.component.html',
  styleUrl: './prompts.component.scss',

})

export class PromptsComponent implements OnInit {
  // Popup state for success messages
  showSuccessPopup = false;
  popupMessage = '';
  popupTitle = '';
  iconName = 'info';
  submissionSuccess = false;
  // popup for delete confirmation
  showDeletePopup: boolean = false;
  promptToDelete: CardData | null = null;

  simpleOptions: DropdownOption[] = [
    { name: 'Option 1', value: '1' },
    { name: 'Option 2', value: '2' },
    { name: 'Option 3', value: '3' },
    { name: 'Option 4', value: '4' },
    { name: 'Option 5', value: '5' },
  ];
  iconList = [
    { name: 'trash', iconName: 'trash-2', cursor: true },
    { name: 'edit', iconName: 'edit', cursor: true },
    { name: 'eye', iconName: 'eye', cursor: true },
  ];
  public promptLabels = promptsLabels.labels;
  allPrompts: CardData[] = [];
  filteredPrompts: CardData[] = [];
  displayedPrompts: CardData[] = [];
  searchForm!: FormGroup;
  isLoading = false;
  currentPage: number = 1;
  itemsPerPage: number = 11;
  totalPages: number = 1;
  protected destroy$ = new Subject<void>();
  selectedData: any = null;
  cardSkeletonPlaceholders = Array(11);

  constructor(
    private paginationService: PaginationService,
    private promptsService: PromptsService,
    private router: Router,
    private route: ActivatedRoute,
    private fb: FormBuilder,
  ) {
    this.searchForm = this.fb.group({
      search: [''],
    });
  }

  ngOnInit(): void {
    this.isLoading = true;
    this.initSearchListener();
    this.promptsService
      .fetchAllPrompts()
      .pipe(
        takeUntil(this.destroy$),
        map(this.transformResponseToCardData.bind(this)),
        catchError((error) => {
          console.error('Error fetching prompts:', error);
          this.isLoading = false;
          return of({ prompts: [] });
        }),
      )
      .subscribe({
        next: ({ prompts }) => {
          this.allPrompts = prompts;
          this.filteredPrompts = [...prompts];
          this.updateDisplayedPrompts();
          this.setInitialPageFromQueryParam();
        },
        error: (err) => console.error('Subscription error:', err.message),
        complete: () => {
          this.isLoading = false;
        },
      });
  }

  onSelectionChange(data: any) {
    this.selectedData = data;
  }

  onCreatePrompt(): void {
    this.router.navigate(['/libraries/prompts/create']);
  }

  getHeaderIcons(prompt: any): { iconName: string; title: string }[] {
    return [
      { iconName: 'wrench', title: prompt.toolType || 'Prompt' },
      { iconName: 'users', title: `${prompt.userCount || 30}` },
    ];
  }

  getFooterIcons(prompt: any): { iconName: string; title: string }[] {
    return [
      { iconName: 'user', title: prompt.owner || 'AAVA' },
      { iconName: 'calendar-days', title: prompt.createdDate },
    ];
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedPrompts();
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { page: this.currentPage },
      queryParamsHandling: 'merge',
    });
  }

  onIconClicked(icon: any, promptId: string): void {
    switch (icon.name) {
      case 'trash':
        this.deletePrompt(promptId);
        break;
      case 'edit':
        this.executePrompt(promptId);
        break;
      case 'eye':
        this.viewPrompt(promptId);
        break;
      default:
        console.log('Unknown icon action:', icon.name);
    }}

  private viewPrompt(promptId: string): void {
    this.router.navigate(['/libraries/prompts/edit', promptId], {
      queryParams: { view: 'true', returnPage: this.currentPage },
    });
  }
  

  private transformResponseToCardData(
    response: CardData[] | { prompts: CardData[] },
  ): { prompts: (CardData & { tagSummary: string })[] } {
    const prompts = 'prompts' in response ? response.prompts : response;
    return {
      prompts: prompts.map(this.formatPromptCard.bind(this)),
    };
  }

  private formatPromptCard(item: any): CardData & { tagSummary: string } {
    const {
      name,
      updatedAt,
      categoryName,
      domainName,
      tags = [],
      ...rest
    } = item;
    const customTags = this.getCustomTags(categoryName, domainName);
    const allTags = [...tags, ...customTags];
    const tagSummary = this.getTagSummary(allTags);
    const createdDate = formatToDisplayDate(updatedAt);
    return {
      title: name,
      createdDate,
      tags: allTags,
      tagSummary,
      actions: PROMPTS_BASE_ACTIONS,
      ...rest,
    };
  }

  // Step 1: User clicks trash icon → open delete confirmation
private deletePrompt(promptId: string): void {
  const prompt = this.allPrompts.find(p => p.id === promptId);
  if (!prompt) return;

  this.promptToDelete = prompt;
  this.showDeletePopup = true;
}

// Step 2: User confirms delete in popup
onConfirmDelete(): void {
  if (!this.promptToDelete?.id) return;

  const promptId = this.promptToDelete.id;

  this.promptsService.deletePrompt(promptId).subscribe({
    next: (res) => {
      if (res && res.success !== false) {
        // Update local prompt lists
        this.allPrompts = this.allPrompts.filter(p => p.id !== promptId);
        this.filteredPrompts = this.filteredPrompts.filter(p => p.id !== promptId);
        this.updateDisplayedPrompts();

        // Show success popup
        this.iconName = 'check-circle';
        this.popupTitle = 'Success';
        this.popupMessage = 'Prompt deleted successfully.';
        this.submissionSuccess = true;
        this.showSuccessPopup = true;
      } else {
        this.iconName = 'alert-circle';
        this.popupTitle = 'Error';
        this.popupMessage = 'Failed to delete prompt.';
        this.submissionSuccess = false;
        this.showSuccessPopup = true;
      }

      this.closeDeletePopup();
    },
    error: (err) => {
      console.error('Failed to delete prompt:', err);
      this.iconName = 'alert-circle';
      this.popupTitle = 'Error';
      this.popupMessage = 'An unexpected error occurred.';
      this.submissionSuccess = false;
      this.showSuccessPopup = true;
      this.closeDeletePopup();
    },
  });
}

// Step 3: User cancels or closes delete popup
closeDeletePopup(): void {
  this.showDeletePopup = false;
  this.promptToDelete = null;
}

// Success popup confirm handler
onSuccessConfirm(): void {
  this.closeSuccessPopup();
}

// Close success popup manually or when user clicks close icon
closeSuccessPopup(): void {
  this.showSuccessPopup = false;
  this.popupTitle = '';
  this.popupMessage = '';
  this.iconName = 'info';
}


  private executePrompt(promptId: string): void {
    this.router.navigate(['/libraries/prompts/edit', promptId], {
      queryParams: { execute: 'true', returnPage: this.currentPage },
    });
  }

  private async copyPrompt(promptId: string): Promise<void> {
    if (!promptId) return;
    const prompt = this.allPrompts.find(p => p.id === promptId);
    if (prompt) {
      try {
        await navigator.clipboard.writeText(JSON.stringify(prompt, null, 2));
      } catch (err) {
        // Optionally handle error silently
      }
    }
  }

  private getCustomTags(categoryName?: string, domainName?: string): CardTag[] {
    const tags: CardTag[] = [];
    if (categoryName) tags.push({ label: categoryName, type: 'primary' });
    if (domainName) tags.push({ label: domainName, type: 'secondary' });
    return tags;
  }

  private getTagSummary(tags: CardTag[]): string {
    return tags.map((tag) => tag.label).join(', ');
  }

  private setInitialPageFromQueryParam(): void {
    const pageParam = this.route.snapshot.queryParamMap.get('page');
    if (pageParam) {
      const page = parseInt(pageParam, 10);
      if (!isNaN(page)) this.currentPage = page;
    }
  }

  private initSearchListener(): void {
    this.searchForm
      .get('search')!
      .valueChanges.pipe(
        startWith(''),
        debounceTime(300),
        distinctUntilChanged(),
        map((value) => value?.toLowerCase() ?? ''),
        takeUntil(this.destroy$),
      )
      .subscribe((searchText) => {
        this.filterPrompts(searchText);
      });
  }

  private updateDisplayedPrompts(): void {
    this.itemsPerPage = this.currentPage === 1 ? 12 : 11;
    const { displayedItems, totalPages } =
      this.paginationService.getPaginatedItems(
        this.filteredPrompts,
        this.currentPage,
        this.itemsPerPage,
      );
    this.displayedPrompts = displayedItems;
    this.totalPages = totalPages;
  }

  private filterPrompts(searchText: string): void {
    this.filteredPrompts = this.allPrompts.filter((prompt) => {
      const titleMatch = prompt.title?.toLowerCase().includes(searchText);
      const descriptionMatch = prompt.description
        ?.toLowerCase()
        .includes(searchText);
      const tagMatch = prompt.tags?.some((tag) =>
        tag.label?.toLowerCase().includes(searchText),
      );
      return titleMatch || descriptionMatch || tagMatch;
    });
    this.currentPage = 1;
    this.updateDisplayedPrompts();
  }
}
