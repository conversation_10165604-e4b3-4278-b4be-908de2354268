{"labels": {"guardrailName": "Guardrail Name", "gnPlacholder": "Guardrail One", "description": "Description", "gdPlaceholder": "Enter the description", "searchPlaceholder": "Search 'Guardrails'", "dropdownTitle": "choose guardrail", "title": "Create Guardrail", "text": "No guardrails found matching your criteria", "deleteTitle": "Delete Guardrail", "sectionTitle": "Guardrail Configuration", "pageTitle": "Guardrail Creator", "guardrailConfiguration": "Guardrail Configuration", "placeholderGuardrail": "Enter Guardrail Name", "assignFilters": "Assign <PERSON>s", "organization": "Organization", "domain": "Domain", "project": "Project", "team": "Team", "codeFormat": "Code Format", "colang": "Colang", "yml": "YML", "yaml": "YAML", "codeContent": "Code Content", "yamlContent": "YAML Content", "configKey": "Config Key", "chatBot": "<PERSON><PERSON>", "guardrailType": "Guardrail Type", "action": "Action", "loadingText": "Loading guardrail data...", "createGuardrail": "Create Guardrail", "updateGuardrail": "Update Guardrail", "saveGuardrail": "Save Guardrail", "executeGuardrail": "Execute Guardrail", "testGuardrail": "Test Guardrail", "exit": "Exit", "update": "Update", "save": "Save", "execute": "Execute", "test": "Test", "validate": "Validate", "compile": "Compile", "noResults": "No guardrails found matching your criteria", "noGuardrailsUploaded": "No guardrails created yet.", "playground": "Playground", "guardrailTesting": "Guardrail Testing", "guardrailTestingPrompt": "Guardrail testing prompt", "chatInterface": "Chat Interface", "validationOutput": "Validation Output", "toolCompiler": "<PERSON><PERSON> Compiler", "guardrailEditor": "Guardrail Editor", "colangEditor": "Colang Editor", "yamlEditor": "YAML Editor", "codeEditor": "Code Editor", "enableGuardrails": "Enable Guardrails", "guardrailEnabled": "Guardrail Enabled", "guardrailDisabled": "Guardrail Disabled", "guardrailStatus": "Guardrail Status", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "success": "Success", "error": "Error", "warning": "Warning", "info": "Info", "placeholderDescription": "Enter guardrail description", "placeholderOrganization": "Enter organization", "placeholderDomain": "Enter domain", "placeholderProject": "Enter project", "placeholderTeam": "Enter team", "placeholderConfigKey": "Enter config key", "placeholderColangCode": "Write your Colang code here...", "placeholderYamlCode": "Write your YAML code here...", "placeholderCodeContent": "Write your code here...", "placeholderTestPrompt": "Enter test prompt", "placeholderChatMessage": "Type your message...", "guardrailValidationSuccess": "Guardrail validation successful", "guardrailValidationError": "Guardrail validation failed", "guardrailSaveSuccess": "Guardrail saved successfully", "guardrailSaveError": "Failed to save guardrail", "guardrailUpdateSuccess": "Guardrail updated successfully", "guardrailUpdateError": "Failed to update guardrail", "guardrailDeleteSuccess": "Guardrail deleted successfully", "guardrailDeleteError": "Failed to delete guardrail", "guardrailExecuteSuccess": "Guardrail executed successfully", "guardrailExecuteError": "Failed to execute guardrail", "invalidGuardrailFormat": "Invalid guardrail format", "missingRequiredFields": "Please fill in all required fields", "guardrailNameRequired": "Guardrail name is required", "descriptionRequired": "Description is required", "codeContentRequired": "Code content is required", "configKeyRequired": "Config key is required", "organizationRequired": "Organization is required", "guardrailNameMinLength": "Guardrail name must be at least 3 characters", "guardrailNameMaxLength": "Guardrail name cannot exceed 50 characters", "descriptionMaxLength": "Description cannot exceed 500 characters", "codeContentMaxLength": "Code content cannot exceed 10000 characters", "yamlContentMaxLength": "YAML content cannot exceed 10000 characters", "invalidYamlFormat": "Invalid YAML format", "invalidColangFormat": "Invalid Colang format", "guardrailTestingInProgress": "Guardrail testing in progress...", "guardrailTestingComplete": "Guardrail testing complete", "guardrailTestingFailed": "Guardrail testing failed", "testResultsAvailable": "Test results are available", "noTestResults": "No test results available", "clearTestResults": "Clear Test Results", "downloadTestResults": "Download Test Results", "exportGuardrail": "Export Guardrail", "importGuardrail": "Import Guardrail", "cloneGuardrail": "Clone Guardrail", "deleteGuardrail": "Delete Guardrail", "confirmDelete": "Are you sure you want to delete this guardrail?", "confirmExit": "Are you sure you want to exit? Unsaved changes will be lost.", "unsavedChanges": "You have unsaved changes", "saveChangesPrompt": "Do you want to save your changes before leaving?", "discardChanges": "Discard Changes", "saveAndExit": "Save and Exit", "continueEditing": "Continue Editing", "guardrailLibrary": "Guardrail Library", "myGuardrails": "My Guardrails", "sharedGuardrails": "Shared Guardrails", "recentGuardrails": "Recent Guardrails", "popularGuardrails": "Popular Guardrails", "guardrailTemplates": "Guardrail Templates", "createFromTemplate": "Create from Template", "useTemplate": "Use Template", "customGuardrail": "Custom Guardrail", "predefinedGuardrail": "Predefined Guardrail", "guardrailCategory": "Guardrail Category", "securityGuardrail": "Security Guardrail", "complianceGuardrail": "Compliance Guardrail", "performanceGuardrail": "Performance Guardrail", "qualityGuardrail": "Quality Guardrail", "businessGuardrail": "Business Guardrail", "technicalGuardrail": "Technical Guardrail", "operationalGuardrail": "Operational Guardrail", "riskGuardrail": "Risk Guardrail", "dataGuardrail": "Data Guardrail", "privacyGuardrail": "Privacy Guardrail", "ethicalGuardrail": "Ethical Guardrail", "regulatoryGuardrail": "Regulatory Guardrail", "guardrailSeverity": "Guardrail Severity", "critical": "Critical", "high": "High", "medium": "Medium", "low": "Low", "guardrailPriority": "Guardrail Priority", "urgent": "<PERSON><PERSON>", "normal": "Normal", "defer": "Defer", "guardrailScope": "Guardrail Scope", "global": "Global", "regional": "Regional", "local": "Local", "departmental": "Departmental", "projectSpecific": "Project Specific", "teamSpecific": "Team Specific", "userSpecific": "User Specific", "guardrailVersion": "Guardrail Version", "versionHistory": "Version History", "currentVersion": "Current Version", "latestVersion": "Latest Version", "previousVersion": "Previous Version", "versionNotes": "Version Notes", "releaseNotes": "Release Notes", "changeLog": "Change Log", "guardrailMetrics": "Guardrail Metrics", "executionCount": "Execution Count", "successRate": "Success Rate", "failureRate": "Failure Rate", "averageExecutionTime": "Average Execution Time", "lastExecuted": "Last Executed", "createdBy": "Created By", "modifiedBy": "Modified By", "createdDate": "Created Date", "modifiedDate": "Modified Date", "lastModified": "Last Modified", "guardrailOwner": "Guardrail Owner", "guardrailMaintainer": "Guardrail Maintainer", "guardrailReviewer": "Guardrail Reviewer", "guardrailApprover": "Guardrail Approver"}}