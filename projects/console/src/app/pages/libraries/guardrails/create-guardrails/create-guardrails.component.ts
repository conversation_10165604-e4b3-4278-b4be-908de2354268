import { Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  FormControl,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { ChatMessage } from '../../../../shared/components/chat-window/chat-window.component';
import { ToolExecutionService } from '../../../../shared/services/tool-execution/tool-execution.service';
import { Subscription } from 'rxjs';
import { PlaygroundComponent } from 'projects/console/src/app/shared/components/playground/playground.component';
import {
  AvaTextareaComponent,
  AvaTextboxComponent,
  ButtonComponent,
  DropdownOption,
  PopupComponent,
} from '@ava/play-comp-library';
import {
  CodeEditorComponent,
  CodeLanguage,
  CodeEditorTheme,
} from '../../../../shared/components/code-editor/code-editor.component';
import { PromptEnhanceService } from 'projects/console/src/app/shared/services/prompt-enhance.service';
import { GuardrailsService } from 'projects/console/src/app/shared/services/guardrails.service';
import { Guardrail } from 'projects/console/src/app/shared/models/card.model';
import { NavItemComponent } from 'projects/console/src/app/shared/components/nav-item/nav-item.component';
import guardrailsLabels from '../constants/guardrails-base.json';

@Component({
  selector: 'app-create-guardrails',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    PlaygroundComponent,
    AvaTextboxComponent,
    ButtonComponent,
    AvaTextareaComponent,
    CodeEditorComponent,
    PopupComponent
  ],
  templateUrl: './create-guardrails.component.html',
  styleUrls: ['./create-guardrails.component.scss'],
})
export class CreateGuardrailsComponent implements OnInit, OnDestroy {
  @ViewChild(CodeEditorComponent) codeEditor!: CodeEditorComponent;

  // Labels from constants file
  grLabels = guardrailsLabels.labels;

  // Mode flags
  guardrailId: string | null = null;
  isEditMode: boolean = false;
  isExecuteMode: boolean = false;
  showChatInterface: boolean = false;

  guardrailForm: FormGroup;

  // Code format options
  codeFormats: string[] = ['Colang', 'YML'];
  selectedCodeFormat: string = 'Colang';

  // Chat interface properties
  chatMessages: ChatMessage[] = [];
  isProcessingChat: boolean = false;

  // Subscription
  private executionSubscription: Subscription = new Subscription();
  public validationOutput: string = '';
  public showValidationOutput: boolean = false;
  public validationOutputEditorConfig = {
    title: 'Tool Compiler',
    language: 'json' as CodeLanguage,
    theme: 'light' as CodeEditorTheme,
    readOnly: true,
    height: '250px',
  };

  codeFormatTabs = [
    { label: 'Colang', value: 'colang' },
    { label: 'YML', value: 'yml' },
  ];
  selectedTab: string = 'colang';

  private rawColangContent: string = '';
  private rawYamlContent: string = '';

  // For shared save/update popup
  showConfirmPopup: boolean = false;
  confirmPopupTitle: string = '';
  confirmPopupMessage: string = '';
  confirmButtonLabel: string = '';
  confirmButtonColor: string = '';
  actionType: 'save' | 'update' | null = null;
  // Success Popup for Save/Update
  showInfoPopup: boolean = false;
  infoMessage: string = '';
  public labels: any = guardrailsLabels.labels;
  
  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private toolExecutionService: ToolExecutionService,
    private promptGenerateService: PromptEnhanceService,
    private guardrailsService: GuardrailsService,
  ) {
    this.guardrailForm = this.fb.group({
      // Guardrail details
      name: [''],
      description: [''],

      // Filters
      organization: [''],
      domain: [''],
      project: [''],
      team: [''],
      // Code content
      codeContent: [''],
      yamlContent: [''],

      // codeContent: ['<!DOCTYPE html>\n<html lang="en">\n<head>\n  <meta charset="UTF-8">\n  <meta name="viewport" content="width=device-width, initial-scale=1">\n  <title>Agentic Activity Log</title>\n  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">\n</head>\n<body>\n\n<div class="container mt-5">\n  <h2 class="text-center mb-4">Agentic Activity Log</h2>\n  \n  <div class="card shadow-lg">\n    <div class="card-body">\n      <h5 class="card-title">Lead Qualification Agent</h5>\n      <p><strong>Execution ID:</strong> LQ-20250323-001</p>\n      <p><strong>Status:</strong> <span class="badge bg-success">Completed</span></p>\n      \n      <table class="table table-bordered mt-3">\n        <thead class="table-dark">\n          <tr>\n            <th>Step</th>\n            <th>Details</th>\n            <th>Time</th>']
    });
  }
  // Define language mapping (Colang and YML can be treated as plaintext or yaml depending on real syntax support)
  codeLanguagesMap: Record<string, CodeLanguage> = {
    Colang: 'plaintext',
    YML: 'yaml',
  };
  get currentCodeControl(): FormControl {
    return this.selectedCodeFormat === 'YML'
      ? (this.guardrailForm.get('yamlContent') as FormControl)
      : (this.guardrailForm.get('codeContent') as FormControl);
  }

  ngOnInit(): void {
    // Check if we're in edit mode
    this.guardrailId = this.route.snapshot.paramMap.get('id');
    const executeParam = this.route.snapshot.queryParamMap.get('execute');

    this.isEditMode = !!this.guardrailId;
    this.isExecuteMode = executeParam === 'true';
    this.showChatInterface = this.isExecuteMode;

    if (this.isEditMode && this.guardrailId) {
      console.log('iseditmode', this.isEditMode);
      // Load guardrail data for editing
      console.log(`Editing guardrail with ID: ${this.guardrailId}`);
      this.loadGuardrailData(this.guardrailId);

      // If in execute mode, start the execution
      if (this.isExecuteMode) {
        // Initialize messages
        this.chatMessages = [
          {
            from: 'ai',
            text: 'Hi Akash, this is the guardrail testing',
          },
          {
            from: 'user',
            text: 'Test this input',
          },
          {
            from: 'ai',
            text: 'Here is the output',
          },
        ];

        // Start execution (after a small delay to ensure UI is ready)
        setTimeout(() => {
          this.toolExecutionService.startExecution(
            this.guardrailId!,
            this.chatMessages,
          );

          // Subscribe to execution state changes
          this.executionSubscription = this.toolExecutionService
            .getExecutionState()
            .subscribe((state) => {
              if (state.isExecuting && state.toolId === this.guardrailId) {
                this.chatMessages = state.chatMessages;
              }
            });
        }, 100);
      }
    }
  }

  ngOnDestroy(): void {
    // Clean up subscription
    if (this.executionSubscription) {
      this.executionSubscription.unsubscribe();
    }
  }

  onSave(): void {
    const name = this.guardrailForm.get('name')?.value;
    const description = this.guardrailForm.get('description')?.value;
    const content = this.guardrailForm.get('codeContent')?.value;
    const organization =
      this.guardrailForm.get('organization')?.value || 'Ascendion';
    const configKey = this.guardrailForm.get('configKey')?.value;
    const yamlContent = this.guardrailForm.get('yamlContent')?.value;
    const chatBot = this.guardrailForm.get('chatBot')?.value;

    const payload = {
      name,
      description,
      content,
      organization,
      configKey,
      yamlContent,
      chatBot,
    };
    if (this.guardrailForm.invalid) {
      this.guardrailForm.markAllAsTouched();
      return;
    }
    if (this.isEditMode && this.guardrailId) {
      this.guardrailsService
        .updateGuardrail({ id: Number(this.guardrailId), ...payload })
        .subscribe({
          next: () =>{
          this.infoMessage = `Guardrail "${payload.name}" has been successfully updated.`;
          this.showInfoPopup = true;
          },
          error: (err) => console.error('Update failed', err),
        });
    } else {
      this.guardrailsService.addGuardrail(payload).subscribe({
        next: () => {
        this.infoMessage = `Guardrail "${payload.name}" has been successfully created.`;
        this.showInfoPopup = true;
      },
        error: (err) => console.error('Create failed', err),
      });
    }
  }
  handleInfoPopup(): void {
  this.showInfoPopup = false;
  this.router.navigate(['/libraries/guardrails']);
}

  onExecute(): void {
    console.log('Executing guardrail:', this.guardrailForm.value);
    if (this.guardrailId) {
      // If we're already in execute mode with chat interface showing
      if (this.isExecuteMode && this.showChatInterface) {
        // Process the execution
        console.log('Processing execution');
      } else {
        console.log('Entering execute mode, showing chat interface');

        // Set flags to show chat interface
        this.isExecuteMode = true;
        this.showChatInterface = true;

        // Set the initial messages
        this.chatMessages = [
          {
            from: 'ai',
            text: 'Hi Akash, this is the guardrail testing',
          },
          {
            from: 'user',
            text: 'Test this input',
          },
          {
            from: 'ai',
            text: 'Here is the output',
          },
        ];

        // Delay starting the execution service slightly to allow UI to update
        setTimeout(() => {
          console.log(
            'Starting execution service for guardrail ID:',
            this.guardrailId,
          );
          this.toolExecutionService.startExecution(
            this.guardrailId!,
            this.chatMessages,
          );
        }, 100);
      }
    }
  }

  onExit(): void {
    // If we're in execute mode with chat interface showing
    if (this.isExecuteMode && this.isEditMode) {
      // Return to edit mode without chat interface
      this.isExecuteMode = false;
      this.showChatInterface = false;
      this.toolExecutionService.stopExecution();
      console.log('Exited execution mode, returning to edit mode');
    } else {
      // Get the return page if available
      const returnPage = this.route.snapshot.queryParamMap.get('returnPage');
      const pageNumber = returnPage ? parseInt(returnPage) : 1;

      // Exit to guardrails list at correct page
      this.router.navigate(['/libraries/guardrails'], {
        queryParams: { page: pageNumber },
      });
    }
  }

  // Helper method to get form controls easily from the template
  getControl(name: string): FormControl {
    return this.guardrailForm.get(name) as FormControl;
  }

  // Method to change the selected code format
  setCodeFormat(format: string): void {
    // Save the current editor content into the appropriate form control
    const currentEditorContent = this.codeEditor.getValue();
    if (this.selectedCodeFormat === 'Colang') {
      this.rawColangContent = currentEditorContent;
      this.guardrailForm.get('codeContent')?.setValue(currentEditorContent);
    } else {
      this.rawYamlContent = currentEditorContent;
      this.guardrailForm.get('yamlContent')?.setValue(currentEditorContent);
    }

    // Change the format
    this.selectedCodeFormat = format;

    // Get the new content for the selected format
    const newContent =
      format === 'Colang' ? this.rawColangContent : this.rawYamlContent;

    // Update the editor value
    setTimeout(() => {
      this.codeEditor.setValue(newContent || '');
    });
  }
  // Load guardrail data from mock data
  loadGuardrailData(guardrailId: string): void {
    this.guardrailsService.getGuardrailById(Number(guardrailId)).subscribe({
      next: (guardrail: Guardrail) => {
        if (!guardrail) {
          console.error('No guardrail data found for ID:', guardrailId);
          return;
        }

        // Set raw contents
        this.rawColangContent = guardrail.content || '';
        this.rawYamlContent = guardrail.yamlContent || '';

        // Patch form values
        this.guardrailForm.patchValue({
          name: guardrail.name || '',
          description: guardrail.description || '',
          organization: guardrail.organization || 'Ascendion',
          configKey: guardrail.configKey || '',
          chatBot: guardrail.chatBot || false,
          codeContent: this.rawColangContent,
          yamlContent: this.rawYamlContent,
        });

        // Show initial editor content based on selected Tab
        const initialContent =
          this.selectedCodeFormat === 'Colang'
            ? this.rawColangContent
            : this.rawYamlContent;

        // Set editor content
        if (this.codeEditor) {
          this.codeEditor.setValue(initialContent || '');
        }
      },
      error: (err) => {
        console.error('Failed to load guardrail data:', err);
      },
    });
  }

  private tryParseJSONString(str: string): string {
    try {
      const parsed = JSON.parse(str);
      return typeof parsed === 'string' ? parsed : str;
    } catch {
      return str;
    }
  }

  private addChatMessage(from: 'ai' | 'user', text: string): void {
    this.chatMessages = [...this.chatMessages, { from, text }];
  }

  // Handle chat messages
  handleChatMessage(message: string): void {
    this.addChatMessage('user', message);
    if (this.guardrailId && this.isExecuteMode) {
      this.isProcessingChat = true;
      this.validateGuardrail(message);

      // Process through the service - it will handle adding user and AI messages
      // this.toolExecutionService.processUserMessage(message);

      // Reset loading state after a delay that matches the service's response time
      setTimeout(() => {
        this.isProcessingChat = false;
      }, 1000);
    }
  }
  //Drop Down
  promptOptions: DropdownOption[] = [
    { value: 'default', name: 'Choose Prompt' },
    { value: 'ruby-developer', name: 'Senior Ruby Developer' },
    { value: 'python-developer', name: 'Python Developer' },
    { value: 'data-scientist', name: 'Data Scientist' },
    { value: 'frontend-developer', name: 'Frontend Developer' },
  ];
  onPromptChanged(option: DropdownOption) {
    console.log('Prompt changed in parent:', option);
    // your logic to handle selected prompt
  }
  showInterface = (): void => {
    this.isProcessingChat = false;
    this.isExecuteMode = true;
    this.showChatInterface = true;
    setTimeout(() => {
      if (this.codeEditor?.isReady) {
        this.codeEditor.focus();
        this.codeEditor['editor']?.layout();
        this.codeEditor.hideProcessingLoader(); // ✅ force hide loader
      }
    }, 100);
  };

  public validateGuardrail(message: string): void {
    this.showValidationOutput = false;
    this.validationOutput = '';

    const codeContent = this.getControl('codeContent').value?.trim() || '';
    const ymlContent = this.getControl('yamlContent').value?.trim() || '';

    if (!codeContent) {
      this.validationOutput =
        'Code content is empty. Please enter some code before validating.';
      this.showValidationOutput = true;
      return;
    }

    // Dynamic/default config
    const prompt = message;
    const mode = 'DEFAULT';
    const promptOverride = true;
    const userSignature = '<EMAIL>';
    const colangContent = JSON.stringify(codeContent) || '';
    const yamlContent = JSON.stringify(ymlContent) || '';

    this.promptGenerateService
      .modelGuardrailApi(
        prompt,
        mode,
        promptOverride,
        yamlContent,
        colangContent,
      )
      .subscribe({
        next: (res: any) => {
          let responseText = res?.response?.choices?.[0]?.text;
          this.addChatMessage('ai', responseText);
          if (!responseText || typeof responseText !== 'string') {
            this.validationOutput =
              'Unable to validate. Empty or invalid response.';
            this.showValidationOutput = true;
            this.codeEditor?.hideProcessingLoader();
            return;
          }

          responseText = responseText
            .replace(/```json\n?/, '')
            .replace(/```$/, '')
            .trim();

          try {
            const parsed = JSON.parse(responseText);
            let formatted = '';

            if (Array.isArray(parsed.issues) && parsed.issues.length > 0) {
              parsed.issues.forEach((issue: any, idx: number) => {
                formatted += `Issue ${idx + 1}:\n`;
                formatted += `  Type: ${issue.type}\n`;
                formatted += `  Description: ${issue.description}\n`;
                formatted += `  Severity: ${issue.severity}\n`;
                formatted += `  Line Number: ${issue.line_number}\n`;
                formatted += `  Suggestion: ${issue.suggestion}\n\n`;
              });
            } else {
              formatted = 'No issues found.';
            }

            this.validationOutput = formatted;
          } catch (e) {
            this.validationOutput = responseText;
          }

          this.showValidationOutput = true;
          this.codeEditor?.hideProcessingLoader();
        },
        error: (e) => {
          this.validationOutput =
            'Error: ' +
            (e?.error?.message || 'Unknown error occurred during validation.');
          this.showValidationOutput = true;
          this.codeEditor?.hideProcessingLoader();
        },
      });
  }

  onTabSelected(tabValue: string): void {
    // Save the currently shown code in the form and local raw storage
    const currentEditorContent = this.codeEditor.getValue();
    if (this.selectedTab === 'colang') {
      this.rawColangContent = currentEditorContent;
      this.guardrailForm.get('codeContent')?.setValue(currentEditorContent);
    } else {
      this.rawYamlContent = currentEditorContent;
      this.guardrailForm.get('yamlContent')?.setValue(currentEditorContent);
    }

    // Update selected tab
    this.selectedTab = tabValue;

    // Load new content based on selected tab
    const newContent =
      this.selectedTab === 'colang'
        ? this.rawColangContent
        : this.rawYamlContent;

    // Update the code editor with the new content
    setTimeout(() => {
      this.codeEditor.setValue(newContent || '');
    });
  }
  isSubmitDisabled(): boolean {
    const isFormInvalid = this.guardrailForm.invalid;
    return isFormInvalid;
  }
  getFieldError(fieldName: string): string {
    const field = this.guardrailForm.get(fieldName);
    // Capitalize only if first letter is not already uppercase
    const formattedFieldName = /^[A-Z]/.test(fieldName)
      ? fieldName
      : fieldName.charAt(0).toUpperCase() + fieldName.slice(1);
    if (field && field.invalid && (field.touched || field.dirty)) {
      if (field.errors?.['required']) {
        return `${formattedFieldName} is required`;
      }
    }
    return '';
  }

  // Trigger Save Confirmation
confirmSave(): void {
  this.actionType = 'save';
  this.confirmPopupTitle = 'Save Guardrail?';
  this.confirmPopupMessage = 'Are you sure you want to save this guardrail?';
  this.confirmButtonLabel = 'Save';
  this.confirmButtonColor = '#007bff';
  this.showConfirmPopup = true;
}

// Trigger Update Confirmation
confirmUpdate(): void {
  this.actionType = 'update';
  this.confirmPopupTitle = 'Update Guardrail?';
  this.confirmPopupMessage = 'Are you sure you want to update this guardrail?';
  this.confirmButtonLabel = 'Update';
  this.confirmButtonColor = '#28a745';
  this.showConfirmPopup = true;
}

// On confirm button clicked
  onConfirmAction(): void {
    this.showConfirmPopup = false;
    this.onSave(); // reuse existing logic
  }

  // Close/cancel
  closePopup(): void {
    this.showConfirmPopup = false;
  }
  get isColangTestDisabled(): boolean {
  const isColang = this.selectedCodeFormat === 'Colang';
  const colangCode = this.guardrailForm.get('codeContent')?.value?.trim();
  return isColang && (!colangCode || colangCode.length === 0);
}
}
