<p class="page-title">Guardrail Creator 🛠️</p>
<div class="create-guardrails-container">
  <form [formGroup]="guardrailForm">
    <div
      class="form-layout"
      [ngClass]="{ 'three-column-layout': isExecuteMode && showChatInterface }"
    >
      <!-- Left Column -->
      <div class="left-column">
        <div class="card-content">
          <ava-textbox
            [formControl]="getControl('name')"
            [label]="labels.guardrailName"
            id="guardrailName"
            name="GuardrailName"
            [placeholder]="labels.gnPlacholder"
            variant="primary"
            size="md"
            [fullWidth]="true"
            [required]="true"
            [error]="getFieldError('name')"
          ></ava-textbox>
          <ava-textarea
            id="description"
            name="description"
            [label]="labels.description"
            [formControl]="getControl('description')"
            [placeholder]="labels.gdPlaceholder"
            [rows]="4"
            size="md"
            [fullWidth]="true"
            [required]="true"
            [error]="getFieldError('description')"
          >
          </ava-textarea>
        </div>
      </div>

      <!-- Middle Column (previously Right) -->
      <div class="middle-column">
        <!-- Content wrapper to match left column height -->
        <div class="middle-column-content">
          <div class="card-content">
            <div class="head-section">
              <h3 class="section-title">{{ labels.sectionTitle }}</h3>
              <div class="code-format-buttons">
                <ava-button
                  [label]="isEditMode ? 'Update' : 'Save'"
                  size="medium"
                  state="active"
                  variant="primary"
                  (userClick)="isEditMode ? confirmUpdate() : confirmSave()"
                  [disabled]="isSubmitDisabled()"
                ></ava-button>
              </div>
            </div>

            <div class="code-format-buttons">
              <ava-button
                *ngFor="let format of codeFormats"
                [label]="format"
                [pill]="true"
                size="medium"
                [variant]="
                  selectedCodeFormat === format ? 'primary' : 'secondary'
                "
                (userClick)="setCodeFormat(format)"
              ></ava-button>
            </div>

            <!-- Code Editor Area -->
            <div class="code-editor-container">
              <!-- Code Editor Component -->
              <app-code-editor
                #codeEditor
                [title]="selectedCodeFormat + ' Editor'"
                [language]="codeLanguagesMap[selectedCodeFormat]"
                [Control]="currentCodeControl"
                [customCssClass]="'tools-monaco-editor'"
                [placeholder]="
                  'Write your ' + selectedCodeFormat + ' code here...'
                "
                (primaryButtonSelected)="showInterface()"
                [isPrimaryButtonDisabled]="isColangTestDisabled"
              >
              </app-code-editor>
            </div>
          </div>
        </div>
      </div>

      <div class="rightEnd-column" *ngIf="isExecuteMode && showChatInterface">
        <app-playground
          [promptOptions]="promptOptions"
          [messages]="chatMessages"
          [isLoading]="isProcessingChat"
          [showChatInteractionToggles]="false"
          [showAiPrincipleToggle]="true"
          (promptChange)="onPromptChanged($event)"
          (messageSent)="handleChatMessage($event)"
          [showApprovalButton]="false"
          [isMinimalView]="true"
        >
        </app-playground>
      </div>
    </div>
  </form>
</div>
<!-- Shared Save/Update Popup -->
<ava-popup
  [show]="showConfirmPopup"
  [title]="confirmPopupTitle"
  [message]="confirmPopupMessage"
  [showHeaderIcon]="true"
  [headerIconName]="actionType === 'save' ? 'save' : 'edit'"
  [iconColor]="confirmButtonColor"
  [showClose]="true"
  [showCancel]="true"
  [showConfirm]="true"
  [confirmButtonLabel]="confirmButtonLabel"
  [confirmButtonVariant]="'primary'"
  [confirmButtonBackground]="confirmButtonColor"
  (confirm)="onConfirmAction()"
  (cancel)="closePopup()"
  (closed)="closePopup()"
></ava-popup>
<!-- Success Popup -->
<ava-popup
  messageAlignment="center"
  [show]="showInfoPopup"
  title="SUCCESS!"
  [message]="infoMessage"
  [showHeaderIcon]="true"
  headerIconName="circle-check"
  iconColor="green"
  [showClose]="true"
  (closed)="handleInfoPopup()"
></ava-popup>
