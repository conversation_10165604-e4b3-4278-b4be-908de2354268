import { Component, OnDestroy, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { Router } from '@angular/router';
import {
  of,
  map,
  catchError,
  Subject,
  takeUntil,
  debounceTime,
  distinctUntilChanged,
  startWith,
} from 'rxjs';
import { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';
import { PaginationService } from '../../../shared/services/pagination.service';
import { KnowledgeBaseService } from '../../../shared/services/knowledge-base.service';
import {
  AvaTextboxComponent,
  DropdownComponent,
  DropdownOption,
  IconComponent,
  TextCardComponent,
  PopupComponent,
} from '@ava/play-comp-library';
import { LucideAngularModule } from 'lucide-angular';
import knowledgeBaseLabels from '../knowledge-base/constants/knowledge-base.json';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-knowledge-base',
  standalone: true,
  imports: [
    CommonModule,
    PageFooterComponent,
    TextCardComponent,
    AvaTextboxComponent,
    DropdownComponent,
    LucideAngularModule,
    IconComponent,
    ReactiveFormsModule,
    PopupComponent,
  ],
  providers: [DatePipe],
  templateUrl: './knowledge-base.component.html',
  styleUrls: ['./knowledge-base.component.scss'],
})
export class KnowledgeBaseComponent implements OnInit, OnDestroy {
  searchForm!: FormGroup;
  search: any;
  public kbLabels = knowledgeBaseLabels.labels;
  allKnowledgeBase: any[] = [];
  filteredKnowledgeBase: any[] = [];
  displayedKnowledgeBase: any[] = [];
  isLoading: boolean = false;
  error: string | null = null;
  currentPage: number = 1;
  itemsPerPage: number = 12;
  totalPages: number = 1;
  private destroy$ = new Subject<void>();

  knowledgeBaseOptions: DropdownOption[] = [
    { name: 'All', value: 'all' },
    { name: 'Type A', value: 'typeA' },
    { name: 'Type B', value: 'typeB' },
  ];
  selectedData: any = null;

  iconList = [
    { name: 'square-pen', iconName: 'square-pen', cursor: true },
    { name: 'trash', iconName: 'trash-2', cursor: true },
  ];

  showDeletePopup = false;
  knowledgeBaseToDelete: any = null;
  cardSkeletonPlaceholders = Array(11);

  constructor(
    private knowledgeBaseService: KnowledgeBaseService,
    private paginationService: PaginationService,
    private router: Router,
    private datePipe: DatePipe,
    private fb: FormBuilder,
  ) {
    this.searchForm = this.fb.group({
      search: [''],
    });
  }

  ngOnInit(): void {
    this.searchForm
      .get('search')!
      .valueChanges.pipe(
        startWith(''),
        debounceTime(300),
        distinctUntilChanged(),
        map((value) => value?.toLowerCase() ?? ''),
      )
      .subscribe((searchText) => {
        this.filterKnowledgeBase(searchText);
      });

    this.fetchAllKnowledge();
  }

  fetchAllKnowledge() {
    this.isLoading = true;
    this.knowledgeBaseService
      .fetchAllKnowledge()
      .pipe(
        map((response: any[]) => {
          return response.map((item: any) => {
            const formattedDate =
              this.datePipe.transform(item.createdDate, 'MM/dd/yyyy') || '';
            return {
              ...item,
              title: item.collectionName,
              createdDate: formattedDate,
              userCount: item.userCount || 0,
            };
          });
        }),
        catchError((error) => {
          this.isLoading = false;
          return of([]);
        }),
        takeUntil(this.destroy$),
      )
      .subscribe({
        next: (res) => {
          this.allKnowledgeBase = res;
          this.filteredKnowledgeBase = [...this.allKnowledgeBase];
          this.updateDisplayedKnowledgeBase();
          this.isLoading = false;
        },
        error: (err) => {
          this.isLoading = false;
        },
      });
  }

  updateDisplayedKnowledgeBase(): void {
    const paginationResult = this.paginationService.getPaginatedItems(
      this.filteredKnowledgeBase,
      this.currentPage,
      this.itemsPerPage,
    );
    this.displayedKnowledgeBase = paginationResult.displayedItems;
    console.log(this.displayedKnowledgeBase);
    this.totalPages = paginationResult.totalPages;
  }

  filterKnowledgeBase(searchText: string): void {
    this.filteredKnowledgeBase = this.allKnowledgeBase.filter((know) => {
      const inTitle = know.title?.toLowerCase().includes(searchText);
      const inDescription = know.description
        ?.toLowerCase()
        .includes(searchText);
      const inTags =
        Array.isArray(know.tags) &&
        know.tags?.some((tag: any) =>
          tag.label?.toLowerCase().includes(searchText),
        );

      return inTitle || inDescription || inTags;
    });

    this.updateDisplayedKnowledgeBase();
  }

  onCreateKnowledgeBase(): void {
    this.router.navigate(['/libraries/knowledge-base/create']);
  }

  onCardClicked(knowledgeBaseId: string): void {
    // Navigate to knowledge base details page
    this.router.navigate([`/libraries/knowledge-base/edit/${knowledgeBaseId}`]);
  }

  getHeaderIcons(knowledgeBase: any): { iconName: string; title: string }[] {
    return [
      { iconName: 'wrench', title: knowledgeBase.toolType || 'KnowledgeBase' },
      { iconName: 'users', title: `${knowledgeBase.userCount || 120}` },
    ];
  }

  getFooterIcons(knowledgeBase: any): { iconName: string; title: string }[] {
    return [
      { iconName: 'user', title: knowledgeBase.owner || 'AAVA' },
      { iconName: 'calendar-days', title: knowledgeBase.createdDate },
    ];
  }

  onIconClicked(icon: any, knowledgeBaseId: string): void {
    switch (icon.name) {
      case 'trash':
        this.confirmDeleteKnowledgeBase(knowledgeBaseId);
        break;
      case 'square-pen':
        this.editKnowledgeBase(knowledgeBaseId);
        break;
      default:
        break;
    }
  }

  editKnowledgeBase(knowledgeBaseId: string): void {
    // Implement duplicate logic
    this.router.navigate([`/libraries/knowledge-base/edit/${knowledgeBaseId}`]);
  }

  deleteKnowledgeBase(knowledgeBaseId: string): void {
    const collectionObj = this.allKnowledgeBase.find(
      (item) => item.id === knowledgeBaseId,
    );
    const collectionName = collectionObj?.title;

    if (collectionName) {
      this.knowledgeBaseService.deleteByCollection(collectionName).subscribe({
        next: (res) => {
          console.log('Knowledge base deleted:', res);
          this.fetchAllKnowledge(); // Refresh the list
        },
        error: (err) => {
          console.error('Failed to delete knowledge base:', err);
        },
      });
    } else {
      console.warn(`Knowledge base with ID ${knowledgeBaseId} not found.`);
    }
  }

  confirmDeleteKnowledgeBase(knowledgeBaseId: string): void {
    this.knowledgeBaseToDelete = this.allKnowledgeBase.find(
      (item) => item.id === knowledgeBaseId,
    );
    this.showDeletePopup = true;
  }

  onConfirmDelete(): void {
    const collectionName = this.knowledgeBaseToDelete?.title;
    if (collectionName) {
      this.knowledgeBaseService.deleteByCollection(collectionName).subscribe({
        next: (res) => {
          console.log('Knowledge base deleted:', res);
          this.fetchAllKnowledge();
          this.closeDeletePopup();
        },
        error: (err) => {
          console.error('Failed to delete knowledge base:', err);
          this.closeDeletePopup();
        },
      });
    }
  }

  closeDeletePopup(): void {
    this.showDeletePopup = false;
    this.knowledgeBaseToDelete = null;
  }

  duplicateKnowledgeBase(knowledgeBaseId: string): void {
    // Implement duplicate logic
  }

  onSelectionChange(data: any) {
    this.selectedData = data;
    // Implement filter logic if needed
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedKnowledgeBase();
  }

  get showCreateCard(): boolean {
    return this.currentPage === 1 && !this.isLoading && !this.error;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
