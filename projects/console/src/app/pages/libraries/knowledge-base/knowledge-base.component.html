<div id="knowledge-base-container" class="container-fluid">
  <div id="search-filter-container" class="row g-3">
    <div class="col-12 col-md-8 col-lg-9 col-xl-10 search-section">
      <form [formGroup]="searchForm">
        <ava-textbox
          placeholder='Search "Knowledge Base"'
          hoverEffect="glow"
          pressedEffect="solid"
          formControlName="search"
        >
          <ava-icon
            slot="icon-start"
            iconName="search"
            [iconSize]="16"
            iconColor="var(--color-brand-primary)"
          >
          </ava-icon>
        </ava-textbox>
      </form>
    </div>
    <div class="col-12 col-md-4 col-lg-3 col-xl-2 action-buttons">
      <ava-dropdown
        dropdownTitle="choose knowledge base"
        [options]="knowledgeBaseOptions"
        (selectionChange)="onSelectionChange($event)"
      >
      </ava-dropdown>
    </div>
  </div>

  <div id="prompts-card-container" class="row g-3">
    <ava-text-card
      class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5"
      [type]="'create'"
      [iconName]="'plus'"
      iconColor="#144692"
      [title]="kbLabels.createKnowledgeBase"
      (cardClick)="onCreateKnowledgeBase()"
      [isLoading]="isLoading"
    >
    </ava-text-card>

    <!-- No Results Message -->
    <div
      class="col-12 d-flex justify-content-center align-items-center py-5"
      *ngIf="!isLoading && filteredKnowledgeBase.length === 0"
    >
      <div class="text-center">
        <h5 class="text-muted">{{ kbLabels.noResults }}</h5>
      </div>
    </div>

    <ng-container *ngFor="let knowledgeBase of isLoading && displayedKnowledgeBase.length === 0 ? cardSkeletonPlaceholders : displayedKnowledgeBase">
      <ava-text-card
        class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5"
        [type]="'prompt'"
        [title]="!isLoading ? knowledgeBase.title : ''"
        [description]="!isLoading ? knowledgeBase.description : ''"
        [iconList]="!isLoading ? iconList : []"
        [iconColor]="'#144692'"
        [headerIcons]="!isLoading ? getHeaderIcons(knowledgeBase) : []"
        [footerIcons]="!isLoading ? getFooterIcons(knowledgeBase) : []"
        (iconClick)="!isLoading ? onIconClicked($event, knowledgeBase.id) : null"
        [isLoading]="isLoading"
      >
      </ava-text-card>
    </ng-container>
  </div>

  <!-- Pagination Footer -->
  <div class="row" *ngIf="filteredKnowledgeBase.length > 0">
    <div class="col-12 d-flex justify-content-center mt-4">
      <app-page-footer
        [totalItems]="filteredKnowledgeBase.length + 1"
        [currentPage]="currentPage"
        [itemsPerPage]="itemsPerPage"
        (pageChange)="onPageChange($event)"
      ></app-page-footer>
    </div>
  </div>
</div>
<ava-popup
  [show]="showDeletePopup"
  title="Delete Knowledge Base?"
  [message]="
    'Are you sure you want to delete ' +
    (knowledgeBaseToDelete?.title || '') +
    '?'
  "
  [showHeaderIcon]="true"
  headerIconName="trash"
  iconColor="#dc3545"
  [showClose]="true"
  [showCancel]="true"
  [showConfirm]="true"
  [confirmButtonLabel]="'Delete'"
  [confirmButtonVariant]="'primary'"
  [confirmButtonBackground]="'#dc3545'"
  (confirm)="onConfirmDelete()"
  (cancel)="closeDeletePopup()"
  (closed)="closeDeletePopup()"
>
</ava-popup>
