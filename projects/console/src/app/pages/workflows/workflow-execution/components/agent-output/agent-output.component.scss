.agent-output {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
  background: var(--card-bg);

  .output-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h3 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: var(--text-color);
    }

    .export-button {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 10px 16px;
      background-color: var(--card-bg);
      position: relative;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      color: transparent;
      background-image: var(--gradient-primary);
      background-clip: text;
      -webkit-background-clip: text;
      cursor: pointer;
      transition: all 0.2s ease;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 8px;
        padding: 1px;
        background: var(--gradient-primary);
        -webkit-mask:
          linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        pointer-events: none;
      }

      &:hover {
        background-color: var(--dropdown-hover-bg);
      }

      svg {
        width: 16px;
        height: 16px;
        stroke: url(#gradient);

        path {
          stroke: url(#gradient);
          stroke-width: 2;
        }
      }
    }
  }

  .output-content {
    flex-grow: 1;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: var(--dashboard-scrollbar-track);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--dashboard-scrollbar-thumb);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: var(--dashboard-scrollbar-thumb-hover);
    }

    .no-outputs {
      padding: 40px 0;
      text-align: center;
      color: var(--text-secondary);
      font-size: 14px;
      font-style: italic;
    }

    .output-card {
      margin-bottom: 20px;
      border: 1px solid var(--dashboard-border-light);
      border-radius: 8px;
      overflow: hidden;
      background-color: var(--card-bg);
      box-shadow: 0 2px 4px var(--card-shadow);
      padding: 1rem;

      h3 {
        font-size: 1rem;
        font-weight: 700;
      }

      .output-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background-color: var(--dashboard-bg-lighter);
        border-bottom: 1px solid var(--dashboard-border-light);

        .output-info {
          .output-title {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: var(--text-color);
          }

          .output-subtitle {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 2px;
          }
        }

        .output-actions {
          display: flex;
          gap: 8px;

          .action-button {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 6px 10px;
            background-color: var(--card-bg);
            border: 1px solid var(--dashboard-border-light);
            border-radius: 4px;
            font-size: 12px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background-color: var(--dashboard-bg-lighter);
              color: var(--text-color);
            }

            &.copy-button {
              padding: 6px;
            }

            &.preview-button {
              background-color: var(--dashboard-bg-icon-button);
              border-color: var(--dashboard-border-accent);
              color: var(--dashboard-secondary);

              &:hover {
                background-color: var(--dropdown-hover-bg);
              }
            }
          }
        }
      }

      .output-card-content {
        &.code {
          background-color: var(--code-bg, var(--dashboard-bg-light));
          color: var(--code-color, var(--text-color));
          font-family: "Fira Code", monospace;

          pre {
            margin: 0;
            padding: 16px;
            overflow-x: auto;

            &::-webkit-scrollbar {
              width: 6px;
              height: 6px;
            }

            &::-webkit-scrollbar-track {
              background: var(--dashboard-scrollbar-track);
              border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb {
              background: var(--dashboard-scrollbar-thumb);
              border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb:hover {
              background: var(--dashboard-scrollbar-thumb-hover);
            }

            code {
              font-size: 13px;
              line-height: 1.5;
              white-space: pre-wrap;
            }
          }
        }

        &.text {
          padding: 16px;
          color: var(--text-color);
          font-size: 14px;
          line-height: 1.5;

          pre {
            margin: 0;
            white-space: pre-wrap;
            word-break: break-word;
          }
        }

        &.json {
          background-color: var(--code-bg, var(--dashboard-bg-light));
          color: var(--code-color, var(--text-color));
          font-family: "Fira Code", monospace;

          pre {
            margin: 0;
            padding: 16px;
            overflow-x: auto;

            &::-webkit-scrollbar {
              width: 6px;
              height: 6px;
            }

            &::-webkit-scrollbar-track {
              background: var(--dashboard-scrollbar-track);
              border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb {
              background: var(--dashboard-scrollbar-thumb);
              border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb:hover {
              background: var(--dashboard-scrollbar-thumb-hover);
            }

            code {
              font-size: 13px;
              line-height: 1.5;
              white-space: pre-wrap;
            }
          }
        }

        &.markdown {
          padding: 16px;
          color: var(--text-color);
          font-size: 14px;
          line-height: 1.5;

          pre {
            margin: 0;
            white-space: pre-wrap;
            word-break: break-word;
          }
        }
      }

      .output-card-footer {
        padding: 8px 16px;
        background-color: var(--dashboard-bg-lighter);
        border-top: 1px solid var(--dashboard-border-light);

        .output-timestamp {
          font-size: 11px;
          color: var(--text-secondary);
        }
      }
    }
  }
}

.card-details {
  display: flex;
  // justify-content: space-between;
  gap: 32px;
}
