import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from '@ava/play-comp-library';
export interface AgentOutput {
  id: string;
  title: string;
  content: string;
  agentName: string;
  timestamp: string;
  type?: 'code' | 'text' | 'json' | 'markdown';
  description: string;
  expected_output: string;
  summary: string;
  raw: string;
}

@Component({
  selector: 'app-agent-output',
  standalone: true,
  imports: [CommonModule, ButtonComponent],
  templateUrl: './agent-output.component.html',
  styleUrls: ['./agent-output.component.scss'],
})
export class AgentOutputComponent implements OnInit {
  @Input() outputs: AgentOutput[] = [];
  @Output() export = new EventEmitter<void>();

  constructor() {}

  getContentType(output: AgentOutput): string {
    // Default to 'text' if not specified
    if (!output.type) {
      // Try to auto-detect
      if (output.content.startsWith('<') && output.content.includes('</')) {
        return 'code';
      } else if (
        output.content.includes('```') ||
        output.content.includes('#')
      ) {
        return 'markdown';
      } else if (
        output.content.startsWith('{') ||
        output.content.startsWith('[')
      ) {
        return 'json';
      }
      return 'text';
    }
    return output.type;
  }

  copyToClipboard(content: string): void {
    navigator.clipboard
      .writeText(content)
      .then(() => {
        // Could show a toast notification here
        console.log('Content copied to clipboard');
      })
      .catch((err) => {
        console.error('Could not copy text: ', err);
      });
  }

  exportOutput(): void {
    this.export.emit();
  }

  previewOutput(output: AgentOutput): void {
    // In a real app, this would open a preview modal or navigate to a preview page
    console.log('Preview output:', output);
    window.open(
      `data:text/html;charset=utf-8,${encodeURIComponent(output.content)}`,
      '_blank',
    );
  }
  ngOnInit(): void {
    console.log(this.outputs);
  }
}
