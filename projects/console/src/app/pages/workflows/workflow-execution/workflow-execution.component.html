<div class="workflow-execution-container">
  <!-- SVG Gradient Definitions for Icons -->
  <svg width="0" height="0" style="position: absolute">
    <defs>
      <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stop-color="#6566CD" />
        <stop offset="100%" stop-color="#F96CAB" />
      </linearGradient>
    </defs>
  </svg>
  <div class="execution-content" role="main">
    <div
      class="column playground-column"
      role="region"
      aria-label="Workflow Playground"
    >
      <div class="column-content playground-content">
        <div class="column-header">
          <h2 id="playground-title">
            <ava-icon iconName="panelLeft" iconColor="#1A46A7"></ava-icon>
          </h2>
          <div class="header-buttons">
            <ava-button label="History" variant="secondary" size="medium">
            </ava-button>
          </div>
        </div>
        <div class="chat-container" aria-labelledby="playground-title">
          <!-- Chat Interface Component -->
          <app-chat-interface
            #chatInterfaceComp
            [messages]="chatMessages"
            [isLoading]="isProcessingChat"
            (messageSent)="handleChatMessage($event)"
            (attachmentClicked)="handleAttachment()"
            [inputText]="inputText"
            [isOptionalInput]="true"
            [handleFileUpload]="true"
            fileType=".zip"
            [maxFileSize]="10485760"
            (attachmentSelected)="onAttachmentsSelected($event)"
          ></app-chat-interface>
        </div>
      </div>
    </div>

    <!-- Right Column: Agent Output -->
    <div class="column output-column" role="region" aria-label="Agent Output">
      <div class="column-content">
        <div class="column-header row">
          <div class="col-7">
            <ava-tabs
              [tabs]="navigationTabs"
              activeTabId="nav-home"
              variant="button"
              buttonShape="pill"
              [showContentPanels]="false"
              ariaLabel="Pill navigation tabs"
            ></ava-tabs>
          </div>
          <div class="col-5">
            <ava-button
              label="Send for Approval"
              variant="primary"
              [customStyles]="{
                background:
                  'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                '--button-effect-color': '33, 90, 214',
              }"
              size="medium"
            >
            </ava-button>
            <ava-button label="Export" variant="secondary" size="medium">
            </ava-button>
          </div>
        </div>
        <!-- activity content -->
        <div *ngIf="selectedTab === 'activity'" style="height: 100%">
          <app-agent-activity
            [activityLogs]="workflowLogs"
            [executionDetails]="executionDetails"
            [progress]="progress"
            [isRunning]="isRunning"
            (saveLogs)="saveLogs()"
            (controlAction)="handleControlAction($event)"
            [status]="status"
            (onOutPutBtnClick)="this.selectedTab = 'agents'"
          ></app-agent-activity>
        </div>
        <!-- Agent Output Component -->
        <div *ngIf="selectedTab === 'agents'" style="height: 100%">
          <app-agent-output
            [outputs]="taskMessage"
            (export)="exportResults('output')"
          ></app-agent-output>
        </div>
      </div>
    </div>
  </div>
</div>
