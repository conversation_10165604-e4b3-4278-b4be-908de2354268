// Utility mixin for hiding scrollbars while keeping scroll functionality
@mixin hide-scrollbar {
  // Hide scrollbar for webkit browsers (Chrome, Safari, Edge)
  &::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
  }

  // Hide scrollbar for Firefox
  scrollbar-width: none;

  // For Internet Explorer and Edge legacy
  -ms-overflow-style: none;
}

.workflow-editor-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--background-primary);
  overflow: hidden;

  .header-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 60px;
    background-color: var(--card-bg);
    border-bottom: 1px solid var(--border-color);

    .breadcrumb {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;

      .nav-item {
        font-size: 14px;
        color: var(--text-secondary);
        cursor: pointer;
        transition: color 0.2s ease;

        &:hover {
          color: var(--text-primary);
        }

        &.active {
          color: var(--text-primary);
          font-weight: 500;
        }
      }

      .separator {
        color: var(--text-tertiary);
        font-size: 14px;
      }

      .close-btn {
        background: none;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        margin-left: 16px;
        transition: all 0.2s ease;

        &:hover {
          background-color: var(--hover-bg);
          color: var(--text-primary);
        }
      }
    }

    .header-actions {
      .action-group {
        display: flex;
        align-items: center;
        gap: 8px;

        .action-btn {
          background: none;
          border: 1px solid var(--border-color);
          color: var(--text-secondary);
          cursor: pointer;
          padding: 8px;
          border-radius: 6px;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            background-color: var(--hover-bg);
            border-color: var(--border-hover);
            color: var(--text-primary);
          }
        }

        .run-btn {
          background: var(--dashboard-primary);
          border: 1px solid var(--dashboard-primary);
          color: white;
          cursor: pointer;
          padding: 8px 16px;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          display: flex;
          align-items: center;
          gap: 6px;
          transition: all 0.2s ease;

          &:hover {
            background: var(--dashboard-primary-hover);
            border-color: var(--dashboard-primary-hover);
          }
        }
      }
    }
  }

  .main-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    height: calc(100vh - 60px);
    max-height: calc(100vh - 60px);
    position: relative;

    .canvas-area {
      flex: 1;
      background-color: var(--background-primary);
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;

      // Agent Library Floating Panel - Positioned on canvas area
      .agent-library-panel {
        position: absolute;
        top: 20px;
        left: 20px;
        width: 380px;
        height: calc(100vh - 140px);
        background-color: #ffffff;
        border: 1px solid #e5e7eb;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        z-index: 10;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;
        display: flex;
        flex-direction: column;

        // Collapsed state
        &.collapsed {
          width: auto;
          min-width: 200px;
          height: auto;

          .panel-header {
            padding: 12px 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: #ffffff;
            border-bottom: none;

            h3 {
              margin: 0;
              font-size: 14px;
              font-weight: 600;
              color: var(--text-primary);
            }

            ava-icon {
              transition: transform 0.3s ease;
            }
          }

          .panel-content {
            display: none;
          }
        }

        .panel-header {
          padding: 20px 20px 16px 20px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: space-between;
          background-color: #ffffff;
          border-bottom: 1px solid #f3f4f6;
          transition: all 0.3s ease;
          flex-shrink: 0;

          &:hover {
            background-color: #f9fafb;
          }

          h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 700;
            color: #111827;
          }

          ava-icon {
            transition: transform 0.3s ease;
          }
        }

        .panel-content {
          flex: 1;
          overflow: hidden;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          display: flex;
          flex-direction: column;

          &.hidden {
            max-height: 0;
            opacity: 0;
          }

          .search-section {
            padding: 20px 20px 16px 20px;
            flex-shrink: 0;

            form {
              width: 100%;
            }

            // Search input styling
            ::ng-deep ava-textbox {
              .textbox-container {
                border-radius: 12px;
                border: 1px solid #e5e7eb;
                background-color: #f9fafb;

                &:focus-within {
                  border-color: #3b82f6;
                  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                }

                input {
                  background-color: transparent;
                  color: #111827;
                  font-size: 14px;

                  &::placeholder {
                    color: #9ca3af;
                  }
                }
              }
            }
          }

          .agents-list {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 0 20px 20px 20px;
            @include hide-scrollbar;
            min-height: 0;

            .agent-item {
              background-color: #ffffff;
              border: 1px solid #e5e7eb;
              border-radius: 12px;
              padding: 16px;
              margin-bottom: 12px;
              cursor: pointer;
              transition: all 0.2s ease;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

              &:hover {
                border-color: #3b82f6;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                transform: translateY(-1px);
              }

              .agent-header {
                display: flex;
                align-items: center;
                gap: 12px;
                margin-bottom: 8px;

                .agent-icon-box {
                  width: 36px;
                  height: 36px;
                  background-color: #3b82f6;
                  border-radius: 8px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  flex-shrink: 0;
                }

                .agent-name {
                  margin: 0;
                  font-size: 15px;
                  font-weight: 600;
                  color: #111827;
                  flex: 1;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }

                .agent-count {
                  display: flex;
                  align-items: center;
                  gap: 4px;
                  font-size: 13px;
                  color: #6b7280;

                  .count-text {
                    font-weight: 500;
                  }
                }
              }

              .agent-description {
                margin: 0 0 12px 0;
                font-size: 14px;
                color: #6b7280;
                line-height: 1.5;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }

              .agent-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 6px;
                margin-bottom: 12px;

                .agent-tag {
                  background-color: #f3f4f6;
                  color: #374151;
                  font-size: 12px;
                  padding: 4px 8px;
                  border-radius: 6px;
                  font-weight: 500;
                }
              }

              .agent-actions {
                display: flex;
                justify-content: flex-end;

                .preview-btn {
                  ::ng-deep .button-container {
                    border-radius: 8px;
                    font-size: 12px;
                    padding: 6px 12px;
                  }
                }
              }
            }

            .no-results-message {
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 2rem 1rem;
              text-align: center;

              .no-results-content {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 0.5rem;

                p {
                  margin: 0;
                  color: #6b7280;
                  font-size: 14px;
                }
              }
            }
          }

          .create-agent-section {
            padding: 20px;
            border-top: 1px solid #f3f4f6;
            background-color: #ffffff;
            flex-shrink: 0;

            // Create button styling
            ::ng-deep ava-button {
              .button-container {
                border-radius: 12px;
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                border: none;
                font-weight: 600;
                font-size: 14px;
                padding: 12px 20px;

                &:hover {
                  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
                  transform: translateY(-1px);
                  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
                }
              }
            }
          }
        }
      }

      // Action Buttons Section - Positioned on the right
      .action-buttons-section {
        position: absolute;
        top: 20px;
        right: 20px;
        z-index: 10;

        .action-group {
          display: flex;
          align-items: center;
          gap: 8px;

          .action-btn {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

            &:hover {
              background-color: #f9fafb;
              border-color: #3b82f6;
              color: var(--text-primary);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }
          }

          .run-btn {
            background: var(--dashboard-primary);
            border: 1px solid var(--dashboard-primary);
            color: white;
            cursor: pointer;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

            &:hover {
              background: var(--dashboard-primary-hover);
              border-color: var(--dashboard-primary-hover);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }
          }
        }
      }

      // Workflow Editor Canvas
      .editor-canvas {
        width: 100%;
        height: 100%;
        background-color: var(--background-primary);
        position: relative;
        overflow: hidden;

        // Canvas board styling
        ::ng-deep app-canvas-board {
          width: 100%;
          height: 100%;

          // Override header positioning to place beside Agent Library panel
          .header-inputs-section {
            position: absolute;
            top: 20px;
            left: 420px; // Position after Agent Library Panel (380px + 40px gap)
            z-index: 10;
            max-width: calc(100% - 440px); // Prevent overflow
            overflow: visible;

            .header-inputs-container {
              display: flex;
              align-items: center;
              gap: 16px;
              flex-wrap: nowrap;
            }
          }

          // LLM Toggle Container styling
          .llm-toggle-container {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 16px;
            background-color: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-left: 16px;

            .toggle-container {
              display: flex;
              align-items: center;
              gap: 12px;

              .toggle-label {
                font-size: 14px;
                font-weight: 500;
                color: #111827;
                white-space: nowrap;
                cursor: pointer;
              }

              // Toggle styling
              ::ng-deep ava-toggle {
                .toggle-container {
                  border-radius: 12px;
                }
              }
            }
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .workflow-editor-container {
    .main-content {
      .canvas-area {
        .agent-library-panel {
          width: 340px;
          height: calc(100vh - 140px);
        }

        .editor-canvas {
          ::ng-deep app-canvas-board {
            .header-inputs-section {
              left: 380px; // Adjust position for smaller panel
              max-width: calc(100% - 400px);
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .workflow-editor-container {
    .main-content {
      .canvas-area {
        .agent-library-panel {
          width: 300px;
          height: calc(100vh - 140px);

          .panel-content {
            .search-section {
              padding: 16px;
            }

            .agents-list {
              padding: 0 16px 16px 16px;

              .agent-item {
                padding: 12px;
              }
            }

            .create-agent-section {
              padding: 16px;
            }
          }
        }

        .editor-canvas {
          ::ng-deep app-canvas-board {
            .header-inputs-section {
              left: 340px; // Adjust position for smaller panel
              max-width: calc(100% - 360px);
            }
          }
        }
      }
    }
  }
}
