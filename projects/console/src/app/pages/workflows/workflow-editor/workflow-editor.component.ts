import {
  <PERSON>mponent,
  <PERSON><PERSON>nit,
  <PERSON><PERSON><PERSON><PERSON>,
  AfterViewInit,
  ChangeDetectorRef,
  Input,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  FormControl,
} from '@angular/forms';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import {
  WorkflowGraphService,
  WorkflowNode,
  WorkflowEdge,
} from './services/workflow-graph.service';
import { ReactFlowService } from './services/react-flow.service';
import { AgentNodeComponent } from './components/agent-node/agent-node.component';
import { Subscription } from 'rxjs';
import { Agent } from './models/agent.model';
import { DragDropModule } from '@angular/cdk/drag-drop';
import workflowLabels from './../constants/workflows.json';
import { WorkflowService } from '../../../shared/services/workflow.service';
import {
  CanvasBoardComponent,
  CanvasNode,
  CanvasEdge,
} from '../../../shared/components/canvas-board/canvas-board.component';

import {
  AvaTextboxComponent,
  ButtonComponent,
  DropdownOption,
  IconComponent,
  PopupComponent,
  ToggleComponent,
} from '@ava/play-comp-library';
import { TokenStorageService } from '@shared/index';
import { Model } from '../../../shared/models/card.model';
import { WorkflowModes } from '../constants/workflow.constants';
import { CollaborativeAgentServiceService } from '../../../shared/services/collaborative-agent-service.service';

interface NodePosition {
  x: number;
  y: number;
}

@Component({
  selector: 'app-workflow-editor',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    AgentNodeComponent,
    DragDropModule,
    CanvasBoardComponent,
    ToggleComponent,
    ButtonComponent,
    PopupComponent,
    IconComponent,
    AvaTextboxComponent,
  ],
  templateUrl: './workflow-editor.component.html',
  styleUrls: ['./workflow-editor.component.scss'],
})
export class WorkflowEditorComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  @Input() primaryButtonText: string = '';

  workflowId: string | null = null;
  isEditMode: boolean = false;
  workflowForm: FormGroup;
  modelList: DropdownOption[] = [];
  showWarningPopup = false;

  savedWorkFlowDetils: Record<string, any> = {};

  // Labels used across the Knowledge Base UI components (titles)
  public workFlowLabels = workflowLabels.labels;

  showLlm = false;

  levels = ['organization', 'domain', 'project', 'team'];
  filterLabels = ['organization', 'domain', 'project', 'team'];
  optionsMap: { [level: number]: any[] } = {};
  levelOptionsMap: Record<number, any[]> = {};

  inputFieldsConfig = {
    // agentName: { enabled: true, placeholder: 'Workflow Name', required: true },
    agentName: { enabled: true, placeholder: 'Agent Name', required: true },
    agentDetails: {
      enabled: true, // Re-enabled - this is the correct Workflow Details section
      label: 'Workflow Details',
      namePlaceholder: 'Enter Workflow name',
      detailPlaceholder: 'Enter workflow description',
      detailLabel: 'Description',
    },
  };

  // Canvas board properties
  canvasNodes: CanvasNode[] = [];
  canvasEdges: CanvasEdge[] = [];
  navigationHints: string[] = [
    // `${workflowLabels.labels.alt} + ${workflowLabels.labels.drag} ${workflowLabels.labels.toPanCanvas}`,
    // `${workflowLabels.labels.mouseWheel} ${workflowLabels.labels.toZoom}`,
    // `${workflowLabels.labels.space} ${workflowLabels.labels.toResetView}`
  ];

  // Agent library
  agents: Agent[] = [
    {
      id: 'agent1',
      name: 'Test Agent Agent Agent Agent Agent Agent',
      description: 'Translating user needs into actionable development tasks.',
      type: 'Individual',
      capabilities: ['Code Generation', 'Translation'],
    },
    {
      id: 'agent2',
      name: 'Test Agent 2',
      description: 'Processing data and generating insights.',
      type: 'Individual',
      capabilities: ['Data Analysis', 'Visualization'],
    },
    {
      id: 'agent3',
      name: 'Test Agent 3',
      description: 'Handling complex natural language processing tasks.',
      type: 'Collaborative',
      capabilities: ['NLP', 'Summarization', 'Translation'],
    },
  ];

  // Filtered agents for search
  filteredAgents: Agent[] = [];

  // Available agents (filtered and not used yet)
  availableAgents: Agent[] = [];

  // Workflow nodes and edges (original format)
  nodes: WorkflowNode[] = [];
  edges: WorkflowEdge[] = [];

  // Selected node
  selectedNodeId: string | null = null;

  // Subscriptions
  private subscriptions: Subscription[] = [];

  // Track used agent IDs
  usedAgentIds: Set<string> = new Set();

  // Panel state
  isPanelCollapsed: boolean = false;

  // Search form for the panel
  searchForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private workflowGraphService: WorkflowGraphService,
    private workflowService: WorkflowService,
    private reactFlowService: ReactFlowService,
    private cdr: ChangeDetectorRef,
    private collaborativeAgentService: CollaborativeAgentServiceService,
    private tokenStorage: TokenStorageService,
  ) {
    this.workflowForm = this.fb.group({
      // Workflow details
      name: [''],
      description: [''],

      // Filters
      organization: [''],
      domain: [''],
      project: [''],
      team: [''],

      // Enable Manager LLM
      enableManagerLLM: [false],

      // LLM Configuration settings
      modelDeploymentName: [],
      temperature: [0.3],
      topP: [0.95],
      maxRPM: [0],
      maxToken: [4000],
      maxIteration: [1],
      maxExecutionTime: [30],

      // Search filter
      agentFilter: [''],
    });

    // Initialize search form for the panel
    this.searchForm = this.fb.group({
      agentFilter: [''],
    });
  }

  // Panel toggle functionality
  togglePanel(): void {
    this.isPanelCollapsed = !this.isPanelCollapsed;
  }

  // Item preview functionality
  onItemPreview(agent: Agent): void {
    console.log('Preview agent:', agent);
    // Implement preview functionality
  }

  // Create new agent functionality
  onCreateNewAgent(): void {
    console.log('Create new agent');
    // Navigate to agent creation page or open modal
    this.router.navigate(['/build/agents/create']);
  }

  isModeDuplicate() {
    return this.route.snapshot.queryParams['mode'] === WorkflowModes.duplicate;
  }

  getAndPatchWorkFlowDetails(id: string) {
    const isDuplicateMode = this.isModeDuplicate();

    this.workflowService.getOneWorkflow(id).subscribe((response) => {
      this.savedWorkFlowDetils = response;
      // console.log(this.savedWorkFlowDetils);
      const agents = (response?.pipeLineAgents || []) as any[];
      agents.sort((a: any, b: any) => a.serial - b.serial);
      let x = 40;
      let y = 120;
      agents.forEach((agent, i) => {
        this.createNewNode(agent.agent, { x, y });
        x += 400;
        const position = i + 1;
        if (position % 3 == 0) {
          y += 120;
          x = 40;
        }
      });

      const formData: Record<string, any> = {};

      if (!isDuplicateMode) {
        formData['name'] = response.name;
        formData['description'] = response.description;
      }

      if (response?.managerLlm) {
        formData['enableManagerLLM'] = true;
        Object.assign(formData, response?.managerLlm);
      }

      this.workflowForm.patchValue(formData);
    });
  }

  ngOnInit(): void {
    this.fetchChildOptions(0, -1);
    // Check if we're in edit mode
    this.workflowId =
      this.route.snapshot.paramMap.get('id') ||
      this.route.snapshot.queryParams['id'];
    this.isEditMode = !!this.workflowId;

    if (this.isEditMode && this.workflowId) {
      // In a real app, you would fetch the workflow data by ID
      console.log(`Editing workflow with ID: ${this.workflowId}`);
      this.getAndPatchWorkFlowDetails(this.workflowId);

      // this.loadWorkflowData(this.workflowId);
    }

    // Subscribe to nodes to track used agents
    this.subscriptions.push(
      this.workflowGraphService.nodes$.subscribe((nodes) => {
        this.nodes = nodes;
        this.canvasNodes = this.convertToCanvasNodes(nodes);

        // Update used agent IDs
        this.updateUsedAgentIds();
      }),
    );

    this.subscriptions.push(
      this.workflowGraphService.edges$.subscribe((edges) => {
        this.edges = edges;
        this.canvasEdges = this.convertToCanvasEdges(edges);
      }),
    );

    // Subscribe to the search filter changes
    this.subscriptions.push(
      this.getControl('agentFilter').valueChanges.subscribe((filterValue) => {
        this.filterAgents(filterValue);
      }),
    );

    this.collaborativeAgentService
      .getAllCollaborativeAgents()
      .subscribe((response) => {
        this.agents = response;
        this.filterAgents(this.getControl('agentFilter').value);
        // Making sure the dropdown value get patched agin after options are added
        setTimeout(() => {
          this.workflowForm.patchValue({
            modelDeploymentName:
              this.savedWorkFlowDetils['managerLlm']?.modelDeploymentName,
          });
        });
      });

    this.workflowService.getAllGenerativeModels().subscribe((response) => {
      this.modelList = response.map((modle: Model) => {
        return {
          name: modle.modelDeploymentName,
          value: modle.modelDeploymentName,
        };
      });
    });
  }

  ngAfterViewInit(): void {
    // Canvas board handles its own initialization
  }

  // Convert WorkflowNode to CanvasNode
  private convertToCanvasNodes(nodes: WorkflowNode[]): CanvasNode[] {
    return nodes.map((node) => ({
      id: node.id,
      type: node.type,
      data: node.data,
      position: node.position,
    }));
  }

  // Convert WorkflowEdge to CanvasEdge
  private convertToCanvasEdges(edges: WorkflowEdge[]): CanvasEdge[] {
    return edges.map((edge) => ({
      id: edge.id,
      source: edge.source,
      target: edge.target,
      animated: edge.animated,
    }));
  }

  private convertNodesToEdges(nodes: WorkflowNode[]) {
    return nodes.slice(1).map((node, i) => {
      const perviousNode = i <= 0 ? nodes[0] : nodes[i - 1];

      return {
        id: `${perviousNode.id}-${node.id}`,
        source: perviousNode.id,
        target: node.id,
        // animated: node.animated,
      };
    });
  }

  onLevelChange(level: number, selectedValue: string | string[]): void {
    const selected = Array.isArray(selectedValue)
      ? selectedValue[0]
      : selectedValue;

    if (!selected) {
      return;
    }

    const controlName = this.filterLabels[level];
    const control = this.workflowForm.get(controlName);

    if (control) {
      control.setValue(selected);
    }

    // Reset controls and options for levels below the current one
    for (let i = level + 1; i < this.levels.length; i++) {
      this.resetControlAtLevel(i);
      this.levelOptionsMap[i] = [];
    }

    const selectedNumber = Number(selected);
    if (!isNaN(selectedNumber)) {
      this.fetchChildOptions(level + 1, selectedNumber);
    }
  }

  resetControlAtLevel(level: number): void {
    const controlName = this.filterLabels[level];
    const control = this.workflowForm.get(controlName);
    if (control) {
      control.setValue(null);
    }
  }

  getOptionsForLevel(level: number): any[] {
    return this.levelOptionsMap[level] || [];
  }

  fetchChildOptions(level: number, parentId: number) {
    if (!this.filterLabels[level]) return;

    this.workflowService.getDropdownList(level, parentId).subscribe({
      next: (res) => {
        this.levelOptionsMap[level] = Array.isArray(res) ? res : [];
      },
      error: () => {
        this.levelOptionsMap[level] = [];
      },
    });
  }
  ngOnDestroy(): void {
    // Clean up subscriptions
    this.subscriptions.forEach((sub) => sub.unsubscribe());
    this.workflowGraphService.clearWorkflow();
  }

  createNewNode(agent: any, position: { x: number; y: number }) {
    const agentTools = agent?.tools || [];
    const agentUserTools = agent?.userTools || [];
    const tools = [...agentTools, ...agentUserTools]
      .map((tool) => tool?.toolName)
      .join(', ');
    const newNode: WorkflowNode = {
      id: this.workflowGraphService.generateNodeId(),
      type: 'agent',
      data: {
        label: agent.name,
        agentId: agent.id,
        agentName: agent.name,
        description: agent.description,
        capabilities: agent.capabilities,
        width: 280, // Default width
        model: agent?.modelDetails?.modelDeploymentName,
        tools,
      },
      position: position,
    };

    this.workflowGraphService.addNode(newNode);

    if (this.getControl('enableManagerLLM').value) return;
    const nodes = this.workflowGraphService.getAllNodes();
    const lastNode = nodes.at(-2);
    if (lastNode) {
      this.workflowGraphService.addEdge({
        id: `${lastNode.id}-${newNode.id}`,
        source: lastNode.id,
        target: newNode.id,
      });
    }
  }

  // Canvas board event handlers
  onCanvasDropped(event: {
    event: DragEvent;
    position: { x: number; y: number };
  }): void {
    const dragEvent = event.event;
    const position = event.position;

    if (dragEvent.dataTransfer) {
      const agentData = dragEvent.dataTransfer.getData('application/reactflow');

      if (agentData) {
        try {
          const agent = JSON.parse(agentData);
          // const agentTools = agent?.tools || [];
          // const agentUserTools = agent?.userTools || [];
          // const tools = [...agentTools, ...agentUserTools]
          //   .map((tool) => tool?.toolName)
          //   .join(', ');

          // // Create a new node for the agent
          // const newNode: WorkflowNode = {
          //   id: this.workflowGraphService.generateNodeId(),
          //   type: 'agent',
          //   data: {
          //     label: agent.name,
          //     agentId: agent.id,
          //     agentName: agent.name,
          //     description: agent.description,
          //     capabilities: agent.capabilities,
          //     width: 280, // Default width
          //     model: agent?.modelDetails?.modelDeploymentName,
          //     tools,
          //   },
          //   position: position,
          // };

          // this.workflowGraphService.addNode(newNode);
          // const nodes = this.workflowGraphService.getAllNodes();
          // const lastNode = nodes.at(-2);
          // if (lastNode) {
          //   this.workflowGraphService.addEdge({
          //     id: `${lastNode.id}-${newNode.id}`,
          //     source: lastNode.id,
          //     target: newNode.id,
          //   });
          // }

          this.createNewNode(agent, position);
        } catch (error) {
          console.error('Error adding node:', error);
        }
      }
    }
  }

  onConnectionCreated(edge: CanvasEdge): void {
    const newEdge: WorkflowEdge = {
      id: edge.id,
      source: edge.source,
      target: edge.target,
      animated: edge.animated || true,
    };

    this.workflowGraphService.addEdge(newEdge);
  }

  filterAgents(filterValue: string): void {
    // First filter by search term
    if (!filterValue || filterValue.trim() === '') {
      this.filteredAgents = [...this.agents];
    } else {
      filterValue = filterValue.toLowerCase().trim();
      this.filteredAgents = this.agents.filter(
        (agent) =>
          agent.name.toLowerCase().includes(filterValue) ||
          agent.description.toLowerCase().includes(filterValue) ||
          (agent.type && agent.type.toLowerCase().includes(filterValue)) ||
          (agent.capabilities &&
            agent.capabilities.some((cap) =>
              cap.toLowerCase().includes(filterValue),
            )),
      );
    }

    // Then filter out agents that are already used
    this.updateAvailableAgents();
  }

  updateAvailableAgents(): void {
    this.availableAgents = this.filteredAgents.filter(
      (agent) => !this.usedAgentIds.has(agent.id),
    );
  }

  onDragStart(event: DragEvent, agent: Agent): void {
    if (event.dataTransfer) {
      event.dataTransfer.setData(
        'application/reactflow',
        JSON.stringify(agent),
      );
      event.dataTransfer.effectAllowed = 'move';
    }
  }

  onDeleteNode(nodeId: string): void {
    this.workflowGraphService.removeNode(nodeId);

    // If the deleted node was selected, clear selection
    if (this.selectedNodeId === nodeId) {
      this.selectedNodeId = null;
    }
  }

  onNodeSelected(nodeId: string): void {
    this.selectedNodeId = nodeId;
  }

  onNodeMoved(data: { nodeId: string; position: NodePosition }): void {
    // Find the node index
    const nodeIndex = this.nodes.findIndex((node) => node.id === data.nodeId);
    if (nodeIndex === -1) return;

    // Create a new array with the updated node
    const updatedNodes = [...this.nodes];
    updatedNodes[nodeIndex] = {
      ...this.nodes[nodeIndex],
      position: data.position,
    };

    // Update the node positions through service
    this.workflowGraphService.updateNodePositions(updatedNodes);

    // Force a change detection cycle
    this.cdr.detectChanges();
  }

  onStartConnection(data: {
    nodeId: string;
    handleType: 'source' | 'target';
    event: MouseEvent;
  }): void {
    // Canvas board handles connection logic
    // This method is called when a connection starts but the canvas board manages the temp connection
  }

  updateNodePosition(data: {
    nodeId: string;
    position: { x: number; y: number };
  }): void {
    // Canvas board handles connection point updates
  }

  onSave(): void {
    const workflowData = {
      ...this.workflowForm.value,
      nodes: this.nodes,
      edges: this.edges,
    };

    console.log('Saving workflow:', workflowData);

    if (this.isEditMode) {
      console.log('Updating existing workflow');
      // this.workflowService.updateWorkflow(this.workflowId, workflowData);
    } else {
      console.log('Creating new workflow');
      // this.workflowService.createWorkflow(workflowData);
    }

    this.router.navigate(['/build/workflows']);
  }

  onExit(): void {
    this.router.navigate(['/build/workflows']);
  }

  onReset(): void {
    this.workflowGraphService.clearWorkflow();
    this.selectedNodeId = null;
  }

  onUndo(): void {
    console.log('Undo triggered from workflow editor');
  }

  onRedo(): void {
    console.log('Redo triggered from workflow editor');
  }

  onCanvasStateChanged(state: {
    nodes: CanvasNode[];
    edges: CanvasEdge[];
  }): void {
    console.log('Canvas state changed:', state);

    // Convert canvas nodes back to workflow nodes
    const workflowNodes: WorkflowNode[] = state.nodes.map((node) => ({
      id: node.id,
      type: node.type,
      data: node.data,
      position: node.position,
    }));

    // Convert canvas edges back to workflow edges
    const workflowEdges: WorkflowEdge[] = state.edges.map((edge) => ({
      id: edge.id,
      source: edge.source,
      target: edge.target,
      animated: edge.animated,
    }));

    // Update the workflow service to sync the state
    this.workflowGraphService.setNodes(workflowNodes);
    this.workflowGraphService.setEdges(workflowEdges);
  }

  makeWorkFlowPayload() {
    const formValue = this.workflowForm.getRawValue();

    const payload: Record<string, any> = {
      name: formValue?.name || null,
      description: null,
      enableAgenticMemory: false,
      masterEmbedding: {
        modelRef: null,
      },
      userSignature: this.tokenStorage.getDaUsername(),
      levelId: 199,
    };

    const nodes = this.workflowGraphService.getAllNodes();

    if (nodes.length) {
      payload['pipeLineAgents'] = nodes.map((node, index) => ({
        serial: index + 1,
        agentId: node.data.agentId,
      }));
    }

    if (this.workflowId && !this.isModeDuplicate()) {
      payload['pipelineId'] = this.workflowId;
    }

    if (formValue.modelDeploymentName) {
      payload['managerLlm'] = {
        modelDeploymentName: formValue.modelDeploymentName,
        temperature: formValue.temperature,
        topP: formValue.topP,
        maxRPM: formValue.maxRPM,
        maxToken: formValue.maxToken,
        maxIteration: formValue.maxIteration,
        maxExecutionTime: formValue.maxExecutionTime,
      };
    }

    return payload;
  }

  onExecute(): void {
    // Navigate to workflow execution page
    const name = this.getControl('name').value;
    if (!name?.trim()) {
      this.showWarningPopup = true;
      return;
    }
    const payload = this.makeWorkFlowPayload();
    if (this.workflowId) {
      this.workflowService.updateWorkFlow(payload).subscribe(() => {
        this.router.navigate(['/build/workflows/execute', this.workflowId]);
      });
    } else {
      // For new workflows, save first then navigate
      this.workflowService.saveWorkFlow(payload).subscribe((response: any) => {
        this.router.navigate(['/build/workflows/execute', response.pipeLineId]);
      });
      // alert('Please save the workflow before executing it.');
    }
  }

  // Helper method to get form controls easily from the template
  getControl(name: string): FormControl {
    return this.workflowForm.get(name) as FormControl;
  }

  /**
   * Check if an agent is already used in the workflow
   * @param agentId ID of the agent to check
   */
  isAgentUsed(agentId: string): boolean {
    return this.usedAgentIds.has(agentId);
  }

  /**
   * Update the set of used agent IDs
   */
  updateUsedAgentIds(): void {
    this.usedAgentIds.clear();
    this.nodes.forEach((node) => {
      if (node.data && node.data.agentId) {
        this.usedAgentIds.add(node.data.agentId);
      }
    });

    // Update available agents whenever used agents change
    this.updateAvailableAgents();
  }
  savedEdges: WorkflowEdge[] = [];
  onToggleChnage(event: boolean) {
    this.getControl('enableManagerLLM').setValue(event);
    this.showLlm = event;
    if (event) {
      this.savedEdges = this.workflowGraphService.getAllEdges();
      this.workflowGraphService.setEdges([]);
    } else {
      const nodes = this.workflowGraphService.getAllNodes();
      const edges = this.convertNodesToEdges(nodes);
      this.workflowGraphService.setEdges(edges);
      if (!this.savedWorkFlowDetils['managerLlm']) {
        this.getControl('modelDeploymentName').setValue(null);
      }
    }
  }
  toggleLlm(show: boolean) {
    this.showLlm = show;
  }
}
