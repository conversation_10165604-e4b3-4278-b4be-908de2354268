import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  ElementRef,
  ViewChild,
  AfterViewInit,
  OnDestroy,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { WorkflowNode } from '../../services/workflow-graph.service';
import { DragDropModule, CdkDragEnd } from '@angular/cdk/drag-drop';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-agent-node',
  standalone: true,
  imports: [CommonModule, DragDropModule],
  template: `
    <div
      #nodeElement
      class="agent-node"
      [class.selected]="selected"
      [class.expanded]="isExpanded"
      [attr.data-node-id]="node.id"
      cdkDrag
      cdkDragBoundary=".canvas-container"
      [cdkDragFreeDragPosition]="{ x: node.position.x, y: node.position.y }"
      (cdkDragEnded)="onDragEnded($event)"
      (mousedown)="onNodeMouseDown($event)"
    >
      <!-- Drag handle for the entire node -->
      <div class="drag-handle" cdkDragHandle>
        <svg
          width="12"
          height="12"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
        >
          <circle cx="8" cy="8" r="1.5"></circle>
          <circle cx="16" cy="8" r="1.5"></circle>
          <circle cx="8" cy="16" r="1.5"></circle>
          <circle cx="16" cy="16" r="1.5"></circle>
        </svg>
      </div>

      <!-- Close button positioned at top right -->
      <button
        class="delete-btn"
        (click)="onDelete($event)"
        aria-label="Delete node"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="12"
          height="12"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          aria-hidden="true"
        >
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>

      <!-- Node Header with Toggle -->
      <div class="node-header" cdkDragHandle>
        <div class="header-content">
          <div class="header-icon">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <rect x="3" y="3" width="7" height="7"></rect>
              <rect x="14" y="3" width="7" height="7"></rect>
              <rect x="14" y="14" width="7" height="7"></rect>
              <rect x="3" y="14" width="7" height="7"></rect>
            </svg>
          </div>
          <span class="node-title">{{ node.data['label'] }}</span>
        </div>
        <button
          class="toggle-btn"
          (click)="toggleExpanded($event)"
          aria-label="Toggle details"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="14"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            [class.expanded]="isExpanded"
            aria-hidden="true"
          >
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
        </button>
      </div>

      <!-- Expanded Details -->
      <div class="node-details" *ngIf="isExpanded">
        <!-- Prompt -->
        <div class="detail-item">
          <div class="detail-label">Prompt</div>
          <div class="dropdown-field">
            <div class="dropdown-content">
              <span class="dropdown-text">{{
                node.data['description'] || 'Translating user needs into...'
              }}</span>
              <button class="copy-btn" aria-label="Copy prompt">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="12"
                  height="12"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                  <path
                    d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Model -->
        <div class="detail-item">
          <div class="detail-label">Model</div>
          <div class="dropdown-field">
            <div class="dropdown-content">
              <div class="model-info">
                <div class="model-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="12"></line>
                    <line x1="12" y1="16" x2="12.01" y2="16"></line>
                  </svg>
                </div>
                <span class="model-name">{{ node.data['model'] }}</span>
              </div>
              <button class="copy-btn" aria-label="Copy model info">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="12"
                  height="12"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                  <path
                    d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Knowledge Base -->
        <div class="detail-item">
          <div class="detail-label">Knowledge base</div>
          <div class="dropdown-field empty">
            <span class="empty-text">No Data</span>
          </div>
        </div>

        <!-- Tools -->
        <div class="detail-item">
          <div class="detail-label">Tools</div>
          <div class="dropdown-field empty">
            <span
              class="empty-text"
              *ngIf="node.data['tools'] as tools; else noDataRef"
              >{{ tools }}</span
            >

            <ng-template #noDataRef> <span> No Data</span> </ng-template>
          </div>
        </div>
      </div>

      <div class="node-handles">
        <div
          class="handle handle-left"
          [attr.data-handle-id]="node.id + '-left'"
          (mousedown)="onHandleMouseDown($event, 'target')"
          role="button"
          aria-label="Connect from left"
        ></div>
        <div
          class="handle handle-right"
          [attr.data-handle-id]="node.id + '-right'"
          (mousedown)="onHandleMouseDown($event, 'source')"
          role="button"
          aria-label="Connect from right"
        ></div>
      </div>
    </div>
  `,
  styles: [
    `
      .agent-node {
        width: 280px;
        min-height: 48px;
        background-color: #ffffff;
        border-radius: 8px;
        border: 2px solid #e5e7eb;
        overflow: visible;
        box-shadow:
          0 4px 6px -1px rgba(0, 0, 0, 0.1),
          0 2px 4px -1px rgba(0, 0, 0, 0.06);
        position: absolute;
        user-select: none;
        cursor: move;
        touch-action: none;
        z-index: 10;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
      }

      .agent-node.expanded {
        height: 400px;
        // overflow: hidden;
        z-index: 1000;
      }

      .agent-node.selected {
        border-color: #6366f1;
        box-shadow:
          0 10px 15px -3px rgba(0, 0, 0, 0.1),
          0 4px 6px -2px rgba(0, 0, 0, 0.05);
        z-index: 20;
      }

      .drag-handle {
        position: absolute;
        top: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 20px;
        background-color: #6366f1;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        cursor: grab;
        z-index: 25;
        opacity: 0;
        transition: opacity 0.2s ease;
      }

      .agent-node:hover .drag-handle {
        opacity: 1;
      }

      .agent-node.selected .drag-handle {
        opacity: 1;
      }

      .delete-btn {
        position: absolute;
        top: -8px;
        right: -8px;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #ef4444;
        border: none;
        border-radius: 50%;
        padding: 0;
        color: white;
        cursor: pointer;
        z-index: 999;
        transition: all 0.2s ease;
        display: none;
      }

      .agent-node.selected .delete-btn {
        display: flex;
      }

      .delete-btn:hover {
        background-color: #dc2626;
      }

      .node-header {
        background-color: #f8fafc;
        padding: 12px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: move;
        border-radius: 6px;
        transition: border-radius 0.3s ease;
      }

      .agent-node.expanded .node-header {
        border-bottom: 1px solid #e5e7eb;
        border-radius: 6px 6px 0 0;
      }

      .header-content {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-grow: 1;
      }

      .header-icon {
        width: 16px;
        height: 16px;
        color: #6b7280;
        flex-shrink: 0;
      }

      .node-title {
        font-weight: 600;
        font-size: 14px;
        color: #111827;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex-grow: 1;
        max-width: 180px;
      }

      .toggle-btn {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: transparent;
        border: none;
        border-radius: 4px;
        padding: 0;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.2s ease;
        flex-shrink: 0;
      }

      .toggle-btn:hover {
        color: #374151;
        background-color: #f3f4f6;
      }

      .toggle-btn svg {
        transition: transform 0.2s ease;
      }

      .toggle-btn svg.expanded {
        transform: rotate(180deg);
      }

      .node-details {
        padding: 16px;
        display: flex;
        flex-direction: column;
        gap: 12px;
        flex-grow: 1;
        overflow-y: auto;
        opacity: 1;
        transition: opacity 0.3s ease;
      }

      .agent-node:not(.expanded) .node-details {
        display: none;
      }

      .detail-item {
        display: flex;
        flex-direction: column;
        gap: 6px;
      }

      .detail-label {
        font-size: 12px;
        font-weight: 500;
        color: #374151;
        margin: 0;
      }

      .dropdown-field {
        background-color: #f9fafb;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        padding: 8px 12px;
        min-height: 32px;
        display: flex;
        align-items: center;
        position: relative;
      }

      .dropdown-field.empty {
        color: #9ca3af;
        font-size: 13px;
      }

      .dropdown-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
      }

      .dropdown-text {
        font-size: 13px;
        color: #374151;
        flex-grow: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .empty-text {
        font-size: 13px;
        color: #9ca3af;
      }

      .model-info {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-grow: 1;
      }

      .model-icon {
        width: 20px;
        height: 20px;
        background-color: #6366f1;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        flex-shrink: 0;
      }

      .model-name {
        font-size: 13px;
        color: #374151;
        font-weight: 500;
      }

      .copy-btn {
        background: none;
        border: none;
        color: #9ca3af;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        flex-shrink: 0;
      }

      .copy-btn:hover {
        color: #6b7280;
        background-color: #f3f4f6;
      }

      .node-handles {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
      }

      .handle {
        position: absolute;
        width: 8px;
        height: 8px;
        background-color: #6366f1;
        border: 1px solid #ffffff;
        border-radius: 2px;
        pointer-events: all;
        cursor: crosshair;
        z-index: 15;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .handle-left {
        top: 50%;
        left: -4px;
        transform: translateY(-50%);
      }

      .handle-right {
        top: 50%;
        right: -4px;
        transform: translateY(-50%);
      }

      .cdk-drag-placeholder {
        opacity: 0.3;
        background-color: #f3f4f6;
        border: 2px dashed #6366f1;
        border-radius: 12px;
      }

      .cdk-drag-animating {
        transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
      }

      .cdk-drag-preview {
        box-shadow:
          0 20px 25px -5px rgba(0, 0, 0, 0.1),
          0 10px 10px -5px rgba(0, 0, 0, 0.04);
        border-color: #6366f1;
        transform: rotate(2deg);
      }
    `,
  ],
})
export class AgentNodeComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('nodeElement') nodeElement!: ElementRef;
  @Input() node!: WorkflowNode;
  @Input() selected: boolean = false;
  @Output() deleteNode = new EventEmitter<string>();
  @Output() moveNode = new EventEmitter<{
    nodeId: string;
    position: { x: number; y: number };
  }>();
  @Output() nodeSelected = new EventEmitter<string>();
  @Output() startConnection = new EventEmitter<{
    nodeId: string;
    handleType: 'source' | 'target';
    event: MouseEvent;
  }>();

  @Output() nodePositionChanged = new EventEmitter<{
    nodeId: string;
    position: { x: number; y: number };
  }>();

  // Whether the node details are expanded - starts collapsed
  isExpanded: boolean = false;

  // Track if we're dragging to avoid toggles during drag operations
  private isDragging: boolean = false;

  // Fixed node width
  nodeWidth: number = 280;

  // For cleanup
  private destroy$ = new Subject<void>();

  constructor(private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    // Fixed width - no resizing allowed
    this.nodeWidth = 280;

    // Emit initial position for connection points setup
    setTimeout(() => {
      this.nodePositionChanged.emit({
        nodeId: this.node.id,
        position: this.node.position,
      });
    }, 0);
  }

  ngAfterViewInit(): void {
    // No additional initialization needed
  }

  ngOnDestroy(): void {
    // Cleanup subscriptions
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleExpanded(event: MouseEvent): void {
    event.stopPropagation();
    this.isExpanded = !this.isExpanded;
  }

  onNodeMouseDown(event: MouseEvent): void {
    // Start tracking potential drag
    this.isDragging = true;

    // Don't select when clicking on handle or interactive elements
    const targetElement = event.target as HTMLElement;
    if (
      targetElement.classList.contains('handle') ||
      targetElement.classList.contains('delete-btn') ||
      targetElement.closest('.delete-btn') ||
      targetElement.classList.contains('toggle-btn') ||
      targetElement.closest('.toggle-btn') ||
      targetElement.classList.contains('copy-btn') ||
      targetElement.closest('.copy-btn')
    ) {
      return;
    }

    // Select the node
    this.nodeSelected.emit(this.node.id);
  }

  onHandleMouseDown(event: MouseEvent, handleType: 'source' | 'target'): void {
    // Prevent parent node drag
    event.stopPropagation();

    // Select this node
    this.nodeSelected.emit(this.node.id);

    // Make sure position is up to date before starting connection
    this.nodePositionChanged.emit({
      nodeId: this.node.id,
      position: this.node.position,
    });

    // Emit connection start event
    this.startConnection.emit({
      nodeId: this.node.id,
      handleType,
      event,
    });
  }

  onDragEnded(event: CdkDragEnd): void {
    // End of dragging
    this.isDragging = false;

    // Calculate the final position after drag
    const transform = event.source.getFreeDragPosition();

    // Create a safe position (not negative)
    const safePosition = {
      x: Math.max(0, transform.x),
      y: Math.max(0, transform.y),
    };

    // Emit final position to parent
    this.moveNode.emit({
      nodeId: this.node.id,
      position: safePosition,
    });

    // Also emit position changed for connection points
    this.nodePositionChanged.emit({
      nodeId: this.node.id,
      position: safePosition,
    });
  }

  onDelete(event: MouseEvent): void {
    event.stopPropagation();
    this.deleteNode.emit(this.node.id);
  }
}
