import { Component, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { Router } from '@angular/router';
import { PageFooterComponent } from '../../shared/components/page-footer/page-footer.component';
import { PaginationService } from '../../shared/services/pagination.service';
import { WorkflowService } from '../../shared/services/workflow.service';
import {
  AvaTextboxComponent,
  DropdownComponent,
  DropdownOption,
  IconComponent,
  TextCardComponent,
} from '@ava/play-comp-library';
import { LucideAngularModule } from 'lucide-angular';
import workflowLabels from './constants/workflows.json';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { startWith, debounceTime, distinctUntilChanged, map } from 'rxjs';
import { WorkflowModes } from './constants/workflow.constants';

@Component({
  selector: 'app-workflows',
  standalone: true,
  imports: [
    CommonModule,
    PageFooterComponent,
    TextCardComponent,
    AvaTextboxComponent,
    DropdownComponent,
    LucideAngularModule,
    IconComponent,
    ReactiveFormsModule,
  ],
  providers: [DatePipe],
  templateUrl: './workflows.component.html',
  styleUrl: './workflows.component.scss',
})
export class WorkflowsComponent implements OnInit {
  public workFlowLabels = workflowLabels.labels;
  allWorkflows: any[] = [];
  filteredWorkflows: any[] = [];
  displayedWorkflows: any[] = [];
  isLoading: boolean = false;
  error: string | null = null;
  currentPage: number = 1;
  itemsPerPage: number = 12;
  totalPages: number = 1;
  workflowsOptions: DropdownOption[] = [
    { name: 'All', value: 'all' },
    { name: 'Type A', value: 'typeA' },
    { name: 'Type B', value: 'typeB' },
  ];
  selectedData: any = null;
  iconList = [
    { name: 'square-pen', iconName: 'square-pen', cursor: true },
    { name: 'trash', iconName: 'trash-2', cursor: true },
    { name: 'copy', iconName: 'copy', cursor: true },
    { name: 'play', iconName: 'play', cursor: true },
  ];
  searchForm!: FormGroup;
  search: any;
  cardSkeletonPlaceholders = Array(11);

  constructor(
    private paginationService: PaginationService,
    private router: Router,
    private workflowService: WorkflowService,
    private datePipe: DatePipe,
    private fb: FormBuilder,
  ) {
    this.searchForm = this.fb.group({
      search: [''],
    });
  }

  ngOnInit(): void {
    this.searchList();
    this.fetchAllWorkflows();
  }

  fetchAllWorkflows() {
    this.isLoading = true;
    this.workflowService.fetchAllWorkflows().subscribe({
      next: (response: any) => {
        const pipeLines = response.pipeLines || response;
        this.allWorkflows = pipeLines.map((item: any) => ({
          ...item,
          id: item.pipelineId,
          title: item.name,
          name: item.name,
          description: item.description || 'No description',
          createdDate:
            this.datePipe.transform(item.createdAt, 'MM/dd/yyyy') || '',
        }));
        this.filteredWorkflows = [...this.allWorkflows];
        this.updateDisplayedWorkflows();
        this.isLoading = false;
      },
      error: (error) => {
        this.error = error.message || 'Failed to load workflows';
        this.isLoading = false;
      },
    });
  }

  searchList() {
    this.searchForm
      .get('search')!
      .valueChanges.pipe(
        startWith(''),
        debounceTime(300),
        distinctUntilChanged(),
        map((value) => value?.toLowerCase() ?? ''),
      )
      .subscribe((searchText) => {
        this.filterWorkflow(searchText);
      });
  }

  filterWorkflow(searchText: string): void {
    this.filteredWorkflows = this.allWorkflows.filter((wf) => {
      const inTitle = wf.title?.toLowerCase().includes(searchText);
      const inDescription = wf.description?.toLowerCase().includes(searchText);
      const inTags =
        Array.isArray(wf.tags) &&
        wf.tags?.some((tag: any) =>
          tag.label?.toLowerCase().includes(searchText),
        );

      return inTitle || inDescription || inTags;
    });

    this.updateDisplayedWorkflows();
  }

  updateDisplayedWorkflows(): void {
    const paginationResult = this.paginationService.getPaginatedItems(
      this.filteredWorkflows,
      this.currentPage,
      this.itemsPerPage,
    );
    this.displayedWorkflows = paginationResult.displayedItems;
    this.totalPages = paginationResult.totalPages;
  }

  onCreateWorkflow(): void {
    this.router.navigate(['/build/workflows/create']);
  }

  editWorkflow(workflowId: string): void {
    this.router.navigate(['/build/workflows/edit', workflowId]);
  }

  getHeaderIcons(workflow: any): { iconName: string; title: string }[] {
    return [
      { iconName: 'wrench', title: workflow.toolType || 'Workflows' },
      { iconName: 'users', title: `${workflow.userCount || 40}` },
    ];
  }

  getFooterIcons(workflow: any): { iconName: string; title: string }[] {
    return [
      { iconName: 'user', title: workflow.owner || 'AAVA' },
      { iconName: 'calendar-days', title: workflow.createdDate },
    ];
  }

  onIconClicked(icon: any, workflowId: string): void {
    switch (icon.name) {
      case 'square-pen':
        this.editWorkflow(workflowId);
        break;
      case 'trash':
        this.deleteWorkflow(workflowId);
        break;
      case 'copy':
        this.duplicateWorkflow(workflowId);
        break;
      case 'play':
        this.executePrompt(workflowId);
        break;
      default:
        break;
    }
  }

  executePrompt(workflowId: string): void {
    console.log('Executing workflow:', workflowId);
    this.router.navigate(['/build/workflows/execute', workflowId]);
  }

  deleteWorkflow(workflowId: string): void {
    const selectedWorkflow = this.filteredWorkflows.find(
      (w) => w.id === workflowId,
    );
    if (selectedWorkflow) {
      this.workflowService.deleteWorkflowById(workflowId).subscribe((res) => {
        if (res) {
          this.fetchAllWorkflows();
        }
      });
    }
  }

  duplicateWorkflow(workflowId: string): void {
    // Implement duplicate logic
    this.router.navigate(['/build/workflows/create'], {
      queryParams: {
        id: workflowId,
        mode: WorkflowModes.duplicate,
      },
    });
  }

  onSelectionChange(data: any) {
    this.selectedData = data;
    // Implement filter logic if needed
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedWorkflows();
  }

  get showCreateCard(): boolean {
    return this.currentPage === 1 && !this.isLoading && !this.error;
  }
}
