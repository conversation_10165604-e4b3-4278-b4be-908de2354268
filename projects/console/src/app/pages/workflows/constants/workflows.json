{"labels": {"workflowName": "Workflow Name", "workflowPlaceholder": "Enter workflow name", "description": "Description", "loadingText": "Loading Workflow data...", "agentLibrary": "Agent Library", "agentLibraryHelpText": "Drag and drop agents to establish connection and execute workflow", "searchAgents": "Search agents", "assignFilters": "Assign <PERSON>s", "organization": "Organization", "domain": "Domain", "organizationPlaceholder": "Select organization", "describeWorkflow": "Describe your workflow's purpose", "noResults": "No agents found matching your search criteria", "filters": "Filters", "managerLLMToggleLabel": "Enable Manager LLM", "temperature": "Temperature", "topP": "Top P", "maxRPM": "Max RPM", "maxToken": "<PERSON>", "maxIteration": "Max Iteration", "maxExecutionTime": "Max Execution Time", "workflowEditor": "Workflow Editor", "alt": "Alt", "drag": "Drag", "toPanCanvas": "to pan canvas", "mouseWheel": "Mouse Wheel", "toZoom": "to zoom", "space": "Space", "toResetView": "to reset view", "tokensUsed": "8192 Tokens used", "execute": "Execute", "fallbackMessage": "Drag agents from the library to create a workflow", "model": "Model", "workflowLog": "Error connecting to the WebSocket. Please contact the admin.", "workflowLogFailed": "Workflow execution has failed.", "workflowExecProcessing": "Workflow execution is still processing...", "workflowExecComplete": "Workflow execution is complete."}}