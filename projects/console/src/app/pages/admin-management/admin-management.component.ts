import { Component } from '@angular/core';
import { TableGridComponent } from '../../shared/components/table-grid/table-grid.component';
import { AdminManagementCellComponent } from './renderer/action-management-renderer.component';
import { columnDefs } from '../../shared/components/table-grid/model/table-grid.model';

@Component({
  selector: 'app-admin-management',
  imports: [TableGridComponent],
  templateUrl: './admin-management.component.html',
  styleUrl: './admin-management.component.scss',
})
export class AdminManagementComponent {
  columns: columnDefs[] = [
    {
      header: 'Name',
      field: 'name',
      cellRenderer: AdminManagementCellComponent,
    },
    { header: 'Email', field: 'email' },
    { header: 'Created On', field: 'createdOn' },
    { header: 'Validity Till', field: 'validityTill' },
    { header: 'Status', field: 'status' },
    { header: 'Authorized By', field: 'authorizedBy' },
    {
      header: 'Action',
      field: 'action',
      cellRenderer: AdminManagementCellComponent,
      maxWidth: 120,
      cellRendererParams: { context: { componentParent: this } },
    },
  ];

  rows = [
    {
      name: 'Project Alpha',
      email: '<EMAIL>',
      createdOn: '01/01/2023',
      validityTill: '01/01/2023',
      status: 'Active',
      authorizedBy: 'John',
    },
    {
      name: 'Project Alpha',
      email: '<EMAIL>',
      createdOn: '01/01/2023',
      validityTill: '01/01/2023',
      status: 'Active',
      authorizedBy: 'John',
    },
    {
      name: 'Project Alpha',
      email: '<EMAIL>',
      createdOn: '01/01/2023',
      validityTill: '01/01/2023',
      status: 'Active',
      authorizedBy: 'John',
    },
    {
      name: 'Project Alpha',
      email: '<EMAIL>',
      createdOn: '01/01/2023',
      validityTill: '01/01/2023',
      status: 'Active',
      authorizedBy: 'John',
    },
    {
      name: 'Project Alpha',
      email: '<EMAIL>',
      createdOn: '01/01/2023',
      validityTill: '01/01/2023',
      status: 'Active',
      authorizedBy: 'John',
    },
  ];

  deleteUser(value: string) {
    console.log('deleted successfully ->', value);
  }
}
