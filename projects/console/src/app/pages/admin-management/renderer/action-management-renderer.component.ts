import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import {
  CellRenderer,
  CellRendererParams,
} from '../../../shared/components/table-grid/model/table-grid.model';
import { IconsComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-status-cell',
  standalone: true,
  imports: [CommonModule, IconsComponent],
  template: `
    @if (params.colDef.header === 'Name') {
      <div class="name-container">{{ params.value }}</div>
    } @else {
      <awe-icons
        class="delete"
        (click)="deleteUser()"
        iconName="awe_trash"
        iconcolor="danger"
      ></awe-icons>
    }
  `,
  styles: [
    `
      .name-container,
      .delete {
        cursor: pointer;
      }
    `,
  ],
})
export class AdminManagementCellComponent implements CellRenderer {
  params!: any;

  aweInit(params: CellRendererParams) {
    this.params = params;
  }

  deleteUser() {
    this.params.context.componentParent.deleteUser(this.params.rowData.name);
  }
}
