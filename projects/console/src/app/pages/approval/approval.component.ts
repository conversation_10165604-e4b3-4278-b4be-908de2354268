import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';
import { CommonModule, formatDate } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import {
  ApprovalCardComponent,
  AvaTextboxComponent,
  DropdownComponent,
  DropdownOption,
  IconComponent,
  TextCardComponent,
  ButtonComponent,
  PopupComponent,
  ConfirmationPopupComponent,
  AvaTagComponent,
} from '@ava/play-comp-library';
import { ApprovalService } from '../../shared/services/approval.service';
import { SharedApiServiceService } from '../../shared/services/shared-api-service.service';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { startWith, debounceTime, distinctUntilChanged, map } from 'rxjs';
import approvalText from './constants/approval.json'

type RequestStatus = 'approved' | 'rejected' | 'review';
@Component({
  selector: 'app-approval',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ApprovalCardComponent,
    ButtonComponent,
    IconComponent,
    DropdownComponent,
    AvaTextboxComponent,
    TextCardComponent,
    PopupComponent,
    ConfirmationPopupComponent,
    ReactiveFormsModule,
    AvaTagComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './approval.component.html',
  styleUrl: './approval.component.scss',
})
export class ApprovalComponent implements OnInit {
  // Labels from constants file
  appLabels = approvalText.labels;

  public searchValue: string = '';
  public totalApprovedApprovals: number = 20;
  public totalPendingApprovals: number = 15;
  public totalApprovals: number = 60;
  public isBasicCollapsed: boolean = false;
  public quickActionsExpanded: boolean = true;
  public consoleApproval: any = {};
  public options: DropdownOption[] = [];
  public basicSidebarItems: any[] = [];
  public quickActions: any[] = [];
  public toolReviews: any[] = [];
  public workflowReviews: any[] = [];
  public agentsReviews: any[] = [];
  public currentToolsPage = 1;
  public currentAgentsPage = 1;
  public currentWorkflowsPage = 1;
  public pageSize = 50;
  public totalRecords = 0;
  public isDeleted = false;
  public currentTab = 'Agents';
  public showToolApprovalPopup = false;
  public showInfoPopup = false;
  public showErrorPopup = false;
  public infoMessage = '';
  public selectedIndex = 0;
  public showFeedbackPopup = false;
  public searchForm!: FormGroup;
  public labels: any = approvalText.labels;
  public approvedAgentId: number | null = null;

  constructor(
    private router: Router,
    private apiService: SharedApiServiceService,
    private approvalService: ApprovalService,
    private fb: FormBuilder,
  ) {
    this.labels = approvalText.labels;
    this.options = [
      { name: this.labels.electronics, value: 'electronics' },
      { name: this.labels.clothing, value: 'clothing' },
      { name: this.labels.books, value: 'books' },
    ];
    this.basicSidebarItems = [
      { id: '1', icon: 'hammer', text: this.labels.agents, route: '', active: true },
      { id: '2', icon: 'circle-check', text: this.labels.workflows, route: '' },
      { id: '3', icon: 'bot', text: this.labels.tools, route: '' },
    ];
    this.quickActions = [
      {
        icon: 'awe_agents',
        label: this.labels.agents,
        route: '',
      },
      {
        icon: 'awe_workflows',
        label: this.labels.workflows,
        route: '',
      },
      {
        icon: 'awe_tools',
        label: this.labels.tools,
        route: '',
      },
    ];
    this.searchForm = this.fb.group({
      search: [''],
    });
  }

  ngOnInit(): void {
    this.searchList();
    this.totalApprovals = 60;
    this.loadAgentsReviews();
  }

  public searchList() {
    console.log(this.searchForm.get('search')?.value);
    this.searchForm
      .get('search')!
      .valueChanges.pipe(
        startWith(''),
        debounceTime(300),
        distinctUntilChanged(),
        map((value) => value?.toLowerCase() ?? ''),
      )
      .subscribe((searchText) => {
        this.applyFilter(searchText);
      });
  }

  public applyFilter(text: string) {
    const lower = text;

    if (!this.searchValue) {
      if (this.currentTab === 'Agents') {
        this.updateConsoleApproval(this.agentsReviews, "agent");
      } else if (this.currentTab === 'Tools') {
        this.updateConsoleApproval(this.toolReviews, 'tool');
      } else {
        this.updateConsoleApproval(this.workflowReviews, "workflow");
      }
      return;
    }

    if (this.currentTab === 'Agents') {
      const filtered = this.agentsReviews.filter((item) =>
        item.agentName?.toLowerCase().includes(lower),
      );
      this.updateConsoleApproval(filtered, "agent");
    } else if (this.currentTab === 'Tools') {
      const filtered = this.toolReviews.filter((item) =>
        item.toolName?.toLowerCase().includes(lower),
      );
      this.updateConsoleApproval(filtered, 'tool');
    } else {
      const filtered = this.workflowReviews.filter(item =>
        item.name?.toLowerCase().includes(lower)
      );
      this.updateConsoleApproval(filtered, "workflow");
    }
  }

  public onSelectionChange(data: any) {
    console.log('Selection changed:', data);
  }

  public uClick(i: any) {
    console.log('log' + i);
  }

  public toggleQuickActions(): void {
    this.quickActionsExpanded = !this.quickActionsExpanded;
  }

  public onBasicCollapseToggle(isCollapsed: boolean): void {
    this.isBasicCollapsed = isCollapsed;
    console.log('Basic sidebar collapsed:', isCollapsed);
  }

  public onBasicItemClick(item: any): void {
    this.basicSidebarItems.forEach((i) => (i.active = false));
    item.active = true;
    console.log(item);
  }

  public toRequestStatus(value: string | null | undefined): RequestStatus {
    return value === 'approved' || value === 'rejected' || value === 'review'
      ? value
      : 'review';
  }

  public loadToolReviews() {
    this.approvalService
      .getAllReviewTools(this.currentToolsPage, this.pageSize, this.isDeleted)
      .subscribe((response) => {
        if (this.currentToolsPage > 1) {
          this.toolReviews = [
            ...this.toolReviews,
            ...response.userToolReviewDetails,
          ];
        } else {
          this.toolReviews = response?.userToolReviewDetails;
        }
        this.toolReviews = this.toolReviews.filter(
          (r) => r.status !== 'approved',
        );
        this.totalRecords = this.toolReviews.length;
        this.updateConsoleApproval(this.toolReviews, 'tool');
      });
  }

  public loadWorkflowReviews() {
    this.approvalService
      .getAllReviewWorkflows(this.currentWorkflowsPage, this.pageSize, this.isDeleted)
      .subscribe((response) => {
        if (this.currentWorkflowsPage > 1) {
          this.workflowReviews = [
            ...this.workflowReviews,
            ...response.workflowReviewDetails,
          ];
        } else {
          this.workflowReviews = response?.workflowReviewDetails;
        }
        this.workflowReviews = this.workflowReviews.filter(
          (r) => r.status !== 'approved',
        );
        this.totalRecords = this.workflowReviews.length;
        console.log('reviews ', this.workflowReviews);
        this.updateConsoleApproval(this.workflowReviews, 'workflow');
    });
  }

  public loadAgentsReviews() {
    this.approvalService
      .getAllReviewAgents(this.currentAgentsPage, this.pageSize, this.isDeleted)
      .subscribe((response) => {
        if (this.currentAgentsPage > 1) {
          this.agentsReviews = [
            ...this.agentsReviews,
            ...response.agentReviewDetails,
          ];
        } else {
          this.agentsReviews = response?.agentReviewDetails;
        }
        this.agentsReviews = this.agentsReviews.filter(
          (r) => r.status !== 'approved',
        );
        this.totalRecords = this.agentsReviews.length;
        this.updateConsoleApproval(this.agentsReviews, 'agent');
      });
  }

  public loadMoreTools(page: number) {
    this.currentToolsPage = page;
    this.loadToolReviews();
  }

  public loadMoreAgents(page: number) {
    this.currentAgentsPage = page;
    this.loadAgentsReviews();
  }

  public loadMoreWorkflows(page: number) {
    this.currentWorkflowsPage = page;
    this.loadWorkflowReviews();
  }

  public loadReviews(name: string) {
    this.currentTab = name;
    if (name == 'Tools') {
      this.loadToolReviews();
    } else if (name == 'Agents') {
      this.loadAgentsReviews();
    } else {
      this.loadWorkflowReviews();
    }
  }

  public rejectApproval(idx: any) {
    console.log(idx);
    this.selectedIndex = idx;
    this.showFeedbackPopup = true;
  }

  public approveApproval(idx: any) {
    console.log(idx);
    this.selectedIndex = idx;
    this.showToolApprovalPopup = true;
  }

  public handleApproval() {
    if (this.currentTab == 'Tools') {
      this.handleToolApproval();
    } 
    else if (this.currentTab == 'Agents') {
      this.handleAgentApproval();
    } 
    else {
      this.handleWorkflowApproval();
    }
  }

  public handleRejection(feedback: any) {
    if (this.currentTab == 'Tools') {
      this.handleToolRejection(feedback);
    } 
    else if (this.currentTab == 'Agents') {
      this.handleAgentRejection(feedback);
    } 
    else {
      this.handleWorkflowRejection(feedback);
    }
  }

  public handleToolApproval() {
    const toolDetails = this.toolReviews[this.selectedIndex];
    const id = toolDetails.id;
    const toolId = toolDetails.toolId;
    const status = 'approved';
    const reviewedBy = toolDetails.reviewedBy;

    this.approvalService.approveTool(id, toolId, status, reviewedBy).subscribe({
      next: (response: any) => {
        this.infoMessage =
          response?.message || this.labels.toolSuccessApproveMessage;
        this.showInfoPopup = true;
      },
      error: (error) => {
        this.showErrorPopup = true;
        this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;
        console.error('Error:', error);
      },
    });
  }

  public handleToolRejection(feedback: any) {
    const toolDetails = this.toolReviews[this.selectedIndex];
    const id = toolDetails.id;
    const toolId = toolDetails.toolId;
    const status = 'rejected';
    const reviewedBy = toolDetails.reviewedBy;
    const message = feedback;
    
    this.approvalService
      .rejectTool(id, toolId, status, reviewedBy, message)
      .subscribe({
        next: (response: any) => {
          this.infoMessage = response?.message || this.labels.toolSuccessRejectMessage;
          this.showInfoPopup = true;
        },
        error: (error) => {
          this.showErrorPopup = true;
          this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;
          console.error('Error:', error);
        },
      });
  }

  public handleAgentApproval() {
    const agentDetails = this.agentsReviews[this.selectedIndex];
    const id = agentDetails.id;
    const agentId = agentDetails.agentId;
    const status = 'approved';
    const reviewedBy = agentDetails.reviewedBy;
    
    this.approvalService.approveAgent(id, agentId, status, reviewedBy).subscribe({
      next: (response: any) => {
        this.infoMessage =
          response?.message || this.labels.agentSuccessApproveMessage;
        this.showInfoPopup = true;

        // Store agent ID for navigation after popup confirmation
        this.approvedAgentId = agentId;
      },
      error: (error) => {
        this.showErrorPopup = true;
        this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;
        console.error('Error:', error);
      },
    });
  }

  public handleAgentRejection(feedback: any) {
    const agentDetails = this.agentsReviews[this.selectedIndex];
    const id = agentDetails.id;
    const agentId = agentDetails.agentId;
    const status = 'rejected';
    const reviewedBy = agentDetails.reviewedBy;
    const message = feedback;
    
    this.approvalService
      .rejectAgent(id, agentId, status, reviewedBy, message)
      .subscribe({
        next: (response: any) => {
          this.infoMessage = response?.message || this.labels.agentSuccessRejectMessage;
          this.showInfoPopup = true;
        },
        error: (error) => {
          this.showErrorPopup = true;
          this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;
          console.error('Error:', error);
        },
      });
  }

  public handleWorkflowApproval() {
    const workflowDetails = this.workflowReviews[this.selectedIndex];
    const id = workflowDetails?.id;
    const workflowId = workflowDetails?.workflowId;
    const status = 'approved';
    const reviewedBy = workflowDetails?.reviewedBy;
    console.log(id, workflowId, status, reviewedBy);
    this.approvalService.approveWorkflow(id, workflowId, status, reviewedBy).subscribe({
      next: (response: any) => {
        this.infoMessage =
          response?.message || this.labels.workflowSuccessApproveMessage;
        this.showInfoPopup = true;
      },
      error: (error) => {
        this.showErrorPopup = true;
        this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;
        console.error('Error:', error);
      },
    });
  }

  public handleWorkflowRejection(feedback: any) {
    const workflowDetails = this.workflowReviews[this.selectedIndex];
    const id = workflowDetails?.id;
    const workflowId = workflowDetails?.workflowId;
    const status = 'rejected';
    const reviewedBy = workflowDetails?.reviewedBy;
    const message = feedback;
    console.log(id, workflowId, status, reviewedBy, message);
    this.approvalService.rejectWorkflow(id, workflowId, status, reviewedBy, message)
      .subscribe({
        next: (response: any) => {
          this.infoMessage = response?.message || this.labels.workflowSuccessRejectMessage;
          this.showInfoPopup = true;
        },
        error: (error) => {
          this.showErrorPopup = true;
          this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;
          console.error('Error:', error);
        },
      });
  }

  public handleInfoPopup() {
    this.showInfoPopup = false;

    // Check if we need to navigate to build agent screen after approval
    if (this.currentTab == 'Agents' && this.approvedAgentId) {
      // Navigate to build agent screen with the approved agent ID
      // Route structure: /build/agents/collaborative with query params
      this.router.navigate(['/build/agents/collaborative'], {
        queryParams: {
          id: this.approvedAgentId,
          mode: 'edit'
        }
      });
      // Reset the approved agent ID
      this.approvedAgentId = null;
      return;
    }

    if (this.currentTab == 'Tools') {
      this.loadToolReviews();
    } else if (this.currentTab == 'Agents') {
      this.loadAgentsReviews();
    } else {
      this.loadWorkflowReviews();
    }
  }

  public updateConsoleApproval(data: any[], type: string) {
    this.consoleApproval = {
      contents: data?.map((req: any) => {
        const statusIcons: Record<RequestStatus, string> = {
          approved: 'circle-check-big',
          rejected: 'circle-x',
          review: 'clock',
        };
        const statusTexts: Record<RequestStatus, string> = {
          approved: this.labels.approved,
          rejected: this.labels.rejected,
          review: this.labels.review,
        };
        const statusKey = this.toRequestStatus(req?.status);
        let specificId = 0;
        let title = '';

        if (type === 'tool') {
          specificId = req.toolId;
          title = req.toolName;
        } else if (type === 'agent') {
          specificId = req.agentId;
          title = req.agentName;
        } else {
          specificId = req.workflowId;
          title = req.workflowName;
        }

        return {
          id: req.id,
          refId: specificId,
          type: type,
          session1: {
            title: title,
            labels: [
              {
                name: type,
                color: 'success',
                background: 'red',
                type: 'normal',
              },
              {
                name: req.changeRequestType,
                color: req.changeRequestType === 'update' ? 'error' : 'info',
                background: 'red',
                type: 'pill',
              },
            ],
          },
          session2: [
            {
              name: type,
              color: 'default',
              background: 'red',
              type: 'normal',
            },
            {
              name: req.status,
              color: 'default',
              background: 'red',
              type: 'normal',
            },
          ],
          session3: [
            {
              iconName: 'user',
              label: req.requestedBy,
            },
            {
              iconName: 'calendar-days',
              label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN'), 
            },
          ],
          session4: {
            status: statusTexts[statusKey],
            iconName: statusIcons[statusKey],
          },
        };
      }),
      footer: {},
    };
  }
}
