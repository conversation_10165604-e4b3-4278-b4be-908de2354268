<div class="approval">
    <div class="approval-left-screen" [class.quick-actions-expanded]="quickActionsExpanded">
        <!-- <ava-sidebar class="basic-sidebar-theme" width="280px" collapsedWidth="65px" height="500px"
            [class.basic-collapsed]="isBasicCollapsed" (collapseToggle)="onBasicCollapseToggle($event)">  
            <div slot="content">
                <div class="basic-nav-section">
                    <div *ngFor="let item of basicSidebarItems" class="basic-nav-item" [class.basic-active]="item.active"
                        (click)="onBasicItemClick(item)">
                        <ava-icon [iconName]="item.icon" class="basic-nav-icon"></ava-icon>
                        <span class="basic-nav-text">{{ item.text }}</span>
                    </div>
                </div>
            </div>
        </ava-sidebar> -->
        <div class="quick-actions-wrapper" [class.expanded]="quickActionsExpanded">
            <div class="quick-actions-toggle" (click)="toggleQuickActions()">
                <div class="toggle-button">
                    <svg [class.rotate]="quickActionsExpanded" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M5 12h14M12 5l7 7-7 7" stroke="#6566CD" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" />
                    </svg>
                </div>
            </div>

            <!-- Expanded view with text labels -->
            <div class="quick-actions-content" *ngIf="quickActionsExpanded">
                <div class="action-buttons">
                    <button class="action-button" (click)="loadReviews(labels.agents)">
                        <div class="action-icon">
                            <img [src]="'svgs/icons/' + 'awe_agents' + '.svg'" [alt]="labels.agents" width="24" height="24" title="{{labels.agents}}" />
                        </div>
                        <span class="action-label">{{labels.agents}}</span>
                    </button>
                    
                    <button class="action-button" (click)="loadReviews(labels.workflows)">
                        <div class="action-icon">
                            <img [src]="'svgs/icons/' + 'awe_workflows' + '.svg'" [alt]="labels.workflows" width="24" height="24" title="{{labels.workflows}}" />
                        </div>
                        <span class="action-label">{{labels.workflows}}</span>
                    </button>
                    
                    <button class="action-button" (click)="loadReviews(labels.tools)">
                        <div class="action-icon">
                            <img [src]="'svgs/icons/' + 'awe_tools' + '.svg'" [alt]="labels.tools" width="24" height="24" title="{{labels.tools}}" />
                        </div>
                        <span class="action-label">{{labels.tools}}</span>
                    </button>
                </div>
            </div>

            <!-- Collapsed view with icons only -->
            <div class="quick-actions-icons" *ngIf="!quickActionsExpanded">
                <button class="icon-button" (click)="loadReviews(labels.agents)" [title]="labels.agents" title="{{labels.agents}}">
                    <div class="icon-wrapper">
                        <img [src]="'svgs/icons/' + 'awe_agents' + '.svg'" [alt]="labels.agents" width="24" height="24" title="{{labels.agents}}" />
                    </div>
                </button>
                 <button class="icon-button" (click)="loadReviews(labels.workflows)" [title]="labels.workflows" title="{{labels.workflows}}">
                    <div class="icon-wrapper">
                        <img [src]="'svgs/icons/' + 'awe_workflows' + '.svg'" [alt]="labels.workflows" width="24" height="24" title="{{labels.workflows}}" />
                    </div>
                </button>
                <button class="icon-button" (click)="loadReviews(labels.tools)" [title]="labels.tools" title="{{labels.tools}}">
                    <div class="icon-wrapper">
                        <img [src]="'svgs/icons/' + 'awe_tools' + '.svg'" [alt]="labels.tools" width="24" height="24" title="{{labels.tools}}" />
                    </div>
                </button>
            </div>
        </div>
    </div>
    
    <div class="approval-right-screen">
        <div class="approval-title-filter">
            <ava-text-card [type]="'default'" [iconName]="'hourglass'" [title]="labels.totalApprovals" [value]="totalApprovals"
                [description]="currentTab + ' ' + labels.whichAreRequestedForApproval">
            </ava-text-card>
            <ava-text-card [type]="'default'" [iconName]="'shield-alert'" [title]="labels.totalApprovedApprovals" [value]="totalApprovedApprovals"
                [description]="currentTab + ' ' + labels.whichAreApproved">
            </ava-text-card>
            <ava-text-card [type]="'default'" [iconName]="'hourglass'" [title]="labels.totalPendingApprovals" [value]="totalPendingApprovals"
                [description]="labels.all + ' ' + currentTab + ' ' + labels.awaitingApproval">
            </ava-text-card>
        </div>
        
    <div class="filter-section">
        <div class="search-bars">
            <ava-dropdown [dropdownTitle]="labels.allStatus" [options]="options" [search]="true"
                (selectionChange)="onSelectionChange($event)">
            </ava-dropdown>
            <ava-dropdown [dropdownTitle]="labels.allPriority" [options]="options" [search]="true"
                (selectionChange)="onSelectionChange($event)">
            </ava-dropdown>
            <!-- <ava-button label="Bulk Approve" (userClick)="uClick(1)" variant="primary" size="large" state="default"
                iconPosition="left"></ava-button> -->
        </div>
        <div class="textbox section">
            <div>
                <form [formGroup]="searchForm">
                    <ava-textbox [placeholder]="labels.searchPlaceholder" [(ngModel)]="searchValue" formControlName="search">
                        <ava-icon slot="icon-start" iconName="search" [iconSize]="16" iconColor="var(--color-brand-primary)"></ava-icon>
                    </ava-textbox>
                </form>
                <ava-button label="Primary" variant="primary" iconName="calendar-days" iconPosition="only"
                [customStyles]="{
                    background:
                      'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                    '--button-effect-color': '33, 90, 214',
                  }" (userClick)="uClick($event)"></ava-button>
            </div>
        </div>
    </div>
        
        <div class="approval-card-section">
            <div class="approval-card-header">
                All - {{totalRecords}} {{currentTab}}
            </div>
            @for (item of consoleApproval.contents; track $index){
            <div class="approval-card-wrapper">
                <ava-approval-card height="300">
                    <div header>
                        <ava-icon iconSize="20" iconName="ellipsis-vertical"></ava-icon>
                        <div class="header">
                            <h2>{{item.session1.title}}</h2>
                            <ava-tag label="{{currentTab}}" color="info" size="sm"></ava-tag>
                        </div>
                    </div>
                    <div content class="a-content">
                        <div class="box tag-wrapper">
                            <ava-tag label="Individual" size="sm"></ava-tag>
                            <ava-tag label="Ascendion" size="sm"></ava-tag>
                            <ava-tag label="Digital Ascender" size="sm"></ava-tag>
                            <ava-tag label="Platform Engineering" size="sm"></ava-tag>
                        </div>
            
                        <div class="box info-wrapper">
                            <div class="f">
                                <ava-icon iconSize="13" iconName="user"></ava-icon>
                                <span>{{item.session3[0].label}}</span>
                            </div>
                            <div class="ml-auto s">
                                <ava-icon iconSize="20" iconName="calendar-days"></ava-icon>
                                <span>{{item.session3[1].label}}</span>
                            </div>
                        </div>
                    </div>
                    <div footer>
                        <div class="footer-content">
                            <div class="footer-left">
                                <span class="ex">Execution Status</span>
                                <div>
                                    <ava-icon iconSize="20" iconName="circle-check-big"></ava-icon>
                                    <span>{{item?.session4.status}}</span>
                                </div>
            
                            </div>
                            <div class="footer-right">
                                <ava-button [label]="labels.test" (userClick)="uClick($index)" variant="secondary" size="small" [customStyles]="{
                                    'border': '2px solid transparent',
                                    'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                                    'background-origin': 'border-box',
                                    'background-clip': 'padding-box, border-box',
                                    '--button-effect-color': '33, 90, 214'
                                  }"
                                    state="default" iconName="play" iconPosition="left"></ava-button>
                                <ava-button [label]="labels.sendback" (userClick)="rejectApproval($index)" variant="secondary" size="small" [customStyles]="{
                                    'border': '2px solid transparent',
                                    'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                                    'background-origin': 'border-box',
                                    'background-clip': 'padding-box, border-box',
                                    '--button-effect-color': '33, 90, 214'
                                  }"
                                    state="default" iconName="move-left" iconPosition="left"></ava-button>
                                <ava-button [label]="labels.approve" (userClick)="approveApproval($index)" variant="primary" size="small" [customStyles]="{
                                    background:
                                      'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                                    '--button-effect-color': '33, 90, 214',
                                  }"
                                    state="default" iconName="Check" iconPosition="left"></ava-button>
                            </div>
                        </div>
                    </div>
                </ava-approval-card>
            </div>
            }
        </div>
    </div>
</div>

<ava-popup
  [show]="showToolApprovalPopup"
  [title]="labels.confirmApproval"
  [message]="labels.youAreAboutToApproveThis + ' ' + currentTab + '. ' + labels.itWillBeActiveAndAvailableIn + ' ' + currentTab + ' ' + labels.catalogueForUsersToExecute"
  [showClose]="true"
  [showCancel]="true"
  [showConfirm]="true"
  [confirmButtonLabel]="labels.approve"
  [confirmButtonVariant]="'primary'"
  [confirmButtonBackground]="'#dc3545'"
  (confirm)="handleApproval()"
  (cancel)="showToolApprovalPopup=false"
  (closed)="showToolApprovalPopup=false"
>
</ava-popup>

<ava-popup messageAlignment="center" [show]="showInfoPopup"
    title="SUCCESS!" message={{infoMessage}} [showHeaderIcon]="true"
    headerIconName="circle-check" iconColor="green" [showClose]="true" (closed)="handleInfoPopup()">
</ava-popup>

<ava-confirmation-popup [show]="showFeedbackPopup" title="Confirm Send Back"
    message="This {{currentTab}} will be send back for corrections and modification. Kindly comment what needs to be done."
    confirmationLabel="Send Back" (closed)="showFeedbackPopup = false" (confirm)="handleRejection($event)">
</ava-confirmation-popup>

<ava-popup messageAlignment="center" [show]="showErrorPopup"
    title="FAILED!" message={{infoMessage}} [showHeaderIcon]="true"
    headerIconName="circle-x" iconColor="red" [showClose]="true" (closed)="handleInfoPopup()" >
</ava-popup>