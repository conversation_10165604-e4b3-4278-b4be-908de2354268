import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Router } from '@angular/router';
import { SharedApiServiceService } from '../../shared/services/shared-api-service.service';
import {
  ApprovalCardComponent,
  AvaTagComponent,
  ButtonComponent,
  DropdownComponent,
  DropdownOption,
  IconComponent,
  LinkComponent,
  TextCardComponent,
} from '@ava/play-comp-library';
import { QuickActionsComponent } from '../../shared/components/quick-actions/quick-actions.component';

// Interfaces
interface UserLog {
  id: string;
  username: string;
  avatar: string;
  securityToken: string;
  status: 'Active' | 'Inactive' | 'Pending';
}

interface ModelUsage {
  id: string;
  name: string;
  publisher: {
    name: string;
    logo: string;
  };
  agentsCount: number;
}

interface PendingApproval {
  id: string;
  name: string;
  type: string;
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TextCardComponent,
    ButtonComponent,
    ApprovalCardComponent,
    IconComponent,
    DropdownComponent,
    QuickActionsComponent,
    AvaTagComponent,
    LinkComponent,
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss',
})
export class DashboardComponent implements OnInit {
  options: DropdownOption[] = [
    { name: 'Apple', value: 'apple' },
    { name: 'Banana', value: 'banana' },
    { name: 'Orange', value: 'orange' },
    { name: 'Grape', value: 'grape' },
    { name: 'Mango', value: 'mango' },
    { name: 'Pineapple', value: 'pineapple' },
  ];

  quickActions: any[] = [
    { id: 'build-agent', label: 'Build Agent', icon: 'svgs/agents.svg' },
    {
      id: 'build-workflow',
      label: 'Build Workflow',
      icon: 'svgs/Workflows.svg',
    },
    { id: 'create-prompt', label: 'Create Prompt', icon: 'svgs/Prompts.svg' },
    { id: 'create-tool', label: 'Create Tool', icon: 'svgs/Tools.svg' },
    {
      id: 'create-guardrail',
      label: 'Create Guardrail',
      icon: 'svgs/Guardrails.svg',
    },
    {
      id: 'create-knowledge-base',
      label: 'Create Knowledge Base',
      icon: 'svgs/Knowledgebase.svg',
    },
  ];

  approvalCardData = {
    header: {
      iconName: '', // Optional, if you want an icon
      title: 'High Priority Approvals',
      viewAll: true,
    },
    contents: [
      {
        session1: {
          title: 'Autonomous Systems..... Agent',
          labels: [
            {
              name: 'Agent',
              color: 'info',
              background: '#B6F0FA',
              type: 'pill',
            },
            {
              name: 'High',
              color: 'error',
              background: '#E53935',
              type: 'pill',
            },
          ],
        },
        session2: [
          {
            name: 'Individual',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
          {
            name: 'Ascendion',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
          {
            name: 'Platform Engineering',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
          {
            name: 'Digital Ascender',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
          {
            name: 'Revamp Demo',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
        ],
        session3: [
          { iconName: 'user', label: '<EMAIL>' },
          { iconName: 'calendar-days', label: '12 May 2025' },
        ],
        session4: {
          status: 'Pending Approval',
          iconName: 'circle-check',
        },
      },
      // Additional cards
      {
        session1: {
          title: 'Invoice Processing & Approval',
          labels: [
            {
              name: 'Workflow',
              color: 'info',
              background: '#B6F0FA',
              type: 'pill',
            },
            {
              name: 'Critical',
              color: 'error',
              background: '#E53935',
              type: 'pill',
            },
          ],
        },
        session2: [
          {
            name: 'Finance',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
          {
            name: 'Ascendion',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
        ],
        session3: [
          { iconName: 'user', label: '<EMAIL>' },
          { iconName: 'calendar-days', label: '15 May 2025' },
        ],
        session4: {
          status: 'Pending Approval',
          iconName: 'circle-check',
        },
      },
      {
        session1: {
          title: 'Customer Support Chatbot',
          labels: [
            {
              name: 'Agent',
              color: 'info',
              background: '#B6F0FA',
              type: 'pill',
            },
            {
              name: 'Medium',
              color: 'warning',
              background: '#FFD600',
              type: 'pill',
            },
          ],
        },
        session2: [
          {
            name: 'Support',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
          {
            name: 'Ascendion',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
        ],
        session3: [
          { iconName: 'user', label: '<EMAIL>' },
          { iconName: 'calendar-days', label: '18 May 2025' },
        ],
        session4: {
          status: 'Awaiting Review',
          iconName: 'circle-check',
        },
      },
      {
        session1: {
          title: 'AI-Powered Code Review Assistant',
          labels: [
            {
              name: 'Agent',
              color: 'info',
              background: '#B6F0FA',
              type: 'pill',
            },
            {
              name: 'Low',
              color: 'success',
              background: '#4CAF50',
              type: 'pill',
            },
          ],
        },
        session2: [
          {
            name: 'Engineering',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
          {
            name: 'Ascendion',
            color: 'default',
            background: '#ECEEF1',
            type: 'normal',
          },
        ],
        session3: [
          { iconName: 'user', label: '<EMAIL>' },
          { iconName: 'calendar-days', label: '20 May 2025' },
        ],
        session4: {
          status: 'Completed',
          iconName: 'circle-check',
        },
      },
    ],
    footer: {},
  };
  dashboardDetails = [
    {
      icon: 'trending-up',
      title: 'Active Workflows',
      value: 80,
      subtitle: 'Agents actively running',
      badge: 'S',
    },
    {
      icon: 'trending-up',
      title: 'Active Agents',
      value: 1240,
      subtitle: 'Agents active',
      badge: 'S',
    },
    {
      icon: 'trending-up',
      title: 'Agents Approval',
      value: 120,
      subtitle: 'Agents actively running',
      badge: 'S',
    },
    {
      icon: 'trending-up',
      title: 'Active Workflows',
      value: 80,
      subtitle: 'Agents actively running',
      badge: 'S',
    },
  ];
  // Dashboard metrics
  totalAgents: number = 136;
  newAgentsCreated: number = 50;

  totalWorkflows: number = 124;
  newWorkflowsCreated: number = 50;

  totalUsers: number = 300;
  newUsersAdded: number = 50;

  // Current user info (would come from auth service in real app)
  currentUser: { name: string } = { name: 'Akash Raj' };

  // Footer info
  currentYear: number = new Date().getFullYear();

  // User logs data
  userLogs: UserLog[] = [
    {
      id: '1',
      username: 'Michael Scott',
      avatar: 'assets/images/avatars/michael-scott.jpg',
      securityToken: 'X9D7K2B4MQ',
      status: 'Active',
    },
    {
      id: '2',
      username: 'Jim Halpert',
      avatar: 'assets/images/avatars/jim-halpert.jpg',
      securityToken: 'QWE2349SDF',
      status: 'Active',
    },
    {
      id: '3',
      username: 'Dwight Schrute',
      avatar: 'assets/images/avatars/dwight-schrute.jpg',
      securityToken: 'OWDF1230JS',
      status: 'Active',
    },
    {
      id: '4',
      username: 'Kevin Malone',
      avatar: 'assets/images/avatars/kevin-malone.jpg',
      securityToken: 'SDVP9I23EJ',
      status: 'Active',
    },
  ];

  // Model usage data
  modelUsage: ModelUsage[] = [
    {
      id: '1',
      name: 'GPT 3',
      publisher: {
        name: 'Open AI',
        logo: 'assets/images/logos/openai-logo.png',
      },
      agentsCount: 48,
    },
    {
      id: '2',
      name: 'Claude 2',
      publisher: {
        name: 'Anthropic',
        logo: 'assets/images/logos/anthropic-logo.png',
      },
      agentsCount: 24,
    },
    {
      id: '3',
      name: 'Gemini',
      publisher: {
        name: 'Google',
        logo: 'assets/images/logos/google-logo.png',
      },
      agentsCount: 20,
    },
    {
      id: '4',
      name: 'GPT-4',
      publisher: {
        name: 'Open AI',
        logo: 'assets/images/logos/openai-logo.png',
      },
      agentsCount: 8,
    },
  ];

  // Pending approvals
  pendingApprovals: PendingApproval[] = [
    {
      id: '1',
      name: 'Test Ruby to Springboot',
      type: 'migration',
    },
    {
      id: '2',
      name: 'Customer Support Chatbot',
      type: 'agent',
    },
    {
      id: '3',
      name: 'Invoice Processing & Approval',
      type: 'workflow',
    },
    {
      id: '4',
      name: 'Invoice Processing & Approval',
      type: 'workflow',
    },
    {
      id: '5',
      name: 'AI-Powered Code Review Assistant',
      type: 'agent',
    },
    {
      id: '6',
      name: 'AI-Powered Code Review Assistant',
      type: 'agent',
    },
    {
      id: '7',
      name: 'AI-Powered Code Review Assistant',
      type: 'agent',
    },
    {
      id: '8',
      name: 'AI-Powered Code Review Assistant',
      type: 'agent',
    },
  ];

  activityMonitoring = [
    {
      agentName: 'Agent Name will be here',
      status: 'Active',
      user: 'Michael Scott',
      date: '12 June 2025',
      totalRuns: 264,
    },
    {
      agentName: 'Agent Name will be here',
      status: 'Active',
      user: 'Michael Scott',
      date: '12 June 2025',
      totalRuns: 264,
    },
    {
      agentName: 'Agent Name will be here',
      status: 'Active',
      user: 'Michael Scott',
      date: '12 June 2025',
      totalRuns: 264,
    },
    {
      agentName: 'Agent Name will be here',
      status: 'Active',
      user: 'Michael Scott',
      date: '12 June 2025',
      totalRuns: 264,
    },
    {
      agentName: 'Agent Name will be here',
      status: 'Active',
      user: 'Michael Scott',
      date: '12 June 2025',
      totalRuns: 264,
    },
  ];

  constructor(
    private router: Router,
    private apiService: SharedApiServiceService,
  ) {}

  ngOnInit(): void {
    // The layout is now managed with fixed heights in CSS
    // No need for recalculateLayout
  }

  // Navigate to a route
  navigateTo(route: string): void {
    this.router.navigate([route]);
  }

  // Approve a pending item
  approveItem(id: string): void {
    // In a real app, you would call a service to approve the item
    // Then remove it from the pendingApprovals array or refresh the list
  }

  // Test method to demonstrate loader functionality
  testLoader(): void {
    this.apiService.getConfigLabels().subscribe({
      next: (response) => {},
      error: (error) => {},
    });
  }

  uClick(index: any) {}

  onSelectionChange(data: any) {
    console.log('Selection changed:', data);
  }

  onQuickActionClick(action: any) {
    console.log('Quick action clicked:', action);
    // Handle navigation or action based on action.id
    switch (action.id) {
      case 'build-agent':
        this.router.navigate(['/build/agents/create']);
        break;
      case 'build-workflow':
        this.router.navigate(['/build/workflows/create']);
        break;
      case 'create-prompt':
        this.router.navigate(['/libraries/prompts/create']);
        break;
      case 'create-tool':
        this.router.navigate(['/libraries/tools/create']);
        break;
      case 'create-guardrail':
        this.router.navigate(['/libraries/guardrails/create']);
        break;
      case 'create-knowledge-base':
        this.router.navigate(['/libraries/knowledge-base/create']);
        break;
      default:
        console.log('Unknown action:', action.id);
    }
  }
}
