import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class PromptEnhanceService {

  private apiServiceUrl = environment.consoleInstructionApi;
  private defaultStr = '/ava/force/individualAgent/execute';
  private defaultUserSignature = '<EMAIL>'; // Default user signature, can be customized

  constructor(private http: HttpClient) { }

  public modelApi(prompt: any, mode: string = '', promptOverride: boolean = false, useCaseIdentifier: string = '',yamlContent?:string,colangContent?:string) {
    let payload: any = {
      prompt: prompt,
      mode: mode, //Prompt Optimization
      promptOverride: promptOverride,
      useCaseIdentifier: useCaseIdentifier,
      userSignature: this.defaultUserSignature,
    };
    //colang and yaml are optional parameters for create-guardrails screen validateCode API
    if(colangContent ){
      payload.colangContent = colangContent;
    }
    if(yamlContent ){
      payload.yamlContent = yamlContent;
    }
    console.log(payload,"payload");
    const headers = { headers: new HttpHeaders({
      'Content-Type': 'application/json',
    })};
    const url = `${this.apiServiceUrl}${this.defaultStr}`;

    return this.http.post(url, payload, headers).pipe(
      map((response: any) => {
          return response;
      })
    );
  }

  public modelGuardrailApi(prompt: any, mode: string = '', promptOverride: boolean = false, yamlContent?:string, colangContent?:string){
    let payload: any = {
      prompt: prompt,
      mode: mode, //Prompt Optimization
      promptOverride: promptOverride,
      colangContent: colangContent,
      yamlContent: yamlContent,
      userSignature: this.defaultUserSignature,
    };
    //colang and yaml are optional parameters for create-guardrails screen validateCode API

    console.log(payload,"payload");
    const headers = { headers: new HttpHeaders({
      'Content-Type': 'application/json',
    })};
    const url = `${this.apiServiceUrl}${this.defaultStr}`;

    return this.http.post(url, payload, headers).pipe(
      map((response: any) => {
          return response;
      })
    );
  }
}