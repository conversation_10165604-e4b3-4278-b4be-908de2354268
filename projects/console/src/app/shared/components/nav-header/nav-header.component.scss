// CSS Variables for organization trigger styling
:host {
  --nav-bg: #ffffff;
  --nav-text: #000000;
  --nav-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: block;
  padding: 1.5rem 0 0 0;
  margin: 0;
  width: 100%;
  position: relative;

  // Responsive adjustments for consistent spacing
  @media (min-width: 1200px) {
    padding-top: 1.2rem;
  }

  @media (min-width: 1400px) {
    padding-top: 1rem;
  }
}

::ng-deep .outer-box.light {
  background-color: transparent !important;
  box-shadow: none !important;
  padding: 0.5rem 0 !important;
  margin: 0 !important;
  min-height: auto !important;
  width: 100% !important;
}

.container {
  @media screen and (min-width: 1200px) {
    background-color: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
    max-width: 100% !important;
    width: 100% !important;
  }
}

// Remove any Bootstrap or framework container spacing
::ng-deep .container-fluid {
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  max-width: 100% !important;
}

// Ensure full width for the header content and reduce gaps
::ng-deep awe-header {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;

  // Minimize gaps between left, center, right sections
  .header-content {
    gap: 0.125rem !important;
    padding: 0 0.25rem !important;
  }

  [left-content] {
    flex: 0 0 auto !important;
    margin-right: 0.125rem !important;
  }

  [center-content] {
    flex: 1 1 auto !important;
    display: flex !important;
    justify-content: center !important;
    margin: 0 0.125rem !important;
  }

  [right-content] {
    flex: 0 0 auto !important;
    margin-left: 0.125rem !important;
  }
}

// Logo styling - reduce size to save space
[left-content] img {
  max-height: 32px !important;
  width: auto !important;
  padding: 0 0.5rem !important;

  // Responsive logo sizing
  @media (min-width: 1200px) {
    max-height: 50px !important;
    padding: 0 !important;
    margin-top: 5.6px;
  }

  @media (min-width: 1400px) {
    max-height: 40px !important;
  }

  @media (max-width: 768px) {
    max-height: 28px !important;
    padding: 0 0.25rem !important;
  }
}

// Center nav items styling - white background container
.nav-items {
  display: flex;
  align-items: center;
  gap: 1px; // Small gap between nav items
  background-color: var(--nav-pills-bg, rgba(255, 255, 255, 0.9));
  border-radius: 999px;
  padding: 7px; // Small padding around nav items
  box-shadow: var(--nav-pills-shadow, 0 2px 8px rgba(0, 0, 0, 0.08));
  backdrop-filter: blur(10px);
  min-height: 36px; // Compact height
}

// Right side content adjustments - keep original styling without extra background
.user-info-container {
  display: flex;
  align-items: center;
  gap: 0.25rem;

  .org-path-dropdown-container {
    .org-path-trigger {
      padding: 6px 10px !important; // Reduced from 8px 12px
      font-size: 14px !important;
      border-radius: 999px !important;
      height: 32px !important; // Set specific compact height
      display: flex !important;
      align-items: center !important;
      min-height: auto !important;

      .org-icon img {
        width: 18px !important; // Reduced from 20px
        height: 18px !important;
      }

      .org-label-text {
        font-size: 13px !important; // Reduced from 14px
        margin: 0 3px !important; // Reduced margin
      }

      .org-dropdown-arrow svg {
        width: 14px !important; // Reduced from 16px
        height: 14px !important;
      }
    }
  }

  .profile-container {
    img {
      width: 28px !important; // Smaller to match overall scale
      height: 28px !important;
      border-radius: 50% !important;
    }
  }
}

// Right side content adjustments - keep original styling without extra background
.user-info-container {
  display: flex;
  align-items: center;
  gap: 0.25rem;

  .org-path-dropdown-container {
    .org-path-trigger {
      padding: 6px 10px !important; // Smaller padding to match nav tabs size
      font-size: 14px !important; // Match nav-item font size
      border-radius: 999px !important; // Match nav-item border radius
      height: 32px !important; // Set specific height to match nav tabs
      display: flex !important;
      align-items: center !important;
      min-height: auto !important;

      .org-icon img {
        width: 18px !important; // Slightly smaller icon
        height: 18px !important;
      }

      .org-label-text {
        font-size: 13px !important; // Slightly smaller font
        margin: 0 3px !important;
      }

      .org-dropdown-arrow svg {
        width: 14px !important; // Smaller arrow
        height: 14px !important;
      }
      @media screen and (min-width: 1200px) {
        padding: 8px 5px;
      }
    }
  }

  .profile-container {
    img {
      width: 28px !important; // Smaller to match overall scale
      height: 28px !important;
      border-radius: 50% !important;
    }
  }

  // Responsive adjustments for right side - keep consistent with nav tabs
  @media (min-width: 1200px) {
    gap: 0.5rem;

    .org-path-dropdown-container .org-path-trigger {
      padding: 8px 12px !important; // Keep same as nav-item
      font-size: 14px !important; // Keep same as nav-item

      .org-icon img {
        width: 20px !important; // Keep same as nav-item icons
        height: 20px !important;
      }

      .org-label-text {
        font-size: 14px !important; // Keep same as nav-item
      }
    }

    .profile-container img {
      width: 30px !important; // Slightly larger but still compact
      height: 30px !important;
    }
  }

  @media (max-width: 768px) {
    gap: 0.25rem;

    .org-path-dropdown-container .org-path-trigger {
      padding: 4px 8px !important;
      font-size: 12px !important;

      .org-icon img {
        width: 24px !important;
        height: 24px !important;
      }

      .org-label-text {
        font-size: 12px !important;
        margin: 0 4px !important;
      }

      .org-dropdown-arrow svg {
        width: 10px !important;
        height: 10px !important;
      }
    }

    .profile-container img {
      width: 28px !important;
      height: 28px !important;
    }
  }
}

// Navigation menu styles
.nav-menu {
  display: flex;
  align-items: center;
  height: 100%;
  position: relative;
  z-index: 5; // Reduced from 100 to prevent hiding content below
}

.nav-items {
  display: flex;
  align-items: center;
  gap: 10px;
  height: auto;
  background-color: var(--nav-pills-bg, rgba(255, 255, 255, 0.9));
  border-radius: 999px;
  padding: 7px;
  box-shadow: var(--nav-pills-shadow);
  backdrop-filter: blur(10px);
  min-height: 36px;
}

.nav-item-wrapper {
  position: relative;
  z-index: 5; // Reduced from 100 to prevent hiding content below
}

.nav-item-container {
  position: relative;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  color: var(--nav-text, #666d99);
  cursor: pointer;
  transition: all 0.3s ease;

  // &:hover {
  //   background-color: var(--nav-hover, rgba(255, 255, 255, 0.1));
  // }

  &.selected {
    background-color: var(--nav-active, #fff);
    color: var(--nav-active-text, #000);
    box-shadow: 0 2px 4px var(--card-shadow, rgba(0, 0, 0, 0.1));
  }

  .item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    color: currentColor;
  }

  .dropdown-arrow {
    margin-left: 4px;
    transition: transform 0.2s ease;

    &.open {
      transform: rotate(180deg);
    }
  }
}

// Dropdown menu styles
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 280px;
  background-color: var(--dropdown-bg);
  border-radius: 8px;
  box-shadow: 0 4px 16px var(--dropdown-shadow);
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  z-index: 15; // Reduced from 1000 to prevent hiding content below
  margin-top: 8px;
  padding: 8px;

  &.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: var(--dropdown-hover-bg);
  }

  .dropdown-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--nav-item-color);
    flex-shrink: 0;
  }

  .dropdown-item-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .dropdown-item-title {
    font-weight: 500;
    font-size: 16px;
    color: var(--dropdown-text);
  }

  .dropdown-item-desc {
    font-size: 14px;
    color: var(--text-secondary);
  }
}

// Theme toggle icon and user profile styling
.header-right-content {
  img {
    width: 24px;
    height: 24px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }
}

// Profile dropdown container
.profile-container {
  position: relative;
  display: flex;
  align-items: center;

  // Profile icon wrapper with nav-item-like styling
  .cursor-pointer {
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
      background-color: var(--nav-item-active-bg, #e6e4fb);

      &:after {
        opacity: 0.1;
      }
    }

    // When profile dropdown is open, apply selected state styling
    &.active {
      background-color: var(--nav-item-active-bg, #e6e4fb);
      box-shadow: 0 2px 4px var(--card-shadow, rgba(0, 0, 0, 0.1));

      &:after {
        opacity: 0;
      }
    }

    // Subtle gradient overlay similar to nav items
    &:after {
      content: "";
      position: absolute;
      inset: 0;
      background: var(--gradient-primary);
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: -1;
    }
  }
}

// Profile dropdown styles (matching navbar dropdown)
.profile-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  min-width: 200px;
  background-color: var(--dropdown-bg);
  border-radius: 8px;
  box-shadow: 0 4px 16px var(--dropdown-shadow);
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  z-index: 15; // Reduced from 1000 to prevent hiding content below
  margin-top: 8px;
  padding: 8px;

  &.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

.profile-dropdown-content {
  display: flex;
  flex-direction: column;
}

.profile-info {
  padding: 12px;
  border-radius: 6px;
}

.profile-name {
  font-weight: 500;
  font-size: 16px;
  color: var(--dropdown-text);
  margin-bottom: 4px;
}

.profile-email {
  font-size: 14px;
  color: var(--text-secondary);
}

.profile-divider {
  height: 1px;
  background-color: var(--border-color, #e5e7eb);
  margin: 8px 0;
}

.profile-actions {
  display: flex;
  flex-direction: column;
}

.profile-action-item {
  color: var(--nav-text, #666d99);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    background-color: var(--nav-hover);
    color: var(--nav-item-active-color);

    &:after {
      opacity: 0.1;
    }
  }

  // Add the gradient overlay effect
  &:after {
    content: "";
    position: absolute;
    inset: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
  }

  .action-label {
    font-weight: 500;
    font-size: 1rem;
    color: var(--dropdown-text);
  }
}

@media (max-width: 400px) {
  ::ng-deep .outer-box .center-content-wrapper {
    display: none !important;
  }
}

/* Responsive navigation handling */
@media (max-width: 768px) {
  .nav-items {
    gap: 8px;
  }

  .nav-item {
    padding: 6px 12px;
    font-size: 0.875rem;
  }

  .org-path-popover {
    max-width: 95vw;
    padding: 16px 20px 12px 20px;
  }
}

@media (max-width: 480px) {
  .nav-items {
    gap: 4px;
  }

  .nav-item {
    padding: 4px 8px;
    font-size: 0.8rem;
  }

  .org-path-popover {
    max-width: 98vw;
    padding: 12px 16px 8px 16px;
    margin-top: 4px;
  }

  .filter-config-title {
    font-size: 18px;
  }

  .dropdown-row-vertical {
    gap: 8px;
  }
}

.org-path-trigger {
  display: flex;
  align-items: center;
  background: var(--nav-bg, #ffffff) !important;
  border-radius: 24px;
  padding: 0 12px 0 8px;
  height: 48px;
  box-shadow: var(--nav-shadow, 0 2px 8px rgba(0, 0, 0, 0.1)) !important;
  cursor: pointer;
  transition: box-shadow 0.2s;

  .org-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: transparent;

    flex-shrink: 0;

    svg {
      display: block;
    }
  }

  .org-label-text {
    font-weight: 600;
    color: var(--nav-text, #000000) !important;
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .org-dropdown-arrow {
    display: flex;
    align-items: center;
    margin-left: auto;
    color: var(--nav-arrow, #222);
    transition: transform 0.2s;

    svg {
      display: block;
      transition: transform 0.4s ease-in-out;
    }

    &.open svg {
      transform: rotate(180deg);
    }
  }
}

.org-path-dropdown-container {
  position: relative;
  display: inline-block;
}

.org-path-popover {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 8px;
  z-index: 20; // Reduced from 3001 to prevent hiding content below
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
  border: 1px solid #e0e0e0;
  padding: 20px 24px 16px 24px;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 80vh;
  max-width: 90vw;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  transition: box-shadow 0.2s;
}

.org-path-popover.right {
  left: auto;
  right: 0;
}

.org-path-popover.left {
  left: 0;
  right: auto;
}

.org-path-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 19; // Reduced from 3000 to prevent hiding content below
}

.dropdown-row-horizontal {
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-items: center;
  margin-bottom: 16px;
}

.dropdown-row-vertical {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: stretch;
}

.popover-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  width: 100%;
  margin-top: 8px;
}

.org-icon img {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: block;
  object-fit: cover;
}

// Filter Configuration Dialog Styles
.filter-config-title {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.filter-label {
  display: block;
  font-weight: 500;
}

.dropdown-row-vertical .filter-label:first-child {
  margin-top: 0;
}

.required-asterisk {
  color: red;
}

.filter-label.required::after {
  content: " *";
  color: red;
  font-weight: bold;
}

.user-info-container {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
}

// .outer-box .row{
::ng-deep {
  .outer-box {
    .row {
      @media screen and (min-width: 1200px) {
        flex-wrap: nowrap !important;
       // margin: 0 0 15px -39px !important;
      }
    }

    .col-auto {
      padding: 0 10px;
      @media screen and (min-width: 1400px) {
        padding: 0 15px;
      }
    }

    .center-content-wrapper {
      padding: 0 5px;
      @media screen and (min-width: 1400px) {
        padding: 8px 12px;
      }
    }
  }
}
