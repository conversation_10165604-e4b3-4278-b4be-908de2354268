<awe-header theme="light">
  <div left-content>
    <img [src]="logoSrc" class="px-2" alt="Logo" />
  </div>

  <div center-content class="nav-menu">
    <div class="nav-items">
      <app-nav-item
        *ngFor="let item of navItems; let i = index"
        [label]="item.label"
        [route]="item.route"
        [selected]="item.selected"
        [hasDropdown]="item.hasDropdown"
        [dropdownOpen]="item.dropdownOpen || false"
        [dropdownItems]="item.dropdownItems || []"
        [icon]="item.icon"
        (toggleDropdownEvent)="toggleDropdown(i)"
        (navigateEvent)="navigateTo($event)"
        (selectEvent)="selectMenuItem(i)"
        (dropdownItemSelected)="onDropdownItemSelected($event, i)"
        class="nav-item-wrapper"
      >
      </app-nav-item>
    </div>
  </div>
  <div right-content class="user-info-container">
    <div class="org-path-dropdown-container">
      <div class="org-path-trigger" (click)="toggleOrgDialog()" #orgPathTrigger>
        <span class="org-icon">
          <img
            src="assets/svgs/ascendion-logo/header-ascendion-logo.svg"
            alt="Ascendion Logo"
            width="40"
            height="40"
          />
        </span>
        <span class="org-label-text">
          <span>{{ orgLabel }}</span>
        </span>
        <span class="org-dropdown-arrow" [class.open]="isOrgDialogOpen">
          <svg
            width="16"
            height="16"
            viewBox="0 0 12 12"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M2.5 4L6 7.5L9.5 4"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            `
          </svg>
        </span>
      </div>
      <div
        *ngIf="isOrgDialogOpen"
        class="org-path-popover"
        #popover
        [ngClass]="popoverAlign"
      >
        <form>
          <div class="filter-config-title">Filter Configuration</div>
          <div class="dropdown-row-vertical">
            <label class="filter-label required">Choose Organization</label>
            <ava-dropdown
              [dropdownTitle]="'Select Organization'"
              [options]="orgOptions"
              [selectedValue]="selectedOrgName"
              [disabled]="false"
              (selectionChange)="onOrgSelect($event)"
              [search]="true"
              [enableSearch]="true"
            ></ava-dropdown>
            <label class="filter-label required">Choose Domain</label>
            <ava-dropdown
              [dropdownTitle]="'Select Domain'"
              [options]="domainOptions"
              [selectedValue]="selectedDomainName"
              [disabled]="!selectedOrg"
              (selectionChange)="onDomainSelect($event)"
              [search]="true"
              [enableSearch]="true"
            ></ava-dropdown>
            <label class="filter-label required">Choose Project</label>
            <ava-dropdown
              [dropdownTitle]="'Select Project'"
              [options]="projectOptions"
              [selectedValue]="selectedProjectName"
              [disabled]="!selectedDomain"
              (selectionChange)="onProjectSelect($event)"
              [search]="true"
              [enableSearch]="true"
            ></ava-dropdown>
            <label class="filter-label required">Choose Team</label>
            <ava-dropdown
              [dropdownTitle]="'Select Team'"
              [options]="teamOptions"
              [selectedValue]="selectedTeamName"
              [disabled]="!selectedProject"
              (selectionChange)="onTeamSelect($event)"
              [search]="true"
              [enableSearch]="true"
            ></ava-dropdown>
          </div>
          <div class="popover-actions">
            <ava-button
              label="Cancel"
              variant="secondary"
              size="medium"
              (userClick)="closeOrgDialog()"
            >
            </ava-button>
            <ava-button
              label="Apply"
              variant="primary"
              size="medium"
              [disabled]="!headerConfigForm.valid"
              (userClick)="saveOrgPathAndClose()"
            >
            </ava-button>
          </div>
        </form>
      </div>
      <div
        *ngIf="isOrgDialogOpen"
        class="org-path-backdrop"
        (click)="closeOrgDialog()"
      ></div>
    </div>
    <div class="profile-container header-right-content">
      <div
        class="cursor-pointer d-flex justify-content-center align-items-center"
        [class.active]="profileDropdownOpen"
        (click)="toggleProfileDropdown()"
      >
        <img [src]="userAvatar" alt="User Profile" />
      </div>

      <!-- Profile Dropdown -->
      <div class="profile-dropdown" [class.visible]="profileDropdownOpen">
        <div class="profile-dropdown-content">
          <div class="profile-info">
            <div class="profile-name">{{ userName }}</div>
            <div class="profile-email" *ngIf="userEmail">{{ userEmail }}</div>
          </div>
          <div class="profile-divider"></div>
          <div class="profile-actions">
            <div class="profile-action-item" (click)="logout()">
              <span class="action-label">Logout</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</awe-header>
