.nav-item-container {
  position: relative;
  z-index: 1000; /* Ensure this is higher than any base elements */
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 999px;
  font-size: 14px;
  font-weight: 400;
  color: var(--nav-item-color);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  background-color: transparent;
  white-space: nowrap;

  &:hover:not(.selected) {
    background: var(--nav-pill-hover-bg);
    color: var(--nav-pill-hover-color, #ffffff);
    box-shadow: var(--nav-pill-hover-shadow, 0 2px 4px rgba(0, 0, 0, 0.08));

    .item-icon {
      color: var(--nav-pill-hover-color, #ffffff);
    }

    .nav-icon {
      opacity: 1;
      filter: var(--nav-pill-hover-icon-filter, brightness(0) invert(1));
    }
  }

  &.selected {
    background: var(--nav-pill-selected-bg);
    color: var(--nav-pill-selected-color, #ffffff);
    font-weight: bold;
    box-shadow: var(--nav-pill-selected-shadow, 0 2px 4px rgba(0, 0, 0, 0.08));

    .item-icon {
      color: var(--nav-pill-selected-color, #ffffff);
    }

    .nav-icon {
      opacity: 1;
      filter: var(
        --nav-pill-selected-icon-filter,
        brightness(0) invert(1)
      ); /* Makes the icon white when selected */
    }
  }
  @media screen and (min-width: 1400px) {
    padding: 8px 12px;
  }
  @media screen and (min-width: 1200px) {
    padding: 8px 5px;
  }
}

.item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  transition: color 0.3s ease;
  color: inherit;

  &.selected {
    color: var(--nav-pill-selected-color, #ffffff);
  }
}

.item-label {
  flex: 1;
  display: flex;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: inherit;
}

.nav-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
  transition:
    filter 0.3s ease,
    opacity 0.3s ease;
  opacity: 0.8;

  &.selected {
    opacity: 1;
    filter: var(
      --nav-pill-selected-icon-filter,
      brightness(0) invert(1)
    ); /* Makes the icon white when selected */
  }
}

.dropdown-arrow {
  margin-left: 2px;
  transition: transform 0.3s ease;
  height: 16px;
  display: flex;
  align-items: center;

  &.open {
    transform: rotate(180deg);
  }
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .nav-item {
    padding: 6px 8px;
    font-size: 12px;
    gap: 4px;
  }

  .item-icon {
    width: 16px;
    height: 16px;
  }

  .nav-icon {
    width: 14px;
    height: 14px;
  }

  .dropdown-arrow {
    height: 14px;
  }
}
