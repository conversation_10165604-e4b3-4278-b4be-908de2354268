.dropdown-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  z-index: 10;
  background: var(--nav-pills-bg, rgba(255,255,255,0.9));
  border: none;
  width: 100%;
  text-align: left;
  outline: none;
  margin: 2px 0;
  -webkit-appearance: none;
  appearance: none;
  color: var(--nav-text, #333333);

  &:hover {
    background: var(--nav-pill-hover-bg, linear-gradient(118deg, #215ad6 55.27%, #03BDD4));
    color: var(--nav-pill-hover-color, #ffffff);


    .dropdown-item-title,
    .dropdown-item-desc {
      color: var(--nav-pill-hover-color, #ffffff);
    }
    .dropdown-icon {
      filter: var(--nav-pill-hover-icon-filter, brightness(0) invert(1));
    }
  }

  &.active {
    background: var(--nav-pill-selected-bg, linear-gradient(118deg, #215ad6 55.27%, #03BDD4));
    color: var(--nav-pill-selected-color, #ffffff);


    .dropdown-item-title,
    .dropdown-item-desc {
      color: var(--nav-pill-selected-color, #ffffff);
    }
    .dropdown-icon {
      filter: var(--nav-pill-selected-icon-filter, brightness(0) invert(1));
    }
  }

  &:first-child {
    margin-top: 4px;
  }

  &:last-child {
    margin-bottom: 4px;
  }
}

.dropdown-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--nav-text, #333333);
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  pointer-events: none;

  &.active {
    color: var(--nav-pill-selected-color, #ffffff);
  }
}

.dropdown-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
  pointer-events: none;
  transition: filter 0.2s ease;

  &.active {
    filter: var(--nav-pill-selected-icon-filter, brightness(0) invert(1));
  }
}

.dropdown-item-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
  pointer-events: none;
}

.dropdown-item-title {
  font-weight: bold;
  font-size: 16px;
  color: var(--nav-text, #333333);
  transition: color 0.2s ease;
  pointer-events: none;
  line-height: 1.3;

  &.active {
    color: var(--nav-pill-selected-color, #ffffff);
    font-weight: bold;
  }
}

.dropdown-item-desc {
  font-size: 14px;
  color: var(--text-secondary, #666666);
  transition: color 0.2s ease;
  pointer-events: none;
  line-height: 1.4;

  &.active {
    color: var(--nav-pill-selected-color, #ffffff);
  }
}
