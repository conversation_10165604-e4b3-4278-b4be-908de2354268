.preview-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 480px;
  height: 100vh;
  background: #fff;
  box-shadow: -2px 0 12px rgba(0,0,0,0.08);
  display: flex;
  z-index: 200;
  flex-direction: column;
  padding: 0;
  border-left: 1px solid #eee;
}

.preview-header {
  flex-shrink: 0;
  width: 100%;
  min-height: 56px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 0 24px;
}

.divider {
  width: 100%;
  height: 2px;
  background: #b4b4b4;
  margin: 20px 0;
}

.preview-content {
  flex: 1;
  width: 100%;
  overflow-y: auto;
  padding: 0 32px;
  display: flex;
  flex-direction: column;
}

.preview-footer {
  flex-shrink: 0;
  width: 100%;
  padding: 0 24px 20px 24px;
  box-sizing: border-box;
}

@media (max-width: 600px) {
  .preview-panel {
    width: 100vw;
    left: 0;
    right: 0;
    padding: 16px;
  }
} 