.pagination-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 0;
  width: 100%;
}

.page-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: transparent;
  border: none;
  cursor: pointer;
  color: var(--nav-item-color);
  transition: all 0.2s ease;
  
  &:hover:not(.disabled) {
    background-color: var(--nav-item-active-bg);
    color: var(--nav-item-active-color);
  }
  
  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
  
  svg {
    width: 16px;
    height: 16px;
  }
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--dashboard-bg-lighter);
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 300;
  color: var(--text-color);
  transition: all 0.2s ease;
  
  &:hover:not(.active) {
    background-color: var(--nav-item-active-bg);
    color: var(--nav-item-active-color);
  }
  
  &.active {
    background-color: #11387C;
    color: #ffff;
    border-color: var(--dropdown-selected-bg);
  }
}

.ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  font-size: 14px;
  font-weight: 500;
  color: var(--nav-item-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pagination-container {
    padding: 12px 0;
  }
  
  .page-nav, .page-number {
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 480px) {
  .page-nav, .page-number {
    width: 32px;
    height: 32px;
  }
  
  .page-numbers {
    gap: 4px;
  }
  
  .ellipsis {
    width: 20px;
  }
} 