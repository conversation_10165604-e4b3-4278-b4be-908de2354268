<div class="canvas-board-container" [class.execute-mode]="isExecuteMode">
  <!-- Built-in Header Inputs -->
  <div class="header-inputs-section" *ngIf="showHeaderInputs">
    <div class="header-inputs-container">
      <!-- Agent Details Dropdown -->
      <div
        class="input-group agent-details-dropdown"
        *ngIf="inputFieldsConfig.agentDetails?.enabled"
      >
        <div
          class="dropdown-container"
          [class.open]="isAgentDetailsDropdownOpen"
        >
          <!-- Dropdown Toggle -->
          <button
            type="button"
            class="dropdown-toggle"
            [class.open]="isAgentDetailsDropdownOpen"
            (click)="toggleAgentDetailsDropdown()"
            [attr.aria-expanded]="isAgentDetailsDropdownOpen"
          >
            <span>{{
              inputFieldsConfig.agentDetails?.label || "Agent Details"
            }}</span>
            <ava-icon
              iconName="ChevronDown"
              [iconSize]="16"
              [style.transform]="
                isAgentDetailsDropdownOpen ? 'rotate(180deg)' : 'rotate(0deg)'
              "
            >
            </ava-icon>
          </button>

          <!-- Dropdown Content -->
          <div class="dropdown-content" *ngIf="isAgentDetailsDropdownOpen">
            <div class="form-fields">
              <!-- Agent Name Field -->
              <div class="field-group">
                <label class="field-label">Name</label>
                <ava-textbox
                  [placeholder]="
                    inputFieldsConfig.agentDetails?.namePlaceholder ||
                    'Enter name'
                  "
                  [formControl]="agentDetailNameControl"
                  variant="primary"
                  size="md"
                  [fullWidth]="true"
                  class="agent-detail-name-field"
                >
                </ava-textbox>
              </div>

              <!-- Agent Detail Field -->
              <div class="field-group">
                <label class="field-label">{{
                  inputFieldsConfig.agentDetails?.detailLabel || "AgentDetails"
                }}</label>
                <ava-textarea
                  [placeholder]="
                    inputFieldsConfig.agentDetails?.detailPlaceholder ||
                    'Enter agent details and description'
                  "
                  [formControl]="agentDetailControl"
                  [rows]="4"
                  [fullWidth]="true"
                  class="agent-detail-field"
                >
                </ava-textarea>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="dropdown-actions">
              <ava-button
                label="Cancel"
                variant="secondary"
                size="small"
                (userClick)="cancelAgentDetails()"
              >
              </ava-button>
              <ava-button
                label="Apply"
                variant="primary"
                [customStyles]="{
                  background:
                    'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                  '--button-effect-color': '33, 90, 214',
                }"
                size="small"
                (userClick)="applyAgentDetails()"
              >
              </ava-button>
            </div>
          </div>
        </div>
        <div class="canvas-action" *ngIf="showLeftActions">
          <ng-content select="[actionLeft]"></ng-content>
        </div>
      </div>
    </div>
  </div>

  <!-- Navigation hints -->
  <div class="navigation-hint" *ngIf="navigationHints.length > 0">
    <span *ngFor="let hint of navigationHints; let last = last">
      {{ hint }}<span *ngIf="!last"> | </span>
    </span>
  </div>

  <!-- Canvas container -->
  <div
    class="canvas-container"
    #canvasContainer
    [class.show-grid]="showGrid"
    [class.disabled]="!mouseInteractionsEnabled"
    (dragover)="onDragOver($event)"
    (drop)="onDrop($event)"
    (mouseup)="onCanvasMouseUp($event)"
    (mousemove)="onCanvasMouseMove($event)"
    (wheel)="onCanvasWheel($event)"
    (mousedown)="onCanvasMouseDown($event)"
  >
    <!-- Canvas actions placed on the left -->

    <!-- Canvas Tools Toolbar - fixed at bottom center -->
    <ng-content select="[info]"></ng-content>

    <!-- Canvas Tools Toolbar - positioned dynamically -->
    <div class="canvas-tools-toolbar" *ngIf="showCanvasTools">
      <!-- Fixed positioning at bottom center -->

      <!-- Selection Mode -->
      <ava-button
        label=""
        variant="secondary"
        size="small"
        iconName="MousePointer2"
        iconPosition="only"
        [state]="canvasMode === 'select' ? 'active' : 'default'"
        (userClick)="setSelectMode()"
        title="Selection Mode"
      >
      </ava-button>

      <!-- Pan Mode -->
      <ava-button
        label=""
        variant="secondary"
        size="small"
        iconName="Hand"
        [iconColor]="'var(--rgb-brand-primary)'"
        iconPosition="only"
        [state]="canvasMode === 'pan' ? 'active' : 'default'"
        (userClick)="setPanMode()"
        title="Pan Mode"
      >
      </ava-button>

      <!-- Zoom In -->
      <ava-button
        label=""
        variant="secondary"
        size="small"
        iconName="ZoomIn"
        iconPosition="only"
        (userClick)="zoomIn(); setZoomMode()"
        title="Zoom In"
      >
      </ava-button>

      <!-- Zoom Out -->
      <ava-button
        label=""
        variant="secondary"
        size="small"
        iconName="ZoomOut"
        iconPosition="only"
        (userClick)="zoomOut(); setZoomMode()"
        title="Zoom Out"
      >
      </ava-button>
    </div>

    <!-- Floating Toolbar - positioned in top-right corner -->
    <div class="floating-toolbar" *ngIf="showToolbar">
      <ava-button
        *ngIf="enableUndo && !isExecuteMode"
        label=""
        variant="secondary"
        size="small"
        iconName="Undo"
        iconPosition="only"
        (userClick)="onUndo()"
        title="Undo"
      >
      </ava-button>

      <ava-button
        *ngIf="enableRedo && !isExecuteMode"
        label=""
        variant="secondary"
        size="small"
        iconName="Redo"
        iconPosition="only"
        (userClick)="onRedo()"
        title="Redo"
      >
      </ava-button>

      <ava-button
        *ngIf="enableReset && !isExecuteMode"
        label=""
        variant="secondary"
        size="small"
        iconPosition="only"
        iconName="RotateCcw"
        (userClick)="onReset()"
        title="Reset"
      >
      </ava-button>

      <!-- Execute Mode Button - Just Play Icon -->
      <ava-button
        *ngIf="isExecuteMode"
        label=""
        variant="primary"
        [customStyles]="{
          background:
            'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
          '--button-effect-color': '33, 90, 214',
        }"
        size="small"
        iconName="Play"
        iconPosition="only"
        (userClick)="onPrimaryButtonClick()"
        title="Run Agent"
      >
      </ava-button>

      <!-- Normal Mode Button - Execute with Text -->
      <ava-button
        *ngIf="!isExecuteMode"
        [label]="primaryButtonText"
        variant="primary"
        size="small"
        [customStyles]="{
          background:
            'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
          '--button-effect-color': '33, 90, 214',
        }"
        (userClick)="onPrimaryButtonClick()"
        [iconName]="primaryButtonIcon ? '' : 'Play'"
        iconPosition="right"
      >
      </ava-button>
    </div>
    <!-- Execute mode connections - positioned absolutely without viewport transform -->
    <svg
      class="connections-layer canvas-edges execute-connections"
      *ngIf="enableConnections && isExecuteMode"
      [style.position]="'absolute'"
      [style.top]="'0'"
      [style.left]="'0'"
      [style.width]="'100%'"
      [style.height]="'100%'"
      [style.pointer-events]="'none'"
      [style.z-index]="'5'"
    >
      <!-- Static connections for execute mode -->
      <path
        *ngFor="let edge of edges"
        [attr.d]="getEdgePath(edge)"
        [attr.id]="edge.id"
        class="edge-path"
        [class.edge-animated]="edge.animated"
        marker-end="url(#arrow-execute)"
      ></path>

      <!-- Arrow marker definition for execute mode - smaller for short connections -->
      <defs>
        <marker
          id="arrow-execute"
          viewBox="0 0 8 8"
          refX="7"
          refY="4"
          markerWidth="6"
          markerHeight="6"
          orient="auto"
          markerUnits="strokeWidth"
          class="edge-marker"
        >
          <path d="M 1 1 L 7 4 L 1 7 z" class="arrow-head"></path>
        </marker>
      </defs>
    </svg>

    <!-- Viewport container that applies the transform -->
    <div
      class="canvas-viewport"
      [style.transform]="
        isExecuteMode
          ? 'none'
          : 'translate(' +
            viewport.x +
            'px, ' +
            viewport.y +
            'px) scale(' +
            viewport.zoom +
            ')'
      "
    >
      <!-- All elements in the same transformed container -->
      <div class="canvas-content">
        <!-- Connections between nodes for build mode -->
        <svg
          class="connections-layer canvas-edges"
          *ngIf="enableConnections && !isExecuteMode"
        >
          <!-- Static connections -->
          <path
            *ngFor="let edge of edges"
            [attr.d]="getEdgePath(edge)"
            [attr.id]="edge.id"
            class="edge-path"
            [class.edge-animated]="edge.animated"
            marker-end="url(#arrow)"
          ></path>
          <path
            *ngFor="let edge of edges"
            [attr.d]="getEdgePath(edge)"
            [attr.id]="edge.id"
            class="edge-path"
            [class.edge-animated]="edge.animated"
            [style.stroke]="connectionColor"
            marker-end="url(#arrow)"
          ></path>

          <!-- Arrow marker definition -->
          <defs>
            <marker
              id="arrow"
              viewBox="0 0 12 12"
              refX="10"
              refY="6"
              markerWidth="10"
              markerHeight="10"
              orient="auto"
              markerUnits="strokeWidth"
              class="edge-marker"
            >
              <path d="M 2 2 L 10 6 L 2 10 z" class="arrow-head"></path>
            </marker>
          </defs>
        </svg>

        <!-- Nodes container -->
        <div class="nodes-container canvas-nodes">
          <!-- Render nodes using content projection with trackBy -->
          <ng-container *ngFor="let node of nodes; trackBy: trackByNodeId">
            <ng-container
              *ngTemplateOutlet="
                nodeTemplate;
                context: {
                  $implicit: node,
                  selected: selectedNodeId === node.id,
                  onDelete: onDeleteNode.bind(this),
                  onMove: onNodeMoved.bind(this),
                  onSelect: onNodeSelected.bind(this),
                  onDoubleClick: onNodeDoubleClicked.bind(this),
                  onStartConnection: onStartConnection.bind(this),
                  mouseInteractionsEnabled: mouseInteractionsEnabled,
                  canvasMode: isExecuteMode ? 'execute' : 'build',
                }
              "
            ></ng-container>
          </ng-container>
        </div>

        <!-- Temporary connection line - render on top -->
        <svg
          *ngIf="tempConnection.isActive && enableConnections"
          class="temp-connection-layer"
        >
          <path
            [attr.d]="getTempConnectionPath()"
            class="temp-connection-path"
            marker-end="url(#arrow)"
          ></path>
        </svg>

        <!-- Fallback message -->
        <div *ngIf="nodes.length === 0" class="no-nodes-message">
          {{ fallbackMessage }}
        </div>
      </div>
    </div>
  </div>
</div>
