.canvas-board-container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #f8f9fa;
  background-image: radial-gradient(circle, #d1d5db 1px, transparent 1px);
  background-size: 20px 20px;
  border-radius: 8px;
  overflow: hidden;
}

.header-inputs-section {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
  max-width: calc(100% - 40px); // Prevent overflow
  overflow: visible;

  .header-inputs-container {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: nowrap;

    .input-group {
      display: flex;
      align-items: center;
      gap: 8px;
      position: relative;

      .edit-icon {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #9ca3af;
        pointer-events: none;
        z-index: 10;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
        transition: color 0.2s ease;

        svg {
          width: 16px;
          height: 16px;
        }

        &:hover {
          color: #6b7280;
        }
      }
    }
  }

  // Agent Details Dropdown Styles
  .agent-details-dropdown {
    position: relative;

    .dropdown-container {
      position: relative;
      min-width: 200px;
      width: 300px; // Default width for normal/build mode

      .dropdown-toggle {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 8px 12px;
        background: white;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 14px;
        color: #374151;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          border-color: #9ca3af;
        }

        &.open {
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        span {
          font-weight: 500;
        }

        ava-icon {
          transition: transform 0.2s ease;
        }
      }

      .dropdown-content {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        margin-top: 4px;
        padding: 16px;

        .form-fields {
          display: flex;
          flex-direction: column;
          gap: 16px;

          .field-group {
            display: flex;
            flex-direction: column;
            gap: 6px;

            .field-label {
              font-size: 14px;
              font-weight: 500;
              color: #374151;
            }
          }
        }

        .dropdown-actions {
          display: flex;
          justify-content: flex-end;
          gap: 8px;
          margin-top: 16px;
          padding-top: 16px;
          border-top: 1px solid #e5e7eb;

          .cancel-btn,
          .apply-btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
          }

          .cancel-btn {
            background: white;
            border: 1px solid #d1d5db;
            color: #374151;

            &:hover {
              background: #f9fafb;
              border-color: #9ca3af;
            }
          }

          .apply-btn {
            background: #3b82f6;
            border: 1px solid #3b82f6;
            color: white;

            &:hover {
              background: #2563eb;
              border-color: #2563eb;
            }
          }
        }
      }
    }
  }
}

.input-fields-section {
  padding: 16px;
  background-color: var(--dashboard-bg-lighter);
  border-bottom: 1px solid var(--form-input-border);

  .input-fields-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .input-field {
      display: flex;
      flex-direction: column;
      min-width: 200px;
      flex: 1;

      .field-label {
        font-size: 14px;
        font-weight: 500;
        color: var(--text-primary);
        margin-bottom: 6px;

        .required-asterisk {
          color: var(--error-color);
          margin-left: 2px;
        }
      }

      .field-input,
      .field-textarea {
        padding: 8px 12px;
        border: 1px solid var(--form-input-border);
        border-radius: 6px;
        font-size: 14px;
        color: var(--text-primary);
        background-color: var(--form-input-bg);
        transition:
          border-color 0.15s ease,
          box-shadow 0.15s ease;

        &:focus {
          outline: none;
          border-color: var(--dashboard-primary);
          box-shadow: 0 0 0 2px rgba(var(--dashboard-primary-rgb), 0.1);
        }

        &:disabled {
          background-color: var(--form-input-disabled-bg);
          color: var(--text-disabled);
          cursor: not-allowed;
        }

        &::placeholder {
          color: var(--text-placeholder);
        }
      }

      .field-textarea {
        resize: vertical;
        min-height: 80px;
      }
    }
  }
}

.metadata-section {
  padding: 16px;
  background-color: #f9fafb;
  border-bottom: 1px solid #d1d5db;

  .metadata-container {
    .metadata-title {
      font-size: 16px;
      font-weight: 600;
      color: #374151;
      margin-bottom: 16px;
    }

    .dropdown-row-vertical {
      display: flex;
      flex-direction: column;
      gap: 12px;

      @media (min-width: 768px) {
        flex-direction: row;
        flex-wrap: wrap;

        > * {
          flex: 1;
          min-width: 200px;
        }
      }
    }
  }
}

.agent-name-input {
  position: relative;
  width: 180px;
}

// Agent Type Tag styling
.agent-type-tag {
  .type-tag {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 4px 10px;
    background-color: #e9effd;
    color: #215ad6;
    font-size: 12px;
    font-weight: 500;
    border-radius: 16px;
    border: 1px solid #6c96f2;
    white-space: nowrap;
    height: 24px;
  }
}

// Execute mode specific styling
.canvas-board-container.execute-mode {
  .agent-name-input {
    width: 180px !important; // Keep same width in execute mode

    ::ng-deep {
      .ava-textbox {
        width: 180px !important;
      }
    }
  }

  .floating-toolbar {
    // More compact toolbar in execute mode
    gap: 6px !important;
    top: 20px !important;
    right: 20px !important;

    .primary-btn {
      // Ensure the play button is properly styled
      min-width: 36px !important;
      height: 36px !important;
      padding: 8px !important;
    }
  }

  .agent-details-dropdown {
    .dropdown-container {
      width: 76% !important; // Set to 76% width in execute mode
      min-width: 200px !important; // Maintain minimum width
    }
  }

  .header-inputs-section {
    top: 20px !important;
    left: 20px !important;
  }
}

.canvas-tools-toolbar {
  position: absolute;
  display: flex;
  gap: 4px;
  z-index: 20;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  user-select: none;
  align-items: center;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.canvas-action {
  // @extend .canvas-tools-toolbar;
  // bottom: 16px;
  // left: 160px;
  background: none;
  border: none;
  box-shadow: none;
  width: fit-content;
  width: 300px;
}

::ng-deep canvas-tools-toolbar svg {
  stroke: rgb(var(--rgb-brand-primary)) !important;
}

// Custom styling for toolbar ava-buttons to make them compact
.canvas-tools-toolbar {
  ::ng-deep ava-button {
    .ava-button {
      min-width: 32px !important;
      width: 32px !important;
      height: 32px !important;
      padding: 6px !important;
      border-radius: 6px !important;
    }
  }
}

.floating-toolbar {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  z-index: 1000;
}

.navigation-hint {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  font-size: 11px;
  color: var(--text-color);

  span {
    display: flex;
    align-items: center;
    gap: 4px;

    kbd {
      background-color: var(--form-input-bg);
      border: 1px solid var(--form-input-border);
      border-radius: 3px;
      padding: 2px 6px;
      font-size: 10px;
      font-family: monospace;
      color: var(--text-secondary);
    }
  }
}

.canvas-container {
  flex-grow: 1;
  background-color: var(--dashboard-bg-lighter);
  position: relative;
  // border: 1px dashed var(--form-input-border);
  cursor: grab;
  border-radius: 8px;
  transition: none;

  &.show-grid {
    background-image:
      linear-gradient(rgba(200, 200, 200, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(200, 200, 200, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  &:active {
    cursor: grabbing;
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.8;

    &::after {
      content: "Canvas interactions are disabled";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 16px;
      border-radius: 4px;
      font-size: 12px;
      pointer-events: none;
      z-index: 1001;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover::after {
      opacity: 1;
    }
  }
}

.canvas-viewport {
  width: 100%;
  height: 100%;
  position: absolute;
  transform-origin: 0 0;
  transition: none;
}

.canvas-content {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: visible;
}

.connections-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 5;
  pointer-events: none;

  .edge-path {
    fill: none;
    stroke-width: 1.5px; // Slightly thinner for cleaner appearance
    pointer-events: all;
    cursor: pointer;
    stroke: #9ca1aa; // Match marker color
    transition: none; // Remove transition for smooth movement

    &.edge-animated {
      stroke-dasharray: 5;
      animation: dash 1s linear infinite;
    }

    &:hover {
      stroke-width: 2px; // Reduced hover width for consistency
      opacity: 0.8;
      transition: stroke-width 0.1s ease; // Only transition hover effects
    }
  }

  .arrow-head {
    fill: #9ca1aa; // Exact same color as connection line
    stroke: none;
    opacity: 1;
  }

  .edge-marker {
    overflow: visible;
  }

  marker {
    overflow: visible;
  }

  // Execute mode: Make connections uniform and clean
  .canvas-area.execute-mode & {
    .edge-path {
      stroke: #9ca1aa; // Match the marker color for consistency
      stroke-width: 1.5px; // Thinner, consistent width
      stroke-linecap: round; // Rounded line caps for cleaner look

      &:hover {
        stroke: #9ca1aa; // Keep same color on hover
        stroke-width: 2px; // Slightly thicker on hover
      }
    }

    .arrow-head {
      fill: #9ca1aa; // Match connection line color exactly
    }
  }
}

.nodes-container {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 10;
}

.temp-connection-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 100;
  pointer-events: none;

  .temp-connection-path {
    fill: none;
    stroke-width: 1.5px; // Match the main connection line thickness
    stroke-dasharray: 5;
    opacity: 0.7;
    stroke: #9ca1aa; // Match marker color exactly
    animation: dash 1s linear infinite;
  }

  marker {
    overflow: visible;
  }
}

.no-nodes-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--text-secondary);
  font-size: 16px;
  text-align: center;
  pointer-events: none;
  z-index: 5;
}

@keyframes dash {
  from {
    stroke-dashoffset: 10;
  }
  to {
    stroke-dashoffset: 0;
  }
}

.navigation-hint {
  padding: 8px 16px;
  background-color: var(--dashboard-bg-lighter);
  border-bottom: 1px solid var(--form-input-border);
  font-size: 12px;
  color: var(--text-secondary);
  text-align: center;

  span {
    margin: 0 4px;
  }
}
