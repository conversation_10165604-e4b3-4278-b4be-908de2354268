import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from '@ava/play-comp-library';

export interface QuickAction {
  id: string;
  label: string;
  icon: string;
  action?: () => void;
}

@Component({
  selector: 'app-quick-actions',
  standalone: true,
  imports: [CommonModule, ButtonComponent],
  templateUrl: './quick-actions.component.html',
  styleUrls: ['./quick-actions.component.scss'],
})
export class QuickActionsComponent {
  @Input() actions: QuickAction[] = [
    {
      id: 'build-agent',
      label: 'Build Agent',
      icon: '/svgs/icons/awe_add_modetor.svg',
    },
    {
      id: 'build-workflow',
      label: 'Build Workflow',
      icon: '/svgs/icons/awe_add_chart.svg',
    },
    {
      id: 'create-prompt',
      label: 'Create Prompt',
      icon: '/svgs/icons/awe_add_bookmark_filled.svg',
    },
    {
      id: 'create-tool',
      label: 'Create Tool',
      icon: '/svgs/icons/awe_build.svg',
    },
    {
      id: 'create-guardrail',
      label: 'Create Guardrail',
      icon: '/svgs/icons/awe_guardrail.svg',
    },
    {
      id: 'create-knowledge-base',
      label: 'Create Knowledge Base',
      icon: '/svgs/icons/awe_knowledge_base.svg',
    },
  ];

  @Output() actionClick = new EventEmitter<QuickAction>();

  isExpanded = false;

  toggleExpanded(): void {
    this.isExpanded = !this.isExpanded;
  }

  onActionClick(action: QuickAction): void {
    if (action.action) {
      action.action();
    }
    this.actionClick.emit(action);
  }

  onButtonClick(event: any) {}
}
