/* CSS Variables for consistent theming */
:host {
  --quick-actions-bg: #f8f9fa;
  --quick-actions-border: #e9ecef;
  --quick-actions-text: #212529;
  --quick-actions-button-bg: #6c757d;
  --quick-actions-button-hover: #5a6268;
  --quick-actions-shadow: rgba(0, 0, 0, 0.1);
  --quick-actions-shadow-hover: rgba(0, 0, 0, 0.15);
  --border-radius: 24px;
  --transition-speed: 0.3s;
}

.quick-actions-wrapper {
  background-color: var(--quick-actions-bg);
  border: 1px solid var(--quick-actions-border);
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: 55px;
  height: 100%;
  transition:
    width var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1),
    max-width var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1),
    min-width var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  box-shadow: 0 2px 8px var(--quick-actions-shadow);
  position: relative;
}

.quick-actions-wrapper:hover {
  box-shadow: 0 4px 12px var(--quick-actions-shadow-hover);
}

/* Expanded state */
.quick-actions-wrapper.expanded {
  width: 320px;
  max-width: 320px;
  min-width: 320px;
}

/* Toggle button section */
.quick-actions-toggle {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px 16px;
  cursor: pointer;
  transition: background-color var(--transition-speed);
  flex-shrink: 0;
  border-bottom: 1px solid var(--quick-actions-border);
}

.quick-actions-wrapper:not(.expanded) .quick-actions-toggle {
  padding: 20px 0;
  justify-content: center;
  border-bottom: none;
}

.toggle-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 6px;
  background-color: transparent;
  position: relative;
  transition: all var(--transition-speed);
}

.toggle-button svg {
  transition: transform var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
  width: 16px;
  height: 16px;
  color: var(--quick-actions-text);
}

.toggle-button svg.rotate {
  transform: rotate(180deg);
}

.quick-actions-toggle span {
  font-weight: 600;
  font-size: 16px;
  color: var(--quick-actions-text);
  white-space: nowrap;
}

/* Expanded content area */
.quick-actions-content {
  padding: 20px 16px;
  overflow-y: auto;
  flex-grow: 1;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  border-radius: 50px;
  border: none;
  background: var(--quick-actions-button-bg);
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  width: 100%;
  text-align: left;
  color: white;
}

.action-button:hover {
  background: var(--quick-actions-button-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--quick-actions-shadow-hover);
}

.action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.action-icon img {
  width: 20px;
  height: 20px;
  filter: brightness(0) invert(1);
}

.action-label {
  font-size: 16px;
  font-weight: 500;
  color: white;
  white-space: nowrap;
}

/* Collapsed icons */
.quick-actions-icons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 20px 0;
  overflow-y: auto;
  flex-grow: 1;
}

.icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background: var(--quick-actions-button-bg);
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  flex-shrink: 0;
}

.icon-button:hover {
  background: var(--quick-actions-button-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--quick-actions-shadow-hover);
}

.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-wrapper img {
  width: 18px;
  height: 18px;
  filter: brightness(0) invert(1);
}

/* Responsive design */
@media (max-width: 768px) {
  .quick-actions-wrapper.expanded {
    width: 280px;
    max-width: 280px;
    min-width: 280px;
  }

  .action-buttons {
    gap: 8px;
  }

  .action-button {
    padding: 12px 16px;
  }

  .quick-actions-icons {
    gap: 12px;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  :host {
    --quick-actions-bg: #2d3748;
    --quick-actions-border: #4a5568;
    --quick-actions-text: #e2e8f0;
    --quick-actions-button-bg: #4a5568;
    --quick-actions-button-hover: #2d3748;
    --quick-actions-shadow: rgba(0, 0, 0, 0.3);
    --quick-actions-shadow-hover: rgba(0, 0, 0, 0.4);
  }
}
