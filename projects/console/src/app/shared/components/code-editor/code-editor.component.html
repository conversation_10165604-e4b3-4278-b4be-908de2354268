<div class="code-editor-container" [ngClass]="customCssClass">
  <div class="editor-header" *ngIf="title || primaryButtonSelected.observed">
    <div class="title-action-row">
      <h3 class="editor-title" *ngIf="title">{{ title }}</h3>
      <span class="run-btn-wrapper" *ngIf="primaryButtonSelected.observed">
        <ava-button
          label="Test"
          variant="primary"
          size="small"
          [disabled]="state.loading || isPrimaryButtonDisabled"
          (userClick)="onPrimaryButtonClick()"
          class="run-black-btn">
        </ava-button>
      </span>
    </div>

    <div class="editor-actions" *ngIf="actionButtons.length">
      <ng-container *ngFor="let btn of actionButtons; let i = index">
        <span [ngClass]="btn.customClass">
          <ava-button
            class="action-btn"
            [label]="btn.label"
            variant="secondary"
            [iconName]="btn.icon || ''"
            size="small"
            [pill]="true"
            [disabled]="state.loading"
            (userClick)="onActionButtonClick(i)">
          </ava-button>
        </span>
      </ng-container>
    </div>
  </div>

  <div *ngIf="state.loading" class="loading-container">
    <div class="loading-spinner"></div>
    <p>Loading Code Editor...</p>
  </div>

  <div *ngIf="state.error" class="error-container">
    <div class="error-icon">⚠️</div>
    <h4>Failed to Load Editor</h4>
    <p>{{ state.errorMessage }}</p>
    <ava-button
      label="Retry"
      variant="secondary"
      size="small"
      [pill]="true"
      (userClick)="initializeEditor()">
    </ava-button>
  </div>

  <div
    #editorContainer
    class="monaco-editor-container"
    [class.hidden]="state.loading || state.error">
  </div>

  <div *ngIf="state.processing" class="editor-loader-overlay">
    <div class="editor-loader-spinner"></div>
  </div>

  <div class="editor-footer" *ngIf="footerText">
    <p class="footer-note">
      <strong>Note:</strong> {{ footerText }}
    </p>
  </div>
</div>
