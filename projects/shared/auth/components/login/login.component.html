<div id="login-container" class="row">
  <div class="col-5 p-0">
    <img class="login-image" src="assets/images/login.jpg" alt="" />
  </div>
  <div
    class="col-7 p-0 d-flex justify-content-center align-items-center login-section"
  >
    <div class="sign-in-container">
      <div class="heading">
        <h3 class="mb-2 main-heading">{{ labels.labels.main_heading }}</h3>
        <p class="sub-heading">{{ labels.labels.sub_heading }}</p>
      </div>
      <form [formGroup]="loginForm" class="new-login-form">
        <div class="form-field-wrapper">
          <ava-textbox
            [label]="labels.labels.username"
            [placeholder]="labels.placeholders.username"
            [required]="true"
            formControlName="username"
            [error]="getFieldError('username')"
            [iconSeparator]="false"
            [iconSpacing]="'normal'"
          >
            <ava-icon
              slot="icon-end"
              iconName="x"
              [iconSize]="16"
              [cursor]="true"
              (click)="clearUsername()"
              [disabled]="false"
            >
            </ava-icon>
          </ava-textbox>
        </div>
        <div class="form-field-wrapper mt-4 mb-4">
          <ava-textbox
            [label]="labels.labels.password"
            [type]="showPassword() ? 'text' : 'password'"
            [placeholder]="labels.placeholders.password"
            [required]="true"
            formControlName="password"
            [error]="getFieldError('password')"
            [iconSeparator]="false"
            [iconSpacing]="'normal'"
          >
            <ava-icon
              slot="icon-end"
              [iconName]="showPassword() ? 'eye-off' : 'eye'"
              [iconSize]="18"
              [cursor]="true"
              (click)="togglePasswordVisibility()"
              [disabled]="false"
            >
            </ava-icon>
          </ava-textbox>
        </div>
        <div class="new-buttons-container">
          <div class="sign-in-button mb-5">
            <ava-button
              class="mb-4"
              [label]="labels.labels.sign_in + ' ' + labels.labels.arrow"
              variant="primary"
              size="large"
              [customStyles]="{
                background:
                  'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                '--button-effect-color': '33, 90, 214',
              }"
              [processing]="isLoading()"
              [width]="'100%'"
              (userClick)="onBasicLogin()"
            >
            </ava-button>
          </div>

          <div
            class="d-flex justify-content-center align-items-center new-separator"
          >
            {{ labels.labels.seperator }}
          </div>
          <div class="login-with-company mt-5">
            <ava-button
              [label]="labels.labels.login_with_company"
              variant="secondary"
              size="large"
              [width]="'100%'"
              (userClick)="onCompanyLogin()"
            >
            </ava-button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
