<div class="search-container">
  <div class="search-wrapper">
    <div class="search-icon">
      <ava-icon iconName="search" iconSize="20px" iconColor="#666D99" class="search-svg-icon"></ava-icon>
    </div>

    <input type="text" class="search-input" aria-label="Search" [(ngModel)]="searchValue">
    <div class="animated-placeholder-wrapper" *ngIf="!searchValue">
      <div class="animated-placeholder">
        <span *ngFor="let text of placeholderTexts">{{ text }}</span>
      </div>
    </div>
    
    <!-- Send Button -->
    <button class="send-button" (click)="onSend()">
      <ava-icon iconName="send-horizontal" iconSize="24px" iconColor="black" ></ava-icon>
    </button>
  </div>
</div>
