import { Component, OnInit, OnDestroy, Output, EventEmitter } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { IconComponent } from "@ava/play-comp-library";

@Component({
  selector: 'app-search-bar',
  templateUrl: './search-bar.component.html',
  styleUrls: ['./search-bar.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, IconComponent],
})
export default class SearchBar {
  placeholderTexts: string[] = [
    "What you want to do today",
    "How can I make your day productive?"
  ];
  searchValue: string = '';
  
  @Output() sendClicked = new EventEmitter<string>();

  constructor() {}

  onSend() {
    if (this.searchValue.trim()) {
      this.sendClicked.emit(this.searchValue);
    }
  }
}