.search-container {
  display: flex;
  width: 1097px;
  height: 126px;
  padding: 24px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 10px;
  border-radius: 16px;
  border: 1px solid #FFF;
  background: linear-gradient(109deg, rgba(255, 255, 255, 0.32) 3.98%, rgba(255, 255, 255, 0.40) 95.92%);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.07);
  box-sizing: border-box;
  margin: 0 auto;
  position: relative;
  z-index: 2;
  overflow: visible;
}

.search-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  z-index: 2;
}

.search-icon {
  position: absolute;
  left: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.search-svg-icon {
  width: 24px;
  height: 24px;
  display: block;
}


.search-input {
  width: 100%;
  padding: 16px 60px 16px 48px;
  border-radius: 16px;
  background-color: #fff;
  color: #666D99;
  font-family: Mulish;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  outline: none;
  border: double 1px transparent;
  background-image: linear-gradient(white, white),
    linear-gradient(97deg, rgba(67, 131, 230, 1) 8.06%, rgba(250, 167, 74, 1) 73.25%);
  background-origin: border-box;
  background-clip: padding-box, border-box;
}

.animated-placeholder-wrapper {
  position: absolute;
  left: 48px;
  /* Align with input's padding-left */
  top: 50%;
  transform: translateY(-50%);
  overflow: hidden;
  /* **Crucial:** Hides the parts of spans not currently visible */
  height: 24px;
  /* **Crucial:** This must be the exact height of one line of text */
  display: flex;
  align-items: center;
  pointer-events: none;
  /* Allows clicks to pass through to the input */
  width: calc(100% - 48px - 16px);
  /* Adjust width to fit within the input, considering padding */
}

.animated-placeholder {
  display: flex;
  flex-direction: column;
  height: 100%;
  animation: slide-up-down 10s ease-in-out infinite;
  line-height: 1.2;
}

.animated-placeholder span {
  color: #666D99;
  font-family: 'Mulish', -apple-system, 'Roboto', 'Helvetica', sans-serif;
  font-size: 20px;
  font-weight: 400;
  white-space: nowrap;
  /* Prevent text from wrapping */
  /* Each span needs to be the exact height of the wrapper to facilitate precise sliding */
  height: 100%;
  display: flex;
  align-items: center;
  /* Vertically align text within its own 'slot' */
  flex-shrink: 0;
  /* Prevent spans from shrinking */
  flex-grow: 0;
  /* Prevent spans from growing */
}

@keyframes slide-up-down {
  0%, 40% {
    transform: translateY(0%);
  }
  50%, 90% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(0%);
  }
}

/* Keep your existing media queries */
@media (max-width: 1200px) {
  .search-container {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .search-wrapper {
    max-width: 100%;
  }

  .search-container {
    height: auto;
  }

  .search-input {
    font-size: 18px;
  }

  .animated-placeholder span {
    font-size: 18px;
  }

  .animated-placeholder-wrapper {
    height: 22px;
    /* Adjust height for smaller screens, ensure it matches font size */
  }

  .search-ball-left, .search-ball-right {
    width: 60px;
  }
}

awe-icons svg {
  margin-top: 10px;
}

.send-button {
  position: absolute;
  right: 16px;
  top: 38%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  &:focus {
    outline: none;
    background-color: rgba(0, 0, 0, 0.1);
  }
}