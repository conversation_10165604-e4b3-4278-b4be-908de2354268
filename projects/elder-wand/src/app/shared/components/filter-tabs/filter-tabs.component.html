<div class="filter-tabs">
  <div class="tabs-wrapper">
    <!-- Visible Tabs Container -->
    <div class="tabs-container">
      <button *ngFor="let tab of visibleTabs" class="tab-item" [class.active]="isActiveTab(tab.id)"
        (click)="onTabClick(tab.id)">
        <ava-icon *ngIf="tab.icon" [iconName]="tab.icon" iconSize="18px" iconColor="#000"></ava-icon>
        <span>{{ tab.label }}</span>
      </button>
    </div>

    <!-- Filters Dropdown (Always Visible) -->
    <!-- <div class="filter-dropdown-container">
      <button class="tab-item filter-dropdown-btn" [class.active]="showDropdown" (click)="toggleDropdown($event)">
        <i class="fas fa-filter"></i>
        <span>Filters</span>
        <i class="fas fa-chevron-down ms-2"></i>
      </button> -->

      <!-- Dropdown Menu -->
      <!-- <div class="filter-dropdown" *ngIf="showDropdown">
        <button *ngFor="let tab of dropdownTabs" class="dropdown-item" [class.active]="isActiveTab(tab.id)"
          (click)="onTabClick(tab.id, $event)">
          <ava-icon *ngIf="tab.icon" [iconName]="tab.icon" iconSize="18px" iconColor="#666D99"></ava-icon>
          <span>{{ tab.label }}</span>
        </button>
      </div> -->
    <!-- </div> -->
  </div>
</div>