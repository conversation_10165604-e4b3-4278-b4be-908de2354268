.filter-tabs {
  background-color: rgba(237, 237, 243, 0.50);
  border-radius: 12px;
  padding: 8px;
  margin-bottom: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--Border-Color, #E5E7EB);

  .tabs-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px;
    position: relative;
    width: 100%;

    .tabs-container {
      display: flex;
      flex: 1;
      gap: 8px;
      overflow-x: auto;
      scrollbar-width: none;
      /* Firefox */
      -ms-overflow-style: none;
      /* IE and Edge */
      margin-right: 8px; // Space for filters button

      &::-webkit-scrollbar {
        display: none;
        /* Chrome, Safari, Opera */
      }

      .tab-item {
        flex: 1;
        min-width: fit-content;
      }
    }
  }

  .tab-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 0;
    border-radius: 8px;
    border: none;
    background: transparent;
    color: var(--Text-Body, #000);
    font-family: Mulish;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    cursor: pointer;
    transition: all 0.2s ease-in-out;

    i {
      font-size: 16px;
    }

    &:hover {
      background-color: white;
    }

    &.active {
      background-color: white;
      background-image: linear-gradient(90deg, rgb(247, 145, 28) 40%, rgba(67, 131, 230, 1) 70%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      color: transparent;

      i {
        background: inherit;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }
  }

  .filter-dropdown-container {
    position: sticky;
    right: 4px;
    top: 4px;
    margin-left: 8px;
    z-index: 10;
    background-color: var(--background-color);

    .filter-dropdown-btn {
      border: 1px solid var(--Border-Color, #E5E7EB);
      padding-right: 12px;
      white-space: nowrap;

      i.fa-chevron-down {
        font-size: 12px;
        transition: transform 0.2s ease-in-out;
      }

      &.active {
        i.fa-chevron-down {
          transform: rotate(180deg);
        }
      }
    }

    .filter-dropdown {
      position: absolute;
      top: calc(100% + 8px);
      right: 0;
      background-color: var(--background-color);
      border-radius: 12px;
      border: 1px solid var(--Border-Color, #E5E7EB);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      min-width: 200px;
      z-index: 1000;
      padding: 8px;
      animation: fadeIn 0.2s ease-in-out;

      .dropdown-item {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;
        padding: 8px 16px;
        border: none;
        background: transparent;
        color: var(--Text-Body, #666D99);
        font-size: 14px;
        font-weight: 500;
        text-align: left;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        border-radius: 6px;

        i {
          font-size: 16px;
          width: 20px;
          text-align: center;
        }

        &:hover {
          background-color: var(--Hover-Color, rgba(0, 0, 0, 0.05));
        }

        &.active {
          background-color: white;
          background-image: linear-gradient(90deg, #8B8DDA 0%, #F63B8F 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          color: transparent;

          i {
            background: inherit;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }
        }
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive styles
@media (max-width: 768px) {
  .filter-tabs {
    padding: 6px;

    .tabs-wrapper {
      gap: 6px;

      .tabs-container {
        gap: 6px;
      }
    }

    .tab-item {
      padding: 6px 12px;
      font-size: 13px;

      i {
        font-size: 14px;
      }
    }

    .filter-dropdown-container {
      .dropdown-item {
        padding: 6px 12px;
        font-size: 13px;

        i {
          font-size: 14px;
        }
      }
    }
  }
}