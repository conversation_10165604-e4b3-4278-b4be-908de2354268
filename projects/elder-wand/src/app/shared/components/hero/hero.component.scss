.hero-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-top: 2rem;
}

.hero-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.greeting-section {
  max-width: 800px;
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.greeting-header {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.greeting-title {
text-align: center;
font-family: Mulish;
font-size: 48px;
font-style: normal;
font-weight: 700;
line-height: normal;
letter-spacing: -0.912px;
margin-bottom: 12px;
}

.name {
  background: #4383E6;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-family: Mulish;
  font-size: 48px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  letter-spacing: -0.912px;
  white-space: nowrap;
}

.greeting-message {
  color: rgba(0, 0, 0, 0.80);
  font-family: 'Mulish';
  font-size: 24px;
  font-weight: 400;
  line-height: 150%;
  letter-spacing: -0.456px;
  width: 1000px;
  margin: 0;
}

p {
  color: var(--Text-Body, #33364D);
  text-align: center;
  font-family: Mulish;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  line-height: 150%;
  letter-spacing: -0.456px;
}

.search-bar-background {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 126px;
}

.search-ball {
  position: absolute;
  z-index: 1;
  pointer-events: none;
}

.search-ball-left {
  left: -60px;
  top: 40%;
  transform: translateY(-50%);
  width: 120px;
  height: 120px;
}

.search-ball-right {
  right: -29px;
  top: 40%;
  transform: translateY(-50%);
  width: 58px;
  height: 58px;
}

.search-bar-container {
  width: 100%;
  display: flex;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.transparent-bg {
  background: transparent !important;
  box-shadow: none !important;
}

.stars-icon {
  height: 35px;
  width: 27px;
  margin-right: 16px;
  animation: blink 3s ease-in-out infinite;
  vertical-align: middle;
  margin-top: 18px;
}

/* Animations */
@keyframes blink {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes blink {

  0%,
  100% {
    opacity: 0;
  }

  50% {
    opacity: 0.8;
    /* Slightly reduce opacity for smoother blink */
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .greeting-title, .name {
    font-size: 28px;
  }
  .greeting-message {
    font-size: 16px;
  }
  .search-ball-left, .search-ball-right {
    width: 60px;
  }
}