import { Component, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import SearchBar from '../search-bar/search-bar.component';
import { GlobalStoreService } from '../../service/global-store.service';
import { IconComponent } from "@ava/play-comp-library";

interface HeroContent {
  greeting: string;
  message: string;
}

const HERO_TEXT_LOOKUP: Record<string, HeroContent> = {
  Sales: {
    greeting: 'Heya',
    message:
      "I see you have a demo with Dover CPQ in 5 minutes. I've kept your space organized and ready for you!",
  },
  'Project Team': {
    greeting: 'Welcome back',
    message:
      'It’s been a busy week, hasn’t it? I’ve organized your space with the tasks you use most often, so you don’t have to keep searching!',
  },
};

@Component({
  selector: 'app-hero',
  templateUrl: './hero.component.html',
  styleUrls: ['./hero.component.scss'],
  standalone: true,
  imports: [CommonModule, SearchBar, IconComponent],
  encapsulation: ViewEncapsulation.None
})
export default class Hero {
  selectedUser: any;
  heroContent: HeroContent = HERO_TEXT_LOOKUP['Sales'];

  constructor(private readonly globalStoreService: GlobalStoreService) { }

  ngOnInit() {
    this.globalStoreService.selectedUser.subscribe((user) => {
      this.selectedUser = user;
      if (user && user.type) {
        this.heroContent = HERO_TEXT_LOOKUP[user.type] || this.heroContent;
      }
    });
  }

  get userName(): string {
    return this.selectedUser?.name || 'there';
  }
}
