import { Component, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { IconsComponent } from '@awe/play-comp-library';
import { IconComponent } from "@ava/play-comp-library";

interface Studio {
  id: number;
  title: string;
  description: string;
  image: string;
  link: string;
}

@Component({
  selector: 'app-studios',
  templateUrl: './studios.component.html',
  styleUrls: ['./studios.component.scss'],
  standalone: true,
  imports: [CommonModule, RouterModule, IconsComponent, IconComponent],
  encapsulation: ViewEncapsulation.None
})
export class StudiosComponent {
  studios: Studio[] = [
    {
      id: 1,
      title: 'Experience Studio',
      description: 'Evaluating design elements for accuracy and consistency',
      image: 'assets/icons/experience_studio.png',
      link: '/experience',
    },
    {
      id: 2,
      title: 'Product Studio',
      description: 'Evaluating design elements for accuracy and consistency',
      image: 'assets/icons/product_studio.png',
      link: '/product',
    },
    {
      id: 3,
      title: 'Data Studio',
      description: 'Evaluating design elements for accuracy and consistency',
      image: 'assets/icons/data_studio.png',
      link: '/data',
    },
    {
      id: 5,
      title: 'FinOps Studio',
      description: 'Evaluating design elements for accuracy and consistency',
      image: 'assets/icons/finops_studio.png',
      link: '/finops',
    },
  ];
}
