.studios-container {
  padding: 0px 32px;

  .studios-header {
    // margin-bottom: 40px;
    text-align: center;

    .title-wrapper {
      display: inline-flex;
      align-items: center;
      gap: 12px;

      .sparkle {
        font-size: 28px;
      }

      h1 {
        color: #14161F;
        text-align: center;
        font-family: Mulish;
        font-size: 48px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        letter-spacing: -0.912px;
        margin: 0 0 24px 0;

      }
    }
  }

  .studios-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
    margin: 0 0 140px 0;

    .studio-card {
      background: white;
      border-radius: 16px;
      overflow: hidden;
      cursor: pointer;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

      .card-content {
        display: flex;
        justify-content: space-between;
        padding: 16px;
        height: 100%;
        border-radius: 12px;
        border: 1px solid rgba(67, 131, 230, 0.10);
        background: #FFF;
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);

        .text-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          padding: 32px;

          .text-top {
            display: flex;
            flex-direction: column;
            gap: 16px;

            h2 {
              color: #1D1D1D;
              font-family: Mulish;
              font-size: 32px;
              font-style: normal;
              font-weight: 700;
              line-height: 112%;
              margin: 0;
            }

            .description {
              color: #595959;
              font-family: Mulish;
              font-size: 20px;
              font-style: normal;
              font-weight: 500;
              line-height: 150%;
              margin: 0;
              width: 344px;
              text-align: left;
            }
          }

          .arrow-button {
            color: #A0A0A0; // grey by default
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s, color 0.3s;
            border: 2px solid #D3D3D3; // grey border
            background: #fff;
          }
        }

        .image-container {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 280px;
          width: 460px;

          img {
            max-width: 100%;
            height: auto;
            object-fit: contain;
          }
        }
      }
    }
  }
}

.image-container img {
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.studio-card:hover .image-container img {
  opacity: 1;
}

@media (max-width: 768px) {
  .studios-grid {
    grid-template-columns: 1fr !important;
  }
}