<div class="studios-container">
  <div class="studios-header">
    <div class="title-wrapper">
        <!-- <awe-icons iconName="stars" color="#F96CAB"></awe-icons> -->
      <h1>Studios</h1>
    </div>
  </div>

  <div class="studios-grid">
    <div class="studio-card" *ngFor="let studio of studios" [routerLink]="studio.link">
      <div class="card-content">
        <div class="text-content">
          <div class="text-top">
            <h2>{{ studio.title }}</h2>
            <p class="description">{{ studio.description }}</p>
          </div>
          <div class="arrow-button">
            <ava-icon iconName="ArrowUpRight" iconSize="22px" iconColor="grey"></ava-icon>
          </div>
        </div>
        <div class="image-container">
          <img [src]="studio.image" [alt]="studio.title">
        </div>
      </div>
    </div>
  </div>
</div>