<div class="my-agents-container">
  <h2 class="my-agents-title">My Agents</h2>
  <div class="row">
    <div class="col-12 col-md-6 col-lg-4" *ngFor="let card of cards">
      <div class="my-agent-card">
        <div class="card-header">
          <ava-icon [iconName]="card.iconName" [iconColor]="card.iconColor" iconSize="24px" class="card-icon"></ava-icon>
          <span class="card-title">{{ card.title }}</span>
        </div>
        <div class="card-content">
          <span class="card-description">{{ card.description }}</span>
        </div>
        <div class="card-footer">
          <ng-container *ngFor="let btn of card.buttons">
            <button
              class="card-btn"
              type="button"
              (click)="onButtonClick(card, btn)"
            >
              {{ btn.label }}
              <ava-icon *ngIf="btn.iconName" [iconName]="btn.iconName" class="btn-icon" iconColor="#4383E6"></ava-icon>
            </button>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</div>