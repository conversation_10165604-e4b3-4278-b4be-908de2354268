::ng-deep .outer-box.light {
  background-color: transparent !important;
  box-shadow: none !important;
}

::ng-deep .container {
  background-color: transparent !important;
}

.custom-header-container {
  width: 100%;
  box-sizing: border-box;
  margin: 72px;
}

.header-flex-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 52px 52px 0 52px;
}

.header-left-logo {
  display: flex;
  align-items: center;
  width: 160px;
  height: 36px;
}

.axos-logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.header-right-content {
  display: flex;
  align-items: center;
  gap: 32px;
}

.custom-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(20, 70, 146, 0.08);
}

.header-right-content>* {
  width: 40px !important;
  height: 40px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box;
}