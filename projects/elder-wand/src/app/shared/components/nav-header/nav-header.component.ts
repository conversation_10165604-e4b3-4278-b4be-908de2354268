import {
  Component,
  OnInit,
  HostListener,
  Output,
  EventEmitter,
  Input,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from '@awe/play-comp-library';
import { ThemeService } from '../../services/theme.service';
import { IconComponent } from "@ava/play-comp-library";

interface User {
  name: string;
  role: string;
  type: string;
  projects?: string[];
}

interface Project {
  id: string;
  name: string;
  logo: string;
}

@Component({
  selector: 'app-nav-header',
  standalone: true,
  imports: [CommonModule, HeaderComponent, IconComponent],
  templateUrl: './nav-header.component.html',
  styleUrl: './nav-header.component.scss',
})
export class NavHeaderComponent implements OnInit {
  logoSrc: string = '';
  themeToggleIcon: string = '';
  consoleIcon: string = '';
  userAvatar: string = '';
  translateIcon: string = '';
  isDropdownOpen: boolean = false;
  currentProject: string = '';
  selectedUser: User | null = null;
  isFlipping: boolean = false;

  @Input() projects: Project[] = [
    { id: 'axos', name: 'AXOS', logo: 'axos-client-logo.svg' },
    { id: 'cvs', name: 'CVS', logo: 'cvs-client-logo.svg' },
  ];

  @Output() userSelected = new EventEmitter<User>();
  @Output() projectSwitched = new EventEmitter<string>();

  users: User[] = [
    // {
    //   name: 'John',
    //   role: 'Sales',
    //   type: 'Sales',
    // },
    {
      name: 'Akshay',
      role: 'Project Team',
      type: 'Project Team',
      projects: ['axos', 'cvs'],
    },
  ];
  constructor(private themeService: ThemeService) { }

  ngOnInit(): void {
    this.logoSrc = `ascendion-logo-light.svg`;
    this.themeToggleIcon = `svgs/header/toggle-theme/theme-toggle-light.svg`;
    this.consoleIcon = `svgs/header/menu-light.svg`;
    this.userAvatar = `svgs/header/user-avatar.svg`;
    this.translateIcon = `svgs/header/translate.svg`;

    if (this.projects.length > 0) {
      this.currentProject = this.projects[0].name;
      this.startLogoFlip();
    }
    this.updateThemeAssets();
    new MutationObserver(() => this.updateThemeAssets()).observe(
      document.body,
      {
        attributes: true,
        attributeFilter: ['class'],
      },
    );
    this.selectUser(this.users[0])
  }

  private updateThemeAssets(): void {
    const currentTheme = this.themeService.getCurrentTheme();
    // this.themeToggleIcon = `assets/svgs/header/toggle-theme/theme-toggle-${currentTheme}.svg`;
    // this.consoleIcon = `assets/svgs/header/menu-${currentTheme}.svg`;
  }

  toggleTheme(): void {
    this.themeService.toggleTheme();
    this.updateThemeAssets();
  }

  getInitials(name: string): string {
    if (!name) return '';
    const parts = name.split(' ');
    if (parts.length === 1) return parts[0].charAt(0).toUpperCase();
    return `${parts[0].charAt(0)}${parts[parts.length - 1].charAt(0)}`.toUpperCase();
  }

  toggleProfileDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  selectUser(user: User): void {
    this.selectedUser = user;
    this.userSelected.emit(user);
    this.isDropdownOpen = false;
  }

  switchProject(project: string): void {
    if (this.currentProject !== project) {
      this.currentProject = project;
      this.projectSwitched.emit(project);
      this.startLogoFlip();
    }
  }

  getAvailableProjects(user: User): Project[] {
    if (user.type === 'Sales') return [];
    return this.projects.filter((p) => user.projects?.includes(p.id));
  }

  getCurrentProjectLogo(): string {
    const project = this.projects.find((p) => p.name === this.currentProject);
    return project ? project.logo : '';
  }

  startLogoFlip(): void {
    if (this.currentProject) {
      this.isFlipping = true;
    } else {
      this.isFlipping = false;
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.profile-dropdown')) {
      this.isDropdownOpen = false;
    }
  }
}
