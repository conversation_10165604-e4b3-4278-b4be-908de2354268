.news-blogs {
  padding: 0 32px;

  .header {
    text-align: center;
    margin-top: 140px;

    h2 {
      color: #14161f;
      font-family: Mulish;
      font-size: 48px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      letter-spacing: -0.912px;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      margin: 0;

      .star {
        font-size: 28px;
      }

      .gradient-text {
        color: #14161f;
      }
    }
  }

  .blog-grid {
    display: flex;
    flex-direction: row;
    overflow-x: auto;
    gap: 24px;
    padding: 24px 0;
    scroll-behavior: smooth;
    scrollbar-width: none; // Firefox
    -ms-overflow-style: none; // IE 10+
    margin-bottom: 140px;
  }

  .blog-grid::-webkit-scrollbar {
    display: none; // Chrome/Safari
  }

  .blog-card {
    width: 528px;
    flex: 0 0 auto;
    padding: 0; // Remove side paddings
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden; // Ensures image doesn't overflow
  }

  .blog-image {
    width: 100%;
    height: 240px;
    border-radius: 12px 12px 0 0;
    overflow: hidden;
    margin: 20px 0 12px 0; // 20px top, 0 left/right, 12px bottom
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px; // Add 20px left/right padding
  }

  .blog-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px 12px 0 0;
  }

  .blog-content {
    padding: 0 20px 20px 20px; // Add padding for content only
  }

  .blog-content h3 {
    color: #292C3D;
    font-family: Mulish;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 100%;
    margin: 0 0 8px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .blog-content .description {
    color: #33364D;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 150%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    margin: 0;
    -webkit-box-orient: vertical;
  }

  .divider {
    border-bottom: 1px solid #e0e0e0;
    margin: 8px 0 16px 0;
    height: 0;
  }

  .blog-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .author {
    display: flex;
    align-items: center;
  }

  .author-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 8px;
  }

  .author-name {
    color: #292C3D;
    font-family: Mulish;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 140%;
    letter-spacing: 0.07px;
  }

  .views {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666D99;
    gap: 8px;
  }

  .views i {
    margin-right: 4px;
    height: 14px;
    width: 14px;
  }
}

.blog-content h3,
.blog-content .description,
.author-name,
.views {
  text-align: left;
}

@media (max-width: 1200px) {
  .news-blogs {
    .container-fluid {
      padding: 0 5%;
    }

    .blog-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .blog-card {
      height: 220px;

      .blog-image {
        flex: 0 0 200px;
      }

      .blog-content {
        h3 {
          font-size: 20px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .news-blogs {
    padding: 40px 0;

    .container-fluid {
      padding: 0 20px;
    }

    .header h2 {
      font-size: 36px;

      .star {
        font-size: 20px;
      }
    }

    .blog-grid {
      grid-template-columns: 1fr;
      gap: 20px;
    }

    .blog-card {
      height: auto;
      flex-direction: column;

      .blog-image {
        flex: none;
        height: 200px;
      }

      .blog-content {
        padding: 20px;

        h3 {
          font-size: 18px;
          margin-bottom: 8px;
        }

        p {
          margin-bottom: 12px;
        }

        .read-more {
          margin-bottom: 12px;
        }

        .blog-read-more-link {
          /* Updated class name here too */
          margin-bottom: 12px;
        }

        .blog-footer {
          padding-top: 12px;
        }
      }
    }
  }
}