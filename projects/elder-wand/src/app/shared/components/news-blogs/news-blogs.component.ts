import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { IconsComponent } from '@awe/play-comp-library';
import { IconComponent } from "@ava/play-comp-library";

interface BlogPost {
  id: number;
  title: string;
  description: string;
  image: string;
  author: {
    name: string;
    avatar: string;
  };
  views: number;
}

@Component({
  selector: 'app-news-blogs',
  templateUrl: './news-blogs.component.html',
  styleUrls: ['./news-blogs.component.scss'],
  standalone: true,
  imports: [CommonModule, RouterModule, IconsComponent, IconComponent],
})
export class NewsBlogsComponent {
  blogPosts: BlogPost[] = [
    {
      id: 101,
      title: 'Gen AI 101 Learning Path',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien,',
      image: 'assets/icons/genai-101.svg',
      author: {
        name: '<PERSON><PERSON> Doe',
        avatar: 'assets/icons/ellipse-avatar.svg',
      },
      views: 440,
    },
    {
      id: 102,
      title: 'Gen AI 101 Learning Path',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien,',
      image: 'assets/icons/genai-102.svg',
      author: {
        name: 'Jhone Doe',
        avatar: 'assets/icons/ellipse-avatar.svg',
      },
      views: 440,
    },
    {
      id: 103,
      title: 'Gen AI 101 Learning Path',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien,',
      image: 'assets/icons/genai-103.svg',
      author: {
        name: 'Jhone Doe',
        avatar: 'assets/icons/ellipse-avatar.svg',
      },
      views: 440,
    },
    {
      id: 104,
      title: 'Gen AI 101 Learning Path',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien,',
      image: 'assets/icons/genai-104.svg',
      author: {
        name: 'Jhone Doe',
        avatar: 'assets/icons/ellipse-avatar.svg',
      },
      views: 440,
    },
  ];

  readMore(id: number): void {
    // Implement your logic here for what happens when "Read More" is clicked.
    // For example, navigate to a detailed blog post page:
    // this.router.navigate(['/blog', id]);
    console.log('Read More clicked for blog post ID:', id);
    // alert(`Navigating to blog post ${id}`); // For demonstration
  }
}