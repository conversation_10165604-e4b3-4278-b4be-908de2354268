<section class="news-blogs">
  <div class="container-fluid">
    <div class="header">
      <h2>
        <awe-icons iconName="stars" color="#F96CAB"></awe-icons>
        <span class="gradient-text">
          We are great at
        </span>
      </h2>
    </div>
    <div class="blog-grid">
      <article class="blog-card" *ngFor="let post of blogPosts">
        <div class="blog-image">
          <img [src]="post.image" [alt]="post.title" />
        </div>
        <div class="blog-content">
          <h3>{{ post.title }}</h3>
          <p class="description" [title]="post.description">
            {{ post.description }}
          </p>
          <div class="divider"></div>
          <div class="blog-footer">
            <div class="author">
              <img [src]="post.author.avatar" [alt]="post.author.name" class="author-avatar" />
              <span class="author-name">{{ post.author.name }}</span>
            </div>
            <div class="views">
              <ava-icon iconName="eye" iconColor="#666D99" iconSize="14px" class="views-icon"></ava-icon>
              <span>{{ post.views }}</span>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</section>