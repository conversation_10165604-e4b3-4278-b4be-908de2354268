<div class="agents-container">
  <app-filter-tabs *ngIf="!showExploreButton" [tabs]="filterTabs" [activeTab]="activeFilter"
    (tabChange)="onFilterChange($event)">
  </app-filter-tabs>

  <div class="agents-grid row">
    <div class="col-12 col-md-6 col-lg-3" *ngFor="let agent of agents; let i = index">
      <div class="agent-card" (click)="showAgentDetails(agent)"
        >
        <div class="card-content">
          <div class="card-header">
            <h2>{{ agent.title }}</h2>
            <div class="rating">
              <ava-icon iconName="star" iconSize="18px" iconColor="#FFD700" class="agent_star-icon"></ava-icon>
              {{ agent.rating }}
            </div>
          </div>

          <p class="description">{{ agent.description | truncate: 75 }}</p>

          <div class="card-footer">
            <div class="users">
              <ava-icon iconName="user" iconSize="16px" iconColor="#858aad" class="profile-svg-icon"></ava-icon>
              {{ agent.users }}
            </div>
            <div class="agent-time-ago">
              3 days ago
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="explore-more" *ngIf="showExploreButton">
    <awe-button label="Explore More" variant="primary" class="w-100" width="245px"
      gradient="linear-gradient(90deg, #FAA74A 0%, #4383E6 100%)" (click)="navigate()"></awe-button>
  </div>

  <!-- Overlay and Agent Details Panel -->
  <div class="agent-details-overlay" *ngIf="selectedAgent" (click)="closeAgentDetails($event)">
    <div class="agent-details-panel" (click)="$event.stopPropagation()">
      <div class="details-header">
        <button class="close-btn" (click)="closeAgentDetails($event)">
          <ava-icon iconName="x" iconSize="36px" iconColor="#52577A"></ava-icon>
        </button>
      </div>

      <div class="details-content">
        <div class="upper-content">
          <div>
            <div class="details-title">
              <!-- <span class="sparkle">✨</span> -->
              <h2>{{ selectedAgent.title }}</h2>
            </div>

            <p class="details-description">
              Effortlessly convert Ruby code to Spring Boot with optimised
              migration
            </p>

            <!-- Add to List Button -->
            <div class="add-to-list">
             <ava-icon iconName="text" iconSize="16px" iconColor="black"></ava-icon>
              <span>Add to List</span>
            </div>

            <!-- Tags Section -->
            <div class="agent-tags">
              <span class="tag">#1 in Agents</span>
              <span class="tag">Code Migration</span>
              <span class="tag">Development</span>
              <span class="tag">Backend</span>
            </div>

            <!-- Orange Divider -->
            <div class="agent-divider"></div>

            <div class="details-metrics">
              <!-- First Row: Top Labels -->
              <div class="metrics-row">
                <div class="metric">
                  <span class="label-1">Category</span>
                </div>
                <div class="metric">
                  <span class="label-1">Developed</span>
                </div>
                <div class="metric">
                  <span class="label-1">Relevancy</span>
                </div>
                <div class="metric">
                  <span class="label-1">Agent</span>
                </div>
              </div>

              <!-- Second Row: Icons/Values -->
              <div class="metrics-row">
                <div class="metric">
                  <ava-icon iconName="code" iconSize="25px" iconColor="#616874" class="code-icon"></ava-icon>
                </div>
                <div class="metric">
                  <ava-icon iconName="user" iconSize="25px" iconColor="#616874" class="profile-svg-icon"></ava-icon>
                </div>
                <div class="metric">
                  <div class="score">9.5/10</div>
                </div>
                <div class="metric">
                  <div class="rating">
                    <span class="score">4.5</span>
                    <ava-icon iconName="star" iconSize="18px" iconColor="#616874" class="agent-star-icon"></ava-icon>
                  </div>
                </div>
              </div>

              <!-- Third Row: Bottom Labels -->
              <div class="metrics-row">
                <div class="metric">
                  <span class="label">Type</span>
                </div>
                <div class="metric">
                  <span class="label">Name</span>
                </div>
                <div class="metric">
                  <span class="label">Score</span>
                </div>
                <div class="metric">
                  <span class="label">Rating</span>
                </div>
              </div>
            </div>

            <div class="agent-divider"></div>
            
          </div>

          <div>
            <div class="details-section">
              <h3>What it's for</h3>
              <p>
                A agent that converts Ruby code to Spring Boot can be highly
                beneficial for organizations migrating from Ruby on Rails to
                Java Spring Boot. However, the effectiveness depends on several
                factors, including the complexity of the application, language
                differences, and the capabilities of the conversion agent.
              </p>
            </div>
          </div>
          <div class="action-button">
             <awe-button label="Go to Playground" variant="primary" class="w-100" width="100%"
      gradient="linear-gradient(90deg, #FAA74A 0%, #4383E6 100%)" (click)="goToPlayground()"></awe-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
