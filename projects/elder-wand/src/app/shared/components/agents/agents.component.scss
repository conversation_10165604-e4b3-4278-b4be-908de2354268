// Generic electric card styles
.electric-card {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(140, 101, 247, 0.1), rgba(232, 67, 147, 0.1));
    opacity: 0;
    z-index: 0;
  }

  &::after {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    border-radius: 12px;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.8) 0%,
        rgba(173, 216, 230, 0.8) 25%,
        rgba(255, 255, 255, 0.8) 50%,
        rgba(173, 216, 230, 0.8) 75%,
        rgba(255, 255, 255, 0.8) 100%);
    background-size: 400% 100%;
    -webkit-mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    opacity: 0;
    pointer-events: none;
    filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.7)) drop-shadow(0 0 4px rgba(173, 216, 230, 0.5));
  }

  .card-content {
    position: relative;
    z-index: 1;
    padding: 9px;
  }
}

// Agent specific styles
.agents-container {
  padding: 24px 32px 0px 32px;
  display: flex;
  flex-direction: column;

  .agents-header {


    h1 {
      font-family: Mulish;
      font-size: 40px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      letter-spacing: -0.76px;
      background: linear-gradient(90deg, #6566CD 36.04%, #F96CAB 118.04%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin: 0;
    }
  }

  .agent-card {
    /* Remove border-color and transition for border color */
    @extend .electric-card;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 0px;
      margin-bottom: 12px;

      h2 {
        color: rgba(0, 0, 0, 0.80);
        font-family: Inter;
        font-size: 24px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%;
        letter-spacing: -0.456px;
        margin: 0;
      }

      .rating {
        display: flex;
        align-items: center;
        gap: 4px;
        font-weight: 500;

        .star {
          color: #FFD700;
        }
      }
    }

    .description {
      color: #474C6B;
      text-align: left;
      font-family: Mulish;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%;
      margin-bottom: 24px;
    }

    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0px;
      width: 100%;

      .users {
        color: #858aad;
        font-family: Mulish;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap:4px;
      }

      .agent-time-ago {
        color: var(--Neutral-N-800, #474C6B);
        font-family: Mulish;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
      }
    }
  }

  .explore-more {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin-bottom: 140px;
  }

  awe-button span {
    font-size: 24px;
  }

  awe-button button {
    border-radius: 8px;
  }

  // Agent Details Panel Styles
  .agent-details-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    z-index: 1000;
    display: flex;
    justify-content: flex-end;
    animation: fadeIn 0.3s ease;
    font-family: 'Mulish', sans-serif;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  @keyframes slideIn {
    from {
      transform: translateX(100%);
    }

    to {
      transform: translateX(0);
    }
  }

  .agent-details-panel {
    width: 650px;
    height: 100%;
    background-color: #fff;
    box-shadow: -4px 0 24px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    animation: slideIn 0.3s ease;
    display: flex;
    flex-direction: column;
    position: relative;

    .details-header {
      padding: 0;
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 10;

      .close-btn {
        background: none;
        border: none;
        cursor: pointer;
        font-size: 24px;
        color: #666;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 50%;
      }
    }

    .details-content {
      padding: 52px 32px 32px 32px;
      display: flex;
      flex-direction: column;
      min-height: 100%;
      justify-content: space-between;

      .upper-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 30px;
        padding-bottom: 30px;
      }

      .add-to-list {
        display: flex;
        align-items: center;
        background-color: #EDEDF3;
        border-radius: 24px;
        height: 32px;
        width: fit-content;
        padding: 0 16px;
        gap: 0;
        font-size: 16px;
        font-weight: 500;
        color: #23262F;
        margin: 0 0 32px 0;

        ava-icon {
          margin-right: 2px;
          display: flex;
          align-items: center;
        }
      }

      .add-to-list button,
      .add-to-list awe-button,
      .add-to-list awe-button button {
        min-width: 0 !important;
      }

      .action-button {
        margin-top: auto;
        width: 100%;
        align-self: flex-end;
      }

      .agent-tags {
        display: flex;
        flex-wrap: wrap; // Allow tags to wrap to the next line
        align-items: flex-start; // Align tags to the top
        gap: 12px; // Space between tags
        margin: 12px 0 16px 0; // Adjust margin as needed
        padding: 0;
        width: auto; // Let it grow naturally
        justify-content: flex-start; // Align tags to the left
        margin: 0 0 32px 0;

        .tag {
          display: flex;
          align-items: center;
          text-align: center;
          color: #6B7280;
          font-family: Inter;
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px;
          padding: 12px 24px;
          border-radius: 24px;
          height: 38px;
          border: 1px solid orange; // Replace with your gradient or color
          background: #fff; // Optional: white background
          max-width: 170px; // Remove min-width so tags shrink to fit content
        }
      }

      .details-title {

        h2 {
          color: #4C515B;
          font-family: Mulish;
          font-size: 32px;
          font-style: normal;
          font-weight: 700;
          line-height: 38.4px;
          margin-bottom: 12px;
          margin-top: 0;
        }
      }

      .details-description {
        color: #6B7280;
        font-family: Mulish;
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
        margin-bottom: 8px;
        text-align: left;
        margin-top: 0;
        margin-top: 10px;
        margin-bottom: 12px;
      }

      .details-metrics {
        display: flex;
        flex-direction: column;
        margin-bottom: 10px;
        padding: 0;

        .metrics-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .metric {
          flex: 1;
          min-width: auto;
          text-align: center;

          .label-1 {
            color: #858AAD;
            text-align: center;
            font-family: Mulish;
            font-size: 16px;
            font-style: normal;
            font-weight: 800;
            line-height: 110%;
          }

          .label {
            color: #666D99;
            font-family: Mulish;
            font-size: 19px;
            font-style: normal;
            font-weight: 600;
            line-height: 97%;
          }

          .score {
            color: #616874;
            font-family: Mulish;
            font-size: 20px;
            font-style: normal;
            font-weight: 700;
            line-height: 150%;
          }

          .rating {
            color: #616874;
            font-family: Mulish;
            font-size: 20px;
            font-style: normal;
            font-weight: 700;
            line-height: 150%;
            .agent-star-icon{
              padding-bottom: 6px;
              margin-left: 4px;
            }
          }
        }
      }

      .details-section {
        margin-bottom: 15px;
        border-radius: 12px;
        background: linear-gradient(180deg, rgba(250, 167, 74, 0.30) 0%, rgba(67, 131, 230, 0.30) 100%);
        padding: 24px;

        &:last-child {
          margin-bottom: 0;
        }

        h3 {
          color: #4C515B;
          font-family: Mulish;
          font-size: 24px;
          font-style: normal;
          font-weight: 600;
          line-height: 28.8px;
          text-align: left;
          margin-bottom: 12px;
          margin-top: 0;
        }

        p {
          color: #616874;
          font-family: Inter;
          font-size: 1rem;
          font-style: normal;
          font-weight: 400;
          line-height: 150%;
          text-align: left;
        }

        .type-badge {
          display: inline-flex;
          align-items: center;
          gap: 8px;
          padding: 8px 16px;
          border-radius: 4px;
          font-size: 0.875;
          font-family: 'Mulish', sans-serif;

          .icon {
            font-size: 1rem;
          }
        }
      }
    }
  }
}

// Global styles for when details panel is open
:host ::ng-deep body.details-panel-open {
  overflow: hidden;
}

button {
  min-width: 0 !important;
}

.agent-divider {
  width: 100%;
  height: 1px;
  background: orange; // Use the same orange as the tag border
  border: none;
  margin: 32px 0;
  border-radius: 1px;
}

// Remove right margin from awe-button globally in this component
:host ::ng-deep awe-button {
  margin-top: 0 !important;
  padding: 0 !important;
}