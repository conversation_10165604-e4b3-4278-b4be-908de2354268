import { Component, Input, Pipe, PipeTransform, HostL<PERSON>ener, On<PERSON><PERSON>roy, ViewEncapsulation, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { ButtonComponent} from '@awe/play-comp-library';
import { Agent } from '../../interfaces/agent-list.interface';
import {
  FilterTabsComponent,
  FilterTab,
} from '../filter-tabs/filter-tabs.component';
import { IconComponent } from "@ava/play-comp-library";

@Pipe({
  name: 'truncate',
  standalone: true
})
export class TruncatePipe implements PipeTransform {
  transform(value: string, limit = 75): string {
    if (!value) return '';
    if (value.length <= limit) return value;
    return value.substring(0, limit) + '...';
  }
}

@Component({
  selector: 'app-agents',
  templateUrl: './agents.component.html',
  styleUrls: ['./agents.component.scss'],
  standalone: true,
  imports: [CommonModule, RouterModule, ButtonComponent, FilterTabsComponent, TruncatePipe, IconComponent],
  encapsulation: ViewEncapsulation.None
})
export class AgentsComponent implements OnInit, OnDestroy {
  @Input() agents: Agent[] = [];
  @Input() showExploreButton = true;

  private originalAgents: Agent[] = [];

  // New property to track the selected agent for details panel
  selectedAgent: Agent | null = null;

  // Removed agentHoverState and studioColors logic

  activeFilter = 'all';
  filterTabs: FilterTab[] = [
    { id: 'all', label: 'All', priority: 100 },
    { id: 'experience', label: 'Experience Studio', icon: 'lightbulb', priority: 90 },
    { id: 'product', label: 'Product Studio', icon: 'box', priority: 80 },
    { id: 'data', label: 'Data Studio', icon: 'database', priority: 70 },
    { id: 'finops', label: 'FinOps Studio', icon: 'dollar-sign', priority: 60 },
  ];

  constructor(private readonly router: Router) { }

  ngOnInit() {
    this.originalAgents = [...this.agents];
  }

  /**
   * Host listener for keyboard events for accessibility
   * @param event KeyboardEvent
   */
  @HostListener('document:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent): void {
    // Close the details panel when Escape key is pressed
    if (event.key === 'Escape' && this.selectedAgent) {
      this.selectedAgent = null;
      document.body.classList.remove('details-panel-open');
    }
  }

  onFilterChange(filterId: string) {
    this.activeFilter = filterId;
    // Filter agents by studio type if not 'all'
    if (filterId === 'all') {
      this.agents = [...this.originalAgents];
    } else {
      const studioMap: any = {
        experience: 'Experience Studio',
        product: 'Product Studio',
        data: 'Data Studio',
        finops: 'FinOps Studio',
      };
      this.agents = this.originalAgents.filter((agent: Agent) => agent.studio?.type === studioMap[filterId]);
    }
  }

  navigate() {
    this.router.navigateByUrl('/agent-list');
  }

  /**
   * Shows the agent details panel for the selected agent
   * @param agent The agent to display details for
   */
  showAgentDetails(agent: Agent): void {
    this.selectedAgent = agent;
    // Add a class to the body to prevent scrolling when overlay is open
    document.body.classList.add('details-panel-open');
  }

  /**
   * Closes the agent details panel
   * @param event The click event
   */
  closeAgentDetails(event: MouseEvent): void {
    event.stopPropagation();
    this.selectedAgent = null;
    // Remove the class from the body to allow scrolling again
    document.body.classList.remove('details-panel-open');
  }

  /**
   * Navigates to the playground for the selected agent
   */
  goToPlayground(): void {
    // You can replace this with the actual navigation logic
    this.router.navigate(['/playground', this.selectedAgent?.id]);
  }

  /**
   * Clean up when component is destroyed
   */
  ngOnDestroy(): void {
    // Make sure to remove the class from body when component is destroyed
    document.body.classList.remove('details-panel-open');
  }
}
