import { ApplicationConfig, importProvidersFrom, provideZoneChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideAnimations } from '@angular/platform-browser/animations';

import { routes } from './app.routes';
import {LucideAngularModule, Search, Bot, User, Workflow, List, Wrench, ShieldCheck, Book, Plus, BookOpen, RefreshCw, Languages, Sun, X, Text, SendHorizontal,Star, Code, CloudUpload, FileText, BookText, Eye, Lightbulb, Box, Database, DollarSign, Sparkle, Sparkles, ArrowUpRight} from 'lucide-angular';
import { provideHttpClient } from '@angular/common/http';
import { MarkdownModule } from 'ngx-markdown';




export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }), 
    provideRouter(routes),
    provideAnimations(),
provideHttpClient(),
    importProvidersFrom(
      LucideAngularModule.pick({
Search,
User,
Bot,
Workflow,
List,
Wrench,
ShieldCheck,
BookText,
Plus,
BookOpen,
RefreshCw,
Languages,
Sun,
Text,
X,
SendHorizontal,
Star,
Code,
CloudUpload,
FileText,
Eye,
Lightbulb,
Box,
Database,
DollarSign,
Sparkles,
ArrowUpRight
      }),
      MarkdownModule.forRoot(),
    ),
  ],
};
