// Environment configuration for <PERSON> Wan<PERSON> (Launchpad) Application
export const environment = {
    production: false,
  
  // Application URLs
    experienceStudioUrl: 'http://localhost:4201',
    productStudioUrl: 'http://localhost:4202',
  
  // API Configuration
  // API_BASE_URL will be injected from Docker environment, fallback to default
  apiBaseUrl: (window as any).__env?.API_BASE_URL || 'http://localhost:3000',
  apiUrl: (window as any).__env?.API_BASE_URL || 'http://localhost:3000',
  
  // Helper function to get API endpoint with base URL
  getApiUrl: (endpoint: string) => {
    const baseUrl = (window as any).__env?.API_BASE_URL || 'http://localhost:3000';
    return `${baseUrl}${endpoint}`;
  }
  };