<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>ExperienceStudio</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
  <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Mulish:wght@400;600;700&display=swap" rel="stylesheet">

  <!-- Environment configuration - injected from Docker -->
  <script src="env-config.js"></script>

  <!-- Critical CSS to prevent FOUC -->
  <style>
    body {
      margin: 0;
      font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
      line-height: 1.5;
    }

    app-root {
      display: block;
      min-height: 100vh;
    }
  </style>

</head>
<body>
  <app-root></app-root>
</body>
</html>
