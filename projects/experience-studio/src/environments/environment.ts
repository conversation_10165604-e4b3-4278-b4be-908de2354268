// Environment configuration for Experience Studio Application
export const environment = {
    production: false,
  
  // Application URLs
    elderWandUrl: 'http://localhost:4200',
  
  // API Configuration
  // API_BASE_URL will be injected from Docker environment, fallback to default
  apiBaseUrl: (window as any).__env?.API_BASE_URL || 'http://localhost:3000',
  apiUrl: (window as any).__env?.API_BASE_URL || "https://ava-plus-experience-studio-api-dev.azurewebsites.net",
  
  // Helper function to get API endpoint with base URL
  getApiUrl: (endpoint: string) => {
    const baseUrl = (window as any).__env?.API_BASE_URL || 'http://localhost:3000';
    return `${baseUrl}${endpoint}`;
  }
  };