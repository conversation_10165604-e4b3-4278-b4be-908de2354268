export interface CardOption {
  id: string;
  heading: string;
  description: string;
  actionText?: string;
  type: string;
  timestamp?: string;
}

// Enhanced interfaces for filtering and search functionality

/**
 * Project type definitions with user-friendly labels and colors
 */
export interface ProjectType {
  value: string;
  label: string;
  color: string;
  description: string;
}

/**
 * Available project types with their display properties
 */
export const PROJECT_TYPES: Record<string, ProjectType> = {
  wireframe_generation: {
    value: 'wireframe_generation',
    label: 'Wireframe Generation',
    color: '#8c65f7',
    description: 'UI Design and wireframe projects'
  },
  app_generation: {
    value: 'app_generation',
    label: 'App Generation',
    color: '#e84393',
    description: 'Full application generation projects'
  }
} as const;

/**
 * Sorting options for project lists
 */
export type SortOption = 'name-asc' | 'name-desc' | 'date-asc' | 'date-desc' | 'modified-asc' | 'modified-desc';

/**
 * Sort option definitions with user-friendly labels
 */
export interface SortDefinition {
  value: SortOption;
  label: string;
  icon?: string;
}

export const SORT_OPTIONS: SortDefinition[] = [
  { value: 'modified-desc', label: 'Last Modified (Newest)', icon: 'fas fa-clock' },
  { value: 'modified-asc', label: 'Last Modified (Oldest)', icon: 'fas fa-clock' },
  { value: 'name-asc', label: 'Name (A-Z)', icon: 'fas fa-sort-alpha-down' },
  { value: 'name-desc', label: 'Name (Z-A)', icon: 'fas fa-sort-alpha-up' },
  { value: 'date-desc', label: 'Date Created (Newest)', icon: 'fas fa-calendar-plus' },
  { value: 'date-asc', label: 'Date Created (Oldest)', icon: 'fas fa-calendar-plus' }
];

/**
 * Filter state interface for managing active filters
 */
export interface FilterState {
  searchTerm: string;
  selectedProjectTypes: string[];
  sortBy: SortOption;
  showFilters: boolean;
}

/**
 * Enhanced card option with additional metadata for filtering
 */
export interface EnhancedCardOption extends CardOption {
  projectType: ProjectType;
  createdDate?: string;
  lastModified?: string;
  tags?: string[];
}

/**
 * Search and filter configuration
 */
export interface SearchFilterConfig {
  enableSearch: boolean;
  enableProjectTypeFilter: boolean;
  enableSorting: boolean;
  defaultSort: SortOption;
  searchPlaceholder: string;
  debounceTime: number;
}
