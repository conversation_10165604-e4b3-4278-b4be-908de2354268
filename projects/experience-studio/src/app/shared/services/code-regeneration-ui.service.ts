import { Injectable, inject } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { createLogger } from '../utils/logger';

/**
 * Interface for code regeneration progress states
 */
export interface CodeRegenerationProgress {
  event: 'code-regen';
  status: 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  progress: 'CODE_GENERATION' | 'BUILD' | 'DEPLOY';
  files?: any[];
  url?: string;
  metadata?: any;
}

/**
 * Interface for UI state management during code regeneration
 */
export interface CodeRegenerationUIState {
  isCodeGenerationLoading: boolean;
  isBuildInProgress: boolean;
  isDeployInProgress: boolean;
  shouldShowLoadingScreen: boolean;
  shouldSwitchToCodeTab: boolean;
  shouldSwitchToPreviewTab: boolean;
  shouldRefreshPreview: boolean;
  latestFiles: any[];
  currentVersion: number;
}

/**
 * Interface for accordion append events with replacement flag
 */
export interface AccordionAppendEvent {
  files: any[];
  version: number;
  replaceAll?: boolean; // Flag to indicate complete replacement vs append
}

/**
 * Service to manage UI states during code regeneration workflow
 * Handles specific SSE event sequences for code-regen events
 * CRITICAL: Processes files exactly like PAGES_GENERATED step for initial generation
 */
@Injectable({
  providedIn: 'root'
})
export class CodeRegenerationUIService {

  // State subjects
  private uiStateSubject = new BehaviorSubject<CodeRegenerationUIState>({
    isCodeGenerationLoading: false,
    isBuildInProgress: false,
    isDeployInProgress: false,
    shouldShowLoadingScreen: false,
    shouldSwitchToCodeTab: false,
    shouldSwitchToPreviewTab: false,
    shouldRefreshPreview: false,
    latestFiles: [],
    currentVersion: 1
  });

  // Event subjects for specific actions
  private showLoadingScreenSubject = new Subject<boolean>();
  private appendToAccordionSubject = new Subject<AccordionAppendEvent>();
  private appendToCodeViewerSubject = new Subject<any[]>();
  private switchToCodeTabSubject = new Subject<void>();
  private switchToPreviewTabSubject = new Subject<void>();
  private refreshPreviewSubject = new Subject<string>();

  // Public observables
  public readonly uiState$ = this.uiStateSubject.asObservable();
  public readonly showLoadingScreen$ = this.showLoadingScreenSubject.asObservable();
  public readonly appendToAccordion$ = this.appendToAccordionSubject.asObservable();
  public readonly appendToCodeViewer$ = this.appendToCodeViewerSubject.asObservable();
  public readonly switchToCodeTab$ = this.switchToCodeTabSubject.asObservable();
  public readonly switchToPreviewTab$ = this.switchToPreviewTabSubject.asObservable();
  public readonly refreshPreview$ = this.refreshPreviewSubject.asObservable();

  constructor() {

  }

  /**
   * Process code regeneration progress events and update UI accordingly
   * CRITICAL: Handles files exactly like PAGES_GENERATED step
   */
  processCodeRegenerationProgress(progress: CodeRegenerationProgress): void {

    const currentState = this.uiStateSubject.value;

    switch (progress.progress) {
      case 'CODE_GENERATION':
        this.handleCodeGenerationProgress(progress, currentState);
        break;
      case 'BUILD':
        this.handleBuildProgress(progress, currentState);
        break;
      case 'DEPLOY':
        this.handleDeployProgress(progress, currentState);
        break;
    }
  }

  /**
   * Handle CODE_GENERATION progress events
   * CRITICAL: This handles the exact same file format as PAGES_GENERATED step
   */
  private handleCodeGenerationProgress(
    progress: CodeRegenerationProgress, 
    currentState: CodeRegenerationUIState
  ): void {
    if (progress.status === 'IN_PROGRESS') {

      // Show loading screen on right side
      this.showLoadingScreenSubject.next(true);
      
      // Update state
      this.updateUIState({
        ...currentState,
        isCodeGenerationLoading: true,
        shouldShowLoadingScreen: true
      });

    } else if (progress.status === 'COMPLETED') {

      // Hide loading screen
      this.showLoadingScreenSubject.next(false);
      
      // Increment version for new regeneration
      const newVersion = currentState.currentVersion + 1;
      
      if (progress.files && progress.files.length > 0) {

        // CRITICAL: Override previous codes and file models - complete replacement
        // This ensures regeneration completely replaces the previous generation
        this.appendToAccordionSubject.next({
          files: progress.files,
          version: newVersion,
          replaceAll: true // Flag to indicate complete replacement
        });
        
        // CRITICAL: Override all files in code-viewer
        this.appendToCodeViewerSubject.next(progress.files);
      }
      
      // Update state
      this.updateUIState({
        ...currentState,
        isCodeGenerationLoading: false,
        shouldShowLoadingScreen: false,
        latestFiles: progress.files || [],
        currentVersion: newVersion
      });
    }
  }

  /**
   * Handle BUILD progress events
   */
  private handleBuildProgress(
    progress: CodeRegenerationProgress, 
    currentState: CodeRegenerationUIState
  ): void {
    if (progress.status === 'IN_PROGRESS') {

      // Switch to code tab to show latest changes
      this.switchToCodeTabSubject.next();
      
      // Update state
      this.updateUIState({
        ...currentState,
        isBuildInProgress: true,
        shouldSwitchToCodeTab: true
      });

    } else if (progress.status === 'COMPLETED') {

      if (progress.files && progress.files.length > 0) {
        // Append any new files to code-viewer and accordion
        this.appendToCodeViewerSubject.next(progress.files);
        this.appendToAccordionSubject.next({
          files: progress.files,
          version: currentState.currentVersion,
          replaceAll: false // Append, don't replace
        });
      }
      
      // Update state
      this.updateUIState({
        ...currentState,
        isBuildInProgress: false,
        latestFiles: [...currentState.latestFiles, ...(progress.files || [])]
      });
    }
  }

  /**
   * Handle DEPLOY progress events
   */
  private handleDeployProgress(
    progress: CodeRegenerationProgress, 
    currentState: CodeRegenerationUIState
  ): void {
    if (progress.status === 'COMPLETED') {

      // Switch to preview tab
      this.switchToPreviewTabSubject.next();
      
      // Refresh preview with new URL if available
      if (progress.url) {
        this.refreshPreviewSubject.next(progress.url);
      }
      
      // Update state
      this.updateUIState({
        ...currentState,
        isDeployInProgress: false,
        shouldSwitchToPreviewTab: true,
        shouldRefreshPreview: true
      });
      
      // Reset all loading states after deployment
      this.resetLoadingStates();
    }
  }

  /**
   * Update UI state
   */
  private updateUIState(newState: CodeRegenerationUIState): void {
    this.uiStateSubject.next(newState);

  }

  /**
   * Reset all loading states after regeneration completion
   */
  private resetLoadingStates(): void {
    const currentState = this.uiStateSubject.value;
    this.updateUIState({
      ...currentState,
      isCodeGenerationLoading: false,
      isBuildInProgress: false,
      isDeployInProgress: false,
      shouldShowLoadingScreen: false,
      shouldSwitchToCodeTab: false,
      shouldSwitchToPreviewTab: false,
      shouldRefreshPreview: false
    });

  }

  /**
   * Get current UI state
   */
  getCurrentUIState(): CodeRegenerationUIState {
    return this.uiStateSubject.value;
  }

  /**
   * Reset service state
   */
  reset(): void {
    this.updateUIState({
      isCodeGenerationLoading: false,
      isBuildInProgress: false,
      isDeployInProgress: false,
      shouldShowLoadingScreen: false,
      shouldSwitchToCodeTab: false,
      shouldSwitchToPreviewTab: false,
      shouldRefreshPreview: false,
      latestFiles: [],
      currentVersion: 1
    });

  }
}
