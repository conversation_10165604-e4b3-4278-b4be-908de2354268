import { Injectable, signal, computed } from '@angular/core';
import { Observable, BehaviorSubject } from 'rxjs';
import { createLogger } from '../../utils/logger';

export interface CacheConfiguration {
  // Multi-level cache settings
  multiLevelCache: {
    maxMemorySize: number;
    maxPersistentSize: number;
    defaultTTL: number;
    enablePersistence: boolean;
    enableCompression: boolean;
    storagePrefix: string;
  };

  // File content cache settings
  fileContentCache: {
    defaultTTL: number;
    maxFileSize: number;
    enableContentHashing: boolean;
    enableLanguageDetection: boolean;
    compressionThreshold: number;
    enablePreloading: boolean;
    maxPreloadFiles: number;
  };

  // Monaco model cache settings
  monacoModelCache: {
    maxModels: number;
    defaultTTL: number;
    enableContentValidation: boolean;
    enableModelReuse: boolean;
    disposeUnusedAfter: number;
    enableMetrics: boolean;
  };

  // HTTP cache settings
  httpCache: {
    defaultMaxAge: number;
    maxCacheSize: number;
    enableCaching: boolean;
    cacheableUrls: string[];
    uncacheableUrls: string[];
  };

  // Performance settings
  performance: {
    enableCacheWarming: boolean;
    enableMetricsCollection: boolean;
    metricsUpdateInterval: number;
    enablePerformanceLogging: boolean;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
  };

  // Development settings
  development: {
    enableCacheDebugging: boolean;
    enableCacheInspection: boolean;
    enablePerformanceAnalysis: boolean;
    clearCacheOnReload: boolean;
  };
}

export interface CacheProfile {
  name: string;
  description: string;
  config: Partial<CacheConfiguration>;
}

@Injectable({
  providedIn: 'root'
})
export class CacheConfigService {

  // Default configuration
  private readonly defaultConfig: CacheConfiguration = {
    multiLevelCache: {
      maxMemorySize: 100,
      maxPersistentSize: 500,
      defaultTTL: 30 * 60 * 1000, // 30 minutes
      enablePersistence: true,
      enableCompression: false,
      storagePrefix: 'es_cache_'
    },
    fileContentCache: {
      defaultTTL: 60 * 60 * 1000, // 1 hour
      maxFileSize: 1024 * 1024, // 1MB
      enableContentHashing: true,
      enableLanguageDetection: true,
      compressionThreshold: 10 * 1024, // 10KB
      enablePreloading: true,
      maxPreloadFiles: 10
    },
    monacoModelCache: {
      maxModels: 50,
      defaultTTL: 2 * 60 * 60 * 1000, // 2 hours
      enableContentValidation: true,
      enableModelReuse: true,
      disposeUnusedAfter: 30 * 60 * 1000, // 30 minutes
      enableMetrics: true
    },
    httpCache: {
      defaultMaxAge: 5 * 60 * 1000, // 5 minutes
      maxCacheSize: 100,
      enableCaching: true,
      cacheableUrls: ['/api/'],
      uncacheableUrls: ['/api/auth/', '/api/logout']
    },
    performance: {
      enableCacheWarming: true,
      enableMetricsCollection: true,
      metricsUpdateInterval: 30000, // 30 seconds
      enablePerformanceLogging: true,
      logLevel: 'info'
    },
    development: {
      enableCacheDebugging: false,
      enableCacheInspection: false,
      enablePerformanceAnalysis: false,
      clearCacheOnReload: false
    }
  };

  // // Predefined cache profiles
  // private readonly cacheProfiles: CacheProfile[] = [
  //   {
  //     name: 'development',
  //     description: 'Optimized for development with debugging enabled',
  //     config: {
  //       development: {
  //         enableCacheDebugging: true,
  //         enableCacheInspection: true,
  //         enablePerformanceAnalysis: true,
  //         clearCacheOnReload: true
  //       },
  //       performance: {
  //         logLevel: 'debug'
  //       },
  //       multiLevelCache: {
  //         defaultTTL: 5 * 60 * 1000 // 5 minutes for faster development
  //       }
  //     }
  //   },
  //   {
  //     name: 'production',
  //     description: 'Optimized for production performance',
  //     config: {
  //       development: {
  //         enableCacheDebugging: false,
  //         enableCacheInspection: false,
  //         enablePerformanceAnalysis: false,
  //         clearCacheOnReload: false
  //       },
  //       performance: {
  //         logLevel: 'warn'
  //       },
  //       multiLevelCache: {
  //         maxMemorySize: 200,
  //         maxPersistentSize: 1000,
  //         defaultTTL: 60 * 60 * 1000 // 1 hour
  //       },
  //       fileContentCache: {
  //         defaultTTL: 4 * 60 * 60 * 1000, // 4 hours
  //         maxFileSize: 2 * 1024 * 1024 // 2MB
  //       }
  //     }
  //   },
  //   {
  //     name: 'memory-optimized',
  //     description: 'Optimized for low memory usage',
  //     config: {
  //       multiLevelCache: {
  //         maxMemorySize: 50,
  //         maxPersistentSize: 200,
  //         enableCompression: true
  //       },
  //       fileContentCache: {
  //         maxFileSize: 512 * 1024, // 512KB
  //         compressionThreshold: 5 * 1024, // 5KB
  //         maxPreloadFiles: 5
  //       },
  //       monacoModelCache: {
  //         maxModels: 25,
  //         disposeUnusedAfter: 15 * 60 * 1000 // 15 minutes
  //       }
  //     }
  //   },
  //   {
  //     name: 'performance-optimized',
  //     description: 'Optimized for maximum performance',
  //     config: {
  //       multiLevelCache: {
  //         maxMemorySize: 300,
  //         maxPersistentSize: 1500
  //       },
  //       fileContentCache: {
  //         maxFileSize: 5 * 1024 * 1024, // 5MB
  //         maxPreloadFiles: 20
  //       },
  //       monacoModelCache: {
  //         maxModels: 100,
  //         disposeUnusedAfter: 60 * 60 * 1000 // 1 hour
  //       },
  //       performance: {
  //         enableCacheWarming: true,
  //         metricsUpdateInterval: 10000 // 10 seconds
  //       }
  //     }
  //   }
  // ];

  // Current configuration state
  private readonly configSignal = signal<CacheConfiguration>(this.defaultConfig);
  private readonly config$ = new BehaviorSubject<CacheConfiguration>(this.defaultConfig);

  // Computed signals for specific cache configurations
  readonly multiLevelCacheConfig = computed(() => this.configSignal().multiLevelCache);
  readonly fileContentCacheConfig = computed(() => this.configSignal().fileContentCache);
  readonly monacoModelCacheConfig = computed(() => this.configSignal().monacoModelCache);
  readonly httpCacheConfig = computed(() => this.configSignal().httpCache);
  readonly performanceConfig = computed(() => this.configSignal().performance);
  readonly developmentConfig = computed(() => this.configSignal().development);

  constructor() {
    this.loadConfiguration();

  }

  /**
   * Get current configuration as Observable
   */
  getConfig(): Observable<CacheConfiguration> {
    return this.config$.asObservable();
  }

  /**
   * Get current configuration as Signal
   */
  getConfigSignal() {
    return this.configSignal.asReadonly();
  }

  /**
   * Update configuration
   */
  updateConfig(partialConfig: Partial<CacheConfiguration>): void {
    const currentConfig = this.configSignal();
    const newConfig = this.mergeConfigs(currentConfig, partialConfig);

    this.configSignal.set(newConfig);
    this.config$.next(newConfig);
    this.saveConfiguration(newConfig);

  }

  /**
   * Reset to default configuration
   */
  resetToDefault(): void {
    this.configSignal.set(this.defaultConfig);
    this.config$.next(this.defaultConfig);
    this.saveConfiguration(this.defaultConfig);

  }

  /**
   * Export current configuration
   */
  exportConfig(): string {
    return JSON.stringify({
      config: this.configSignal(),
      exportedAt: new Date().toISOString(),
      version: '1.0'
    }, null, 2);
  }

  /**
   * Import configuration from JSON
   */
  importConfig(configJson: string): boolean {
    try {
      const imported = JSON.parse(configJson);
      if (imported.config) {
        this.updateConfig(imported.config);

        return true;
      }
    } catch (error) {

    }
    return false;
  }

  /**
   * Get configuration for specific cache type
   */
  getCacheConfig<T extends keyof CacheConfiguration>(cacheType: T): CacheConfiguration[T] {
    return this.configSignal()[cacheType];
  }

  /**
   * Update configuration for specific cache type
   */
  updateCacheConfig<T extends keyof CacheConfiguration>(
    cacheType: T,
    config: Partial<CacheConfiguration[T]>
  ): void {
    const currentConfig = this.configSignal();
    const updatedConfig = {
      ...currentConfig,
      [cacheType]: { ...currentConfig[cacheType], ...config }
    };

    this.configSignal.set(updatedConfig);
    this.config$.next(updatedConfig);
    this.saveConfiguration(updatedConfig);
  }

  // Private methods

  private loadConfiguration(): void {
    try {
      const stored = sessionStorage.getItem('es_cache_config');
      if (stored) {
        const config = JSON.parse(stored);
        const mergedConfig = this.mergeConfigs(this.defaultConfig, config);
        this.configSignal.set(mergedConfig);
        this.config$.next(mergedConfig);
      }
    } catch (error) {

    }
  }

  private saveConfiguration(config: CacheConfiguration): void {
    try {
      sessionStorage.setItem('es_cache_config', JSON.stringify(config));
    } catch (error) {

    }
  }

  private mergeConfigs(base: CacheConfiguration, override: Partial<CacheConfiguration>): CacheConfiguration {
    const result = { ...base };

    for (const key in override) {
      if (override.hasOwnProperty(key)) {
        const typedKey = key as keyof CacheConfiguration;
        if (typeof override[typedKey] === 'object' && override[typedKey] !== null) {
          result[typedKey] = { ...base[typedKey], ...override[typedKey] } as any;
        } else {
          result[typedKey] = override[typedKey] as any;
        }
      }
    }

    return result;
  }
}
