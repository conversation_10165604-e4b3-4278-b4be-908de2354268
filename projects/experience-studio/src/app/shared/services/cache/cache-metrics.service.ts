import { Injectable, inject, signal, computed } from '@angular/core';
import { Observable, BehaviorSubject, combineLatest, interval } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { MultiLevelCacheService } from './multi-level-cache.service';
import { FileContentCacheService } from './file-content-cache.service';
import { MonacoModelCacheService } from './monaco-model-cache.service';
import { createLogger } from '../../utils/logger';

export interface CachePerformanceMetrics {
  // Overall metrics
  totalCacheHits: number;
  totalCacheMisses: number;
  overallHitRatio: number;
  totalMemoryUsage: number;
  
  // Multi-level cache metrics
  multiLevelCache: {
    memoryHits: number;
    memoryMisses: number;
    persistentHits: number;
    persistentMisses: number;
    memorySize: number;
    persistentSize: number;
    hitRatio: number;
    averageAccessTime: number;
  };
  
  // File content cache metrics
  fileContentCache: {
    totalFiles: number;
    totalSize: number;
    averageFileSize: number;
    languageDistribution: Record<string, number>;
  };
  
  // Monaco model cache metrics
  monacoModelCache: {
    totalModels: number;
    activeModels: number;
    disposedModels: number;
    cacheHits: number;
    cacheMisses: number;
    modelCreations: number;
    modelDisposals: number;
    hitRatio: number;
    memoryUsage: number;
  };
  
  // Performance improvements
  performanceGains: {
    estimatedTimesSaved: number;
    estimatedNetworkRequestsAvoided: number;
    estimatedBandwidthSaved: number; // in bytes
  };
  
  // Timestamps
  lastUpdated: number;
  uptime: number;
}

export interface CacheHealthStatus {
  status: 'healthy' | 'warning' | 'critical';
  issues: string[];
  recommendations: string[];
  score: number; // 0-100
}

@Injectable({
  providedIn: 'root'
})
export class CacheMetricsService {

  private readonly multiLevelCache = inject(MultiLevelCacheService);
  private readonly fileContentCache = inject(FileContentCacheService);
  private readonly monacoModelCache = inject(MonacoModelCacheService);

  // Angular Signals for reactive metrics
  private readonly metricsSignal = signal<CachePerformanceMetrics | null>(null);
  private readonly healthSignal = signal<CacheHealthStatus>({
    status: 'healthy',
    issues: [],
    recommendations: [],
    score: 100
  });

  // Computed signals for derived metrics
  readonly overallHitRatio = computed(() => {
    const metrics = this.metricsSignal();
    return metrics?.overallHitRatio || 0;
  });

  readonly totalMemoryUsage = computed(() => {
    const metrics = this.metricsSignal();
    return metrics?.totalMemoryUsage || 0;
  });

  readonly cacheHealth = computed(() => this.healthSignal());

  // BehaviorSubject for Observable compatibility
  private readonly metrics$ = new BehaviorSubject<CachePerformanceMetrics | null>(null);
  private readonly startTime = Date.now();

  constructor() {
    this.initializeMetricsCollection();
  }

  /**
   * Get current cache metrics as Observable
   */
  getMetrics(): Observable<CachePerformanceMetrics> {
    return this.metrics$.asObservable().pipe(
      map(metrics => metrics || this.getEmptyMetrics())
    );
  }

  /**
   * Get current cache metrics as Signal
   */
  getMetricsSignal() {
    return this.metricsSignal.asReadonly();
  }

  /**
   * Get cache health status
   */
  getCacheHealth(): Observable<CacheHealthStatus> {
    return this.getMetrics().pipe(
      map(metrics => this.calculateHealthStatus(metrics))
    );
  }

  /**
   * Get cache health as Signal
   */
  getCacheHealthSignal() {
    return this.healthSignal.asReadonly();
  }

  /**
   * Reset all metrics
   */
  resetMetrics(): void {

    this.updateMetrics();
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics(): string {
    const metrics = this.metricsSignal();
    if (!metrics) {
      return JSON.stringify(this.getEmptyMetrics(), null, 2);
    }
    
    return JSON.stringify({
      ...metrics,
      exportedAt: new Date().toISOString(),
      version: '1.0'
    }, null, 2);
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): Observable<{
    hitRatio: number;
    memoryUsage: string;
    timesSaved: number;
    networkRequestsAvoided: number;
    status: string;
  }> {
    return this.getMetrics().pipe(
      map(metrics => ({
        hitRatio: Math.round(metrics.overallHitRatio * 100),
        memoryUsage: this.formatBytes(metrics.totalMemoryUsage),
        timesSaved: metrics.performanceGains.estimatedTimesSaved,
        networkRequestsAvoided: metrics.performanceGains.estimatedNetworkRequestsAvoided,
        status: this.calculateHealthStatus(metrics).status
      }))
    );
  }

  // Private methods

  private initializeMetricsCollection(): void {
    // Update metrics every 30 seconds
    interval(30000).pipe(
      startWith(0)
    ).subscribe(() => {
      this.updateMetrics();
    });

  }

  private updateMetrics(): void {
    combineLatest([
      this.multiLevelCache.getMetrics(),
      this.fileContentCache.getFilesCacheStats(),
      this.monacoModelCache.getMetrics()
    ]).subscribe(([multiLevel, fileContent, monacoModel]) => {
      const metrics: CachePerformanceMetrics = {
        // Overall metrics
        totalCacheHits: multiLevel.memoryHits + multiLevel.persistentHits + monacoModel.cacheHits,
        totalCacheMisses: multiLevel.memoryMisses + multiLevel.persistentMisses + monacoModel.cacheMisses,
        overallHitRatio: this.calculateOverallHitRatio(multiLevel, monacoModel),
        totalMemoryUsage: multiLevel.memorySize + multiLevel.persistentSize + monacoModel.memoryUsage,
        
        // Multi-level cache metrics
        multiLevelCache: multiLevel,
        
        // File content cache metrics
        fileContentCache: fileContent,
        
        // Monaco model cache metrics
        monacoModelCache: monacoModel,
        
        // Performance improvements
        performanceGains: this.calculatePerformanceGains(multiLevel, monacoModel),
        
        // Timestamps
        lastUpdated: Date.now(),
        uptime: Date.now() - this.startTime
      };

      // Update signals
      this.metricsSignal.set(metrics);
      this.healthSignal.set(this.calculateHealthStatus(metrics));
      
      // Update BehaviorSubject
      this.metrics$.next(metrics);
    });
  }

  private calculateOverallHitRatio(multiLevel: any, monacoModel: any): number {
    const totalHits = multiLevel.memoryHits + multiLevel.persistentHits + monacoModel.cacheHits;
    const totalMisses = multiLevel.memoryMisses + multiLevel.persistentMisses + monacoModel.cacheMisses;
    const totalRequests = totalHits + totalMisses;
    
    return totalRequests > 0 ? totalHits / totalRequests : 0;
  }

  private calculatePerformanceGains(multiLevel: any, monacoModel: any): {
    estimatedTimesSaved: number;
    estimatedNetworkRequestsAvoided: number;
    estimatedBandwidthSaved: number;
  } {
    const totalHits = multiLevel.memoryHits + multiLevel.persistentHits + monacoModel.cacheHits;
    
    return {
      estimatedTimesSaved: totalHits * 0.1, // Assume 100ms saved per cache hit
      estimatedNetworkRequestsAvoided: multiLevel.memoryHits + multiLevel.persistentHits,
      estimatedBandwidthSaved: totalHits * 1024 // Assume 1KB saved per hit
    };
  }

  private calculateHealthStatus(metrics: CachePerformanceMetrics): CacheHealthStatus {
    const issues: string[] = [];
    const recommendations: string[] = [];
    let score = 100;

    // Check hit ratio
    if (metrics.overallHitRatio < 0.3) {
      issues.push('Low cache hit ratio');
      recommendations.push('Consider warming cache with frequently accessed files');
      score -= 30;
    } else if (metrics.overallHitRatio < 0.6) {
      issues.push('Moderate cache hit ratio');
      recommendations.push('Review cache TTL settings');
      score -= 15;
    }

    // Check memory usage
    const memoryUsageMB = metrics.totalMemoryUsage / (1024 * 1024);
    if (memoryUsageMB > 100) {
      issues.push('High memory usage');
      recommendations.push('Consider reducing cache size limits');
      score -= 20;
    }

    // Check Monaco model disposal rate
    const monacoDisposalRate = metrics.monacoModelCache.modelDisposals / 
      (metrics.monacoModelCache.modelCreations || 1);
    if (monacoDisposalRate > 0.8) {
      issues.push('High Monaco model disposal rate');
      recommendations.push('Increase Monaco model cache TTL');
      score -= 15;
    }

    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (score < 50) {
      status = 'critical';
    } else if (score < 80) {
      status = 'warning';
    }

    return {
      status,
      issues,
      recommendations,
      score: Math.max(0, score)
    };
  }

  private getEmptyMetrics(): CachePerformanceMetrics {
    return {
      totalCacheHits: 0,
      totalCacheMisses: 0,
      overallHitRatio: 0,
      totalMemoryUsage: 0,
      multiLevelCache: {
        memoryHits: 0,
        memoryMisses: 0,
        persistentHits: 0,
        persistentMisses: 0,
        memorySize: 0,
        persistentSize: 0,
        hitRatio: 0,
        averageAccessTime: 0
      },
      fileContentCache: {
        totalFiles: 0,
        totalSize: 0,
        averageFileSize: 0,
        languageDistribution: {}
      },
      monacoModelCache: {
        totalModels: 0,
        activeModels: 0,
        disposedModels: 0,
        cacheHits: 0,
        cacheMisses: 0,
        modelCreations: 0,
        modelDisposals: 0,
        hitRatio: 0,
        memoryUsage: 0
      },
      performanceGains: {
        estimatedTimesSaved: 0,
        estimatedNetworkRequestsAvoided: 0,
        estimatedBandwidthSaved: 0
      },
      lastUpdated: Date.now(),
      uptime: 0
    };
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
