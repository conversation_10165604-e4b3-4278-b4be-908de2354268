import { Injectable } from '@angular/core';
import { HttpRequest, HttpResponse } from '@angular/common/http';
import { createLogger } from '../../utils/logger';

export interface CacheEntry {
  url: string;
  response: HttpResponse<any>;
  timestamp: number;
  expiresAt: number;
}

@Injectable({
  providedIn: 'root'
})
export class HttpCacheService {
  private cache = new Map<string, CacheEntry>();
  private readonly DEFAULT_MAX_AGE = 5 * 60 * 1000; // 5 minutes in milliseconds
  private readonly MAX_CACHE_SIZE = 100; // Maximum number of entries to store

  /**
   * Get a cached response for a given request
   * @param request The HTTP request
   * @returns The cached response or null if not found or expired
   */
  get(request: HttpRequest<any>): HttpResponse<any> | null {
    const url = request.urlWithParams;
    const cachedEntry = this.cache.get(url);

    if (!cachedEntry) {

      return null;
    }

    const now = Date.now();
    if (cachedEntry.expiresAt < now) {
      // Cache entry has expired, remove it

      this.cache.delete(url);
      return null;
    }

    return cachedEntry.response;
  }

  /**
   * Store a response in the cache
   * @param request The HTTP request
   * @param response The HTTP response
   * @param maxAge Maximum age of the cache entry in milliseconds
   */
  put(request: HttpRequest<any>, response: HttpResponse<any>, maxAge?: number): void {
    // Only cache successful GET requests
    if (request.method !== 'GET') {
      return;
    }

    const url = request.urlWithParams;
    const expiresAt = Date.now() + (maxAge || this.DEFAULT_MAX_AGE);

    const entry: CacheEntry = {
      url,
      response,
      timestamp: Date.now(),
      expiresAt
    };

    this.cache.set(url, entry);

    // Ensure cache doesn't grow too large
    this.pruneCache();
  }

  /**
   * Clear the entire cache or a specific entry
   * @param url Optional URL to clear from cache
   */
  clear(url?: string): void {
    if (url) {

      this.cache.delete(url);
    } else {

      this.cache.clear();
    }
  }

  /**
   * Get the current size of the cache
   */
  get size(): number {
    return this.cache.size;
  }

  /**
   * Remove the oldest entries if the cache exceeds the maximum size
   */
  private pruneCache(): void {
    if (this.cache.size <= this.MAX_CACHE_SIZE) {
      return;
    }

    // Convert to array to sort by timestamp
    const entries = Array.from(this.cache.entries());
    
    // Sort by timestamp (oldest first)
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
    
    // Remove oldest entries until we're under the limit
    const entriesToRemove = entries.slice(0, entries.length - this.MAX_CACHE_SIZE);
    
    for (const [key] of entriesToRemove) {
      this.cache.delete(key);
    }

  }
}
