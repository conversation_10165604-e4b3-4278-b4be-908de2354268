import { Injectable, inject } from '@angular/core';
import { Router } from '@angular/router';
import { ToastService } from './toast.service';
import { ProjectLoadingService } from './project-loading.service';
import { firstValueFrom } from 'rxjs';

/**
 * Service to handle project navigation with Angular best practices
 * Provides centralized navigation logic for recent projects
 */
@Injectable({
  providedIn: 'root'
})
export class ProjectNavigationService {
  private readonly router = inject(Router);
  private readonly toastService = inject(ToastService);
  private readonly projectLoadingService = inject(ProjectLoadingService);

  /**
   * Navigate to project code-preview with proper route structure
   * Determines the correct route based on project generation type
   * @param projectId The project ID to navigate to
   * @param projectName Optional project name for toast message
   */
  async navigateToProject(projectId: string, projectName?: string): Promise<void> {
    if (!projectId) {
      this.toastService.error('Invalid project ID');
      return;
    }

    // Show loading toast
    if (projectName) {
      this.toastService.info(`Loading project "${projectName}"...`);
    } else {
      this.toastService.info('Loading project...');
    }

    try {
      // Load project data to determine generation type
      const projectData = await firstValueFrom(this.projectLoadingService.loadProjectData(projectId));

      // Determine the correct route based on generation type
      let targetRoute: string[];
      if (projectData.project_settings.generation_type === 'wireframe_generation') {
        targetRoute = ['/generate-ui-design/projects', projectId];

      } else {
        targetRoute = ['/generate-application/projects', projectId];

      }

      // Navigate to the correct route with project loading enabled
      const success = await this.router.navigate(targetRoute);
      if (!success) {
        this.toastService.error('Failed to navigate to project');
      }
    } catch (error) {

      this.toastService.error('Failed to load project data');

      // Fallback to Generate Application route
      try {
        await this.router.navigate(['/generate-application/projects', projectId]);
      } catch (fallbackError) {

        this.toastService.error('Failed to navigate to project');
      }
    }
  }

  /**
   * Check if the current route is a project loading route
   * @returns boolean indicating if currently on project loading route
   */
  isOnProjectRoute(): boolean {
    return this.router.url.includes('/projects/');
  }

  /**
   * Extract project ID from current route
   * @returns project ID if on project route, null otherwise
   */
  getCurrentProjectId(): string | null {
    const url = this.router.url;
    const match = url.match(/\/projects\/([^\/]+)/);
    return match ? match[1] : null;
  }
}
