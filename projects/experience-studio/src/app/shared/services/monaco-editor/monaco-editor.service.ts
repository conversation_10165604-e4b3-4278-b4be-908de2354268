import { Injectable, NgZone, inject } from '@angular/core';
import { BehaviorSubject, Observable, from, of, throwError } from 'rxjs';
import { catchError, map, shareReplay, tap, switchMap, timeout } from 'rxjs/operators';
import { createLogger } from '../../utils/logger';
import { MonacoModelCacheService } from '../cache/monaco-model-cache.service';
import { FileContentCacheService } from '../cache/file-content-cache.service';

/**
 * Service for managing Monaco Editor instances and configuration
 */
@Injectable({
  providedIn: 'root'
})
export class MonacoEditorService {
  private monaco: typeof import('monaco-editor') | null = null;
  private monacoLoading = false;
  private monacoLoaded$ = new BehaviorSubject<boolean>(false);
  private monacoLoadingState$ = new BehaviorSubject<'idle' | 'loading' | 'loaded' | 'error'>('idle');
  private monacoError$ = new BehaviorSubject<string | null>(null);
  private editorInstances: Map<string, any> = new Map();
  private editorModels: Map<string, any> = new Map();

  // Inject cache services
  private readonly monacoModelCache = inject(MonacoModelCacheService);
  private readonly fileContentCache = inject(FileContentCacheService);

  constructor(private ngZone: NgZone) {
    // Configure Monaco environment when service is created
    this.configureMonacoEnvironment();
  }

  /**
   * Get the current Monaco loading state
   */
  get loadingState$(): Observable<'idle' | 'loading' | 'loaded' | 'error'> {
    return this.monacoLoadingState$.asObservable();
  }

  /**
   * Get the current Monaco error state
   */
  get error$(): Observable<string | null> {
    return this.monacoError$.asObservable();
  }

  /**
   * Check if Monaco is currently loading
   */
  get isLoading$(): Observable<boolean> {
    return this.monacoLoadingState$.pipe(
      map(state => state === 'loading')
    );
  }

  /**
   * Check if Monaco is loaded and ready
   */
  get isLoaded$(): Observable<boolean> {
    return this.monacoLoadingState$.pipe(
      map(state => state === 'loaded')
    );
  }

  /**
   * Configure the Monaco environment for web workers with production support
   */
  private configureMonacoEnvironment(): void {
    // Only configure once
    if ((window as any).MonacoEnvironment) {
      return;
    }

    // Configure Monaco environment to load workers on demand
    (window as any).MonacoEnvironment = {
      getWorkerUrl: (_moduleId: string, label: string) => {
        const workerMap: { [key: string]: string } = {
          typescript: 'ts.worker.js',
          javascript: 'ts.worker.js',
          html: 'html.worker.js',
          css: 'css.worker.js',
          scss: 'css.worker.js',
          less: 'css.worker.js',
          json: 'json.worker.js'
        };

        // Return the appropriate worker URL based on the language
        return workerMap[label] || 'editor.worker.js';
      }
    };

    // Handle Monaco Editor font loading gracefully
    this.handleMonacoFontLoading();
  }

  /**
   * Handle Monaco Editor font loading with fallbacks
   */
  private handleMonacoFontLoading(): void {
    // Check if codicon font is available
    if (document.fonts && document.fonts.check) {
      try {
        // Check if codicon font is loaded
        const isCodiconLoaded = document.fonts.check('16px codicon');

        if (!isCodiconLoaded) {

          // Add fallback CSS for codicon font
          const style = document.createElement('style');
          style.textContent = `
            .codicon {
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace !important;
            }
            .monaco-icon-label::before {
              content: '□' !important; /* Fallback symbol */
            }
          `;
          document.head.appendChild(style);
        }
      } catch (error) {

      }
    }
  }

  /**
   * Lazy load Monaco Editor with enhanced loading states
   * @returns Observable that resolves when Monaco is loaded
   */
  public loadMonaco(): Observable<any> { // Temporarily disabled Monaco Editor for Docker build
    // If Monaco is already loaded, return it immediately
    if (this.monaco) {
      this.monacoLoadingState$.next('loaded');
      return of(this.monaco);
    }

    // If Monaco is currently loading, return the loading observable
    if (this.monacoLoading) {
      return this.monacoLoaded$.pipe(
        map(() => this.monaco!),
        shareReplay(1)
      );
    }

    // Start loading Monaco
    this.monacoLoading = true;
    this.monacoLoadingState$.next('loading');
    this.monacoError$.next(null);

    // Import Monaco dynamically with performance tracking
    const startTime = performance.now();

    // Import Monaco Editor dynamically
    return from(import('monaco-editor')).pipe(
      timeout(30000), // 30 second timeout for production
      tap(monaco => {
        const loadTime = performance.now() - startTime;

        this.monaco = monaco;
        this.monacoLoaded$.next(true);
        this.monacoLoading = false;
        this.monacoLoadingState$.next('loaded');
        this.defineCustomThemes(monaco);
      }),
      catchError(error => {
        const loadTime = performance.now() - startTime;
        let errorMessage = `Failed to load Monaco Editor after ${loadTime.toFixed(2)}ms`;

        // Provide more specific error messages for common issues
        if (error.name === 'TimeoutError') {
          errorMessage += ': Loading timeout - check network connection';
        } else if (error.message.includes('Loading chunk')) {
          errorMessage += ': Chunk loading failed - try refreshing the page';
        } else if (error.message.includes('Loading CSS chunk')) {
          errorMessage += ': CSS chunk loading failed - check asset paths';
        } else {
          errorMessage += `: ${error.message}`;
        }

        this.monacoLoading = false;
        this.monacoLoaded$.next(false);
        this.monacoLoadingState$.next('error');
        this.monacoError$.next(errorMessage);

        // Return a more user-friendly error
        return throwError(() => new Error(errorMessage));
      }),
      shareReplay(1)
    );
  }

  /**
   * Create a Monaco Editor instance
   * @param domElement The DOM element to create the editor in
   * @param options Editor options
   * @param id Optional ID for the editor instance
   * @returns Observable that resolves with the editor instance
   */
  public createEditor(
    domElement: HTMLElement,
    options: any = {},
    id: string = 'default'
  ): Observable<any> {
    return this.loadMonaco().pipe(
      map(monaco => {
        // Run editor creation inside NgZone to ensure change detection works properly
        return this.ngZone.run(() => {
          // Create the editor with default options merged with provided options
          const defaultOptions = {
            automaticLayout: true,
            theme: 'dark-theme',
            language: 'plaintext',
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            fontSize: 14,
            tabSize: 2,
            lineHeight: 22
          };

          const editor = monaco.editor.create(
            domElement,
            { ...defaultOptions, ...options }
          );

          // Store the editor instance for later retrieval or disposal
          this.editorInstances.set(id, editor);

          return editor;
        });
      })
    );
  }

  /**
   * Get an existing editor instance by ID
   * @param id The ID of the editor instance
   * @returns The editor instance or null if not found
   */
  public getEditor(id: string = 'default'): any {
    return this.editorInstances.get(id) || null;
  }

  /**
   * Dispose of an editor instance
   * @param id The ID of the editor instance to dispose
   */
  public disposeEditor(id: string = 'default'): void {
    const editor = this.editorInstances.get(id);
    if (editor) {
      editor.dispose();
      this.editorInstances.delete(id);
    }
  }

  /**

   * Create or get a model for a file with intelligent caching
   * @param content The content of the file
   * @param language The language of the file
   * @param uri Optional URI for the model
   * @returns The model
   */
  public createOrGetModel(content: string, language: string, uri?: string): Observable<any> {
    return this.loadMonaco().pipe(
      switchMap(monaco => {
        // Use Monaco model cache for intelligent model management
        return this.monacoModelCache.getOrCreateModel(monaco, content, language, uri);
      }),
      tap(model => {
        // Store in legacy map for backward compatibility
        if (uri) {
          this.editorModels.set(uri, model);
        }
      })
    );
  }

  /**
   * Update model content with cache invalidation
   * @param uri The URI of the model to update
   * @param content The new content
   * @returns Whether the update was successful
   */
  public updateModelContent(uri: string, content: string): boolean {
    const success = this.monacoModelCache.updateModelContent(uri, content);

    if (success) {

    }

    return success;
  }

  /**
   * Dispose of a model with cache integration
   * @param uri The URI of the model to dispose
   */
  public disposeModel(uri: string): void {
    // Use cache service for disposal
    const success = this.monacoModelCache.disposeModel(uri);

    // Also remove from legacy map for backward compatibility
    if (this.editorModels.has(uri)) {
      this.editorModels.delete(uri);
    }

    if (success) {

    }
  }

  /**
   * Warm cache with multiple models
   * @param models Array of model definitions
   */
  public warmModelCache(models: Array<{
    uri: string;
    content: string;
    language: string;
  }>): Observable<number> {
    return this.loadMonaco().pipe(
      switchMap(monaco => this.monacoModelCache.warmCache(monaco, models))
    );
  }

  /**
   * Get model cache metrics
   */
  public getModelCacheMetrics(): Observable<any> {
    return this.monacoModelCache.getMetrics();
  }

  /**
   * Define custom themes for Monaco Editor
   * @param monaco The Monaco instance
   */
  private defineCustomThemes(monaco: typeof import('monaco-editor')): void {
    // Define light theme
    monaco.editor.defineTheme('light-theme', {
      base: 'vs',
      inherit: true,
      rules: [
        { token: '', foreground: '000000' },
        { token: 'comment', foreground: '008000' },
        { token: 'keyword', foreground: '0000FF' },
        { token: 'string', foreground: 'A31515' },
        { token: 'number', foreground: '098658' },
        { token: 'type', foreground: '267F99' }
      ],
      colors: {
        // Use transparent background
        'editor.background': '#00000000',
        'editor.foreground': '#000000',
        'editor.lineHighlightBackground': '#f5f5f5',
        'editorCursor.foreground': '#000000',
        'editor.selectionBackground': '#add6ff',
        'editor.inactiveSelectionBackground': '#e5ebf1',
        'editorLineNumber.foreground': '#6e7681',
        'editorGutter.background': '#00000000',
        'scrollbarSlider.background': '#64646466',
        'scrollbarSlider.hoverBackground': '#646464B3',
        'scrollbarSlider.activeBackground': '#646464B3',
        // // Minimap settings
        // 'minimap.background': '#00000000',
        // 'minimap.foreground': '#00000080',
        // 'minimap.selectionHighlight': '#add6ff80',
        // Overview ruler settings
        'editorOverviewRuler.background': '#00000000',
        'editorOverviewRuler.border': '#00000000'
      }
    });

    // Define dark theme
    monaco.editor.defineTheme('dark-theme', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: '', foreground: 'D4D4D4' },
        { token: 'comment', foreground: '6A9955' },
        { token: 'keyword', foreground: '569CD6' },
        { token: 'string', foreground: 'CE9178' },
        { token: 'number', foreground: 'B5CEA8' },
        { token: 'type', foreground: '4EC9B0' }
      ],
      colors: {
        // Use transparent background
        'editor.background': '#00000000',
        'editor.foreground': '#D4D4D4',
        'editor.lineHighlightBackground': '#2F3137',
        'editorCursor.foreground': '#FFFFFF',
        'editor.selectionBackground': '#264F78',
        'editor.inactiveSelectionBackground': '#3A3D41',
        'editorLineNumber.foreground': '#858585',
        'editorGutter.background': '#00000000',
        'scrollbarSlider.background': '#79797966',
        'scrollbarSlider.hoverBackground': '#646464B3',
        'scrollbarSlider.activeBackground': '#646464B3',
        // Minimap settings
        // 'minimap.background': '#00000000',
        // 'minimap.foreground': '#FFFFFF80',
        // 'minimap.selectionHighlight': '#264F7880',
        // Overview ruler settings
        'editorOverviewRuler.background': '#00000000',
        'editorOverviewRuler.border': '#00000000'
      }
    });
  }

  /**
   * Set the theme for all editor instances
   * @param theme The theme to set ('light-theme' or 'dark-theme')
   */
  public setTheme(theme: 'light-theme' | 'dark-theme'): void {
    if (!this.monaco) {
      return;
    }

    this.monaco.editor.setTheme(theme);
  }

  /**
   * Get the language for a file based on its extension and filename patterns
   * Enhanced for template files with comprehensive language detection
   * @param fileName The name of the file
   * @returns The language ID
   */
  public getLanguageForFile(fileName: string): string {
    // Handle special filename patterns first
    const lowerFileName = fileName.toLowerCase();

    // Special files by exact name
    const specialFiles: { [key: string]: string } = {
      'dockerfile': 'dockerfile',
      'makefile': 'makefile',
      'rakefile': 'ruby',
      'gemfile': 'ruby',
      'podfile': 'ruby',
      'vagrantfile': 'ruby',
      '.gitignore': 'ignore',
      '.gitattributes': 'ignore',
      '.dockerignore': 'ignore',
      '.eslintrc': 'json',
      '.prettierrc': 'json',
      '.babelrc': 'json',
      'tsconfig.json': 'jsonc',
      'jsconfig.json': 'jsonc',
      'package.json': 'json',
      'composer.json': 'json',
      'bower.json': 'json',
      'manifest.json': 'json',
      '.env': 'properties',
      '.env.local': 'properties',
      '.env.development': 'properties',
      '.env.production': 'properties',
      '.env.example': 'properties'
    };

    if (specialFiles[lowerFileName]) {
      return specialFiles[lowerFileName];
    }

    // Extract extension
    const ext = fileName.split('.').pop()?.toLowerCase();
    if (!ext) {
      return 'plaintext';
    }

    // Comprehensive language mapping for template files
    const languageMap: { [key: string]: string } = {
      // JavaScript/TypeScript family
      'ts': 'typescript',
      'tsx': 'typescript',
      'js': 'javascript',
      'jsx': 'javascript',
      'mjs': 'javascript',
      'cjs': 'javascript',

      // Web technologies
      'html': 'html',
      'htm': 'html',
      'xhtml': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'scss',
      'less': 'less',
      'styl': 'stylus',

      // Data formats
      'json': 'json',
      'jsonc': 'jsonc',
      'json5': 'json',
      'yaml': 'yaml',
      'yml': 'yaml',
      'toml': 'toml',
      'xml': 'xml',
      'svg': 'xml',
      'plist': 'xml',

      // Documentation
      'md': 'markdown',
      'markdown': 'markdown',
      'mdown': 'markdown',
      'mkd': 'markdown',
      'rst': 'restructuredtext',
      'txt': 'plaintext',
      'text': 'plaintext',

      // Configuration files
      'ini': 'ini',
      'cfg': 'ini',
      'conf': 'ini',
      'config': 'ini',
      'properties': 'properties',
      'env': 'properties',

      // Shell scripts
      'sh': 'shell',
      'bash': 'shell',
      'zsh': 'shell',
      'fish': 'shell',
      'ps1': 'powershell',
      'psm1': 'powershell',
      'bat': 'bat',
      'cmd': 'bat',

      // Programming languages
      'py': 'python',
      'pyw': 'python',
      'pyc': 'python',
      'pyo': 'python',
      'pyd': 'python',
      'java': 'java',
      'class': 'java',
      'jar': 'java',
      'c': 'c',
      'h': 'c',
      'cpp': 'cpp',
      'cxx': 'cpp',
      'cc': 'cpp',
      'hpp': 'cpp',
      'hxx': 'cpp',
      'cs': 'csharp',
      'vb': 'vb',
      'fs': 'fsharp',
      'go': 'go',
      'rs': 'rust',
      'rb': 'ruby',
      'rbw': 'ruby',
      'php': 'php',
      'php3': 'php',
      'php4': 'php',
      'php5': 'php',
      'phtml': 'php',
      'swift': 'swift',
      'kt': 'kotlin',
      'kts': 'kotlin',
      'scala': 'scala',
      'clj': 'clojure',
      'cljs': 'clojure',
      'cljc': 'clojure',
      'edn': 'clojure',
      'dart': 'dart',
      'lua': 'lua',
      'r': 'r',
      'R': 'r',
      'sql': 'sql',
      'mysql': 'sql',
      'pgsql': 'sql',
      'plsql': 'sql',

      // Template engines
      'hbs': 'handlebars',
      'handlebars': 'handlebars',
      'mustache': 'handlebars',
      'ejs': 'html',
      'erb': 'erb',
      'haml': 'haml',
      'pug': 'pug',
      'jade': 'pug',
      'twig': 'twig',
      'liquid': 'liquid',

      // Build tools and configs
      'dockerfile': 'dockerfile',
      'makefile': 'makefile',
      'mk': 'makefile',
      'cmake': 'cmake',
      'gradle': 'gradle',
      'groovy': 'groovy',
      'ant': 'xml',
      'maven': 'xml',
      'pom': 'xml'
    };

    return languageMap[ext] || 'plaintext';
  }

  /**
   * Dispose all editor instances and models
   */
  public disposeAll(): void {
    // Dispose all editor instances
    this.editorInstances.forEach(editor => {
      editor.dispose();
    });
    this.editorInstances.clear();

    // Dispose all models
    this.editorModels.forEach(model => {
      model.dispose();
    });
    this.editorModels.clear();
  }
}
