import { Injectable, inject } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { SSEEvent } from './sse.service';
import { NewPollingResponse, MetadataItem, HistoryItem } from '../models/polling-response.interface';
import { createLogger } from '../utils/logger';
import { NewPollingResponseProcessorService } from './new-polling-response-processor.service';

/**
 * SSE Response Processor Service
 * 
 * Processes Server-Sent Events data to match the existing polling response format.
 * Ensures 100% backward compatibility with existing polling-dependent components.
 * 
 * Key Features:
 * - Converts SSE event data to NewPollingResponse format
 * - Maintains identical data structures as polling responses
 * - Reuses existing NewPollingResponseProcessorService for consistency
 * - Handles different SSE event types (update, error, complete)
 * - Provides the same observables as the polling service
 */
@Injectable({
  providedIn: 'root'
})
export class SSEResponseProcessorService {

  private readonly pollingProcessor = inject(NewPollingResponseProcessorService);

  // Mirror the same subjects as PollingService for 100% compatibility
  private readonly statusSubject = new BehaviorSubject<string>('idle');
  private readonly progressSubject = new BehaviorSubject<string>('');
  private readonly progressDescriptionSubject = new BehaviorSubject<string>('');
  private readonly logsSubject = new BehaviorSubject<string[]>([]);
  private readonly artifactDataSubject = new BehaviorSubject<any>(null);
  private readonly isProcessingSubject = new BehaviorSubject<boolean>(false);

  // Public observables matching PollingService interface
  public readonly status$ = this.statusSubject.asObservable();
  public readonly progress$ = this.progressSubject.asObservable();
  public readonly progressDescription$ = this.progressDescriptionSubject.asObservable();
  public readonly logs$ = this.logsSubject.asObservable();
  public readonly artifactData$ = this.artifactDataSubject.asObservable();
  public readonly isProcessing$ = this.isProcessingSubject.asObservable();

  // State tracking
  private lastProcessedEventId: string | null = null;
  private eventCount = 0;
  private lastStatusResponse: NewPollingResponse | null = null;

  constructor() {

  }

  /**
   * Process an SSE event and convert it to polling response format
   * @param sseEvent The incoming SSE event
   */
  processSSEEvent(sseEvent: SSEEvent): void {
    try {
      this.isProcessingSubject.next(true);
      this.eventCount++;

      // Skip duplicate events
      if (sseEvent.id && sseEvent.id === this.lastProcessedEventId) {

        return;
      }

      // Parse the SSE data
      const parsedData = this.parseSSEData(sseEvent.data);
      if (!parsedData) {

        return;
      }

      // Convert to NewPollingResponse format
      const pollingResponse = this.convertToPollingResponse(parsedData, sseEvent);

      // Process using existing polling processor for consistency
      this.processWithPollingProcessor(pollingResponse);

      // Update our own subjects for direct compatibility
      this.updateSubjects(pollingResponse);

      // Track processed event
      this.lastProcessedEventId = sseEvent.id || null;
      this.lastStatusResponse = pollingResponse;

    } catch (error) {

    } finally {
      this.isProcessingSubject.next(false);
    }
  }

  /**
   * Parse SSE event data (JSON string to object)
   */
  private parseSSEData(data: string): any {
    try {
      if (!data || data.trim() === '') {

        return null;
      }

      // Handle different data formats
      let parsedData = JSON.parse(data);

      // Log the parsed structure for debugging

      return parsedData;

    } catch (error) {

      return null;
    }
  }

  /**
   * Convert SSE data to NewPollingResponse format
   */
  private convertToPollingResponse(data: any, sseEvent: SSEEvent): NewPollingResponse {
    // The SSE response should already match the polling response format
    // based on the requirements, but we'll ensure all required fields are present
    
    const pollingResponse: NewPollingResponse = {
      status: data.status || 'IN_PROGRESS',
      progress: data.progress || '',
      log: data.log || '',
      progress_description: data.progress_description || '',
      metadata: this.processMetadata(data.metadata || []),
      history: this.processHistory(data.history || [])
    };

    // Add SSE-specific metadata for tracking
    const sseMetadata: MetadataItem = {
      type: 'artifact',
      data: {
        type: 'json',
        data: {
          source: 'sse',
          eventId: sseEvent.id,
          eventType: sseEvent.event,
          timestamp: new Date().toISOString(),
          eventCount: this.eventCount
        }
      }
    };

    pollingResponse.metadata = [...pollingResponse.metadata, sseMetadata];

    return pollingResponse;
  }

  /**
   * Process metadata array to ensure proper format
   */
  private processMetadata(metadata: any[]): MetadataItem[] {
    if (!Array.isArray(metadata)) {

      return [];
    }

    return metadata.map((item, index) => {
      // Ensure each metadata item has the required structure
      if (typeof item === 'object' && item.type && item.data !== undefined) {
        return item as MetadataItem;
      }

      // Convert non-standard metadata to standard format

      return {
        type: 'artifact',
        data: item
      } as MetadataItem;
    });
  }

  /**
   * Process history array to ensure proper format
   */
  private processHistory(history: any[]): HistoryItem[] {
    if (!Array.isArray(history)) {

      return [];
    }

    return history.map((item, index) => {
      // Ensure each history item has the required structure
      if (this.isValidHistoryItem(item)) {
        return item as HistoryItem;
      }

      // Convert non-standard history to standard format

      return {
        progress: item.progress || '',
        status: item.status || 'IN_PROGRESS',
        log: item.log || '',
        progress_description: item.progress_description || '',
        metadata: this.processMetadata(item.metadata || [])
      } as HistoryItem;
    });
  }

  /**
   * Check if an object is a valid HistoryItem
   */
  private isValidHistoryItem(item: any): boolean {
    return item &&
           typeof item === 'object' &&
           typeof item.progress === 'string' &&
           typeof item.status === 'string' &&
           typeof item.log === 'string' &&
           typeof item.progress_description === 'string' &&
           Array.isArray(item.metadata);
  }

  /**
   * Process the response using the existing polling processor
   * This ensures 100% compatibility with existing logic
   */
  private processWithPollingProcessor(response: NewPollingResponse): void {
    try {
      // Use the existing polling processor to maintain consistency
      this.pollingProcessor.processResponse(response);

    } catch (error) {

    }
  }

  /**
   * Update our own subjects for direct compatibility
   */
  private updateSubjects(response: NewPollingResponse): void {
    try {
      // Update subjects to match PollingService behavior
      this.statusSubject.next(response.status);
      this.progressSubject.next(response.progress);
      this.progressDescriptionSubject.next(response.progress_description);

      // Process logs - extract from current log and history
      const allLogs: string[] = [];
      
      if (response.log && response.log.trim() !== '') {
        allLogs.push(response.log);
      }

      // Add logs from history
      response.history.forEach(historyItem => {
        if (historyItem.log && historyItem.log.trim() !== '') {
          allLogs.push(historyItem.log);
        }
      });

      this.logsSubject.next(allLogs);

      // Process artifact data from metadata
      const artifactData = this.extractArtifactData(response.metadata);
      if (artifactData) {
        this.artifactDataSubject.next(artifactData);
      }

    } catch (error) {

    }
  }

  /**
   * Extract artifact data from metadata
   */
  private extractArtifactData(metadata: MetadataItem[]): any {
    try {
      // Look for artifact metadata
      const artifactMetadata = metadata.find(item => 
        item.type === 'artifact' && 
        item.data && 
        typeof item.data === 'object'
      );

      if (artifactMetadata) {
        return artifactMetadata.data;
      }

      // Look for other relevant data types
      const relevantMetadata = metadata.find(item => 
        ['files', 'ref_code'].includes(item.type)
      );

      return relevantMetadata?.data || null;

    } catch (error) {

      return null;
    }
  }

  /**
   * Get the last processed response
   */
  getLastResponse(): NewPollingResponse | null {
    return this.lastStatusResponse;
  }

  /**
   * Get processing statistics
   */
  getProcessingStats(): {
    eventCount: number;
    lastEventId: string | null;
    isProcessing: boolean;
  } {
    return {
      eventCount: this.eventCount,
      lastEventId: this.lastProcessedEventId,
      isProcessing: this.isProcessingSubject.value
    };
  }

  /**
   * Reset the processor state
   * ENHANCED: Added option to preserve artifact data during reset
   */
  reset(preserveArtifacts: boolean = false): void {

    this.eventCount = 0;
    this.lastProcessedEventId = null;
    this.lastStatusResponse = null;

    // Reset subjects to initial state
    this.statusSubject.next('idle');
    this.progressSubject.next('');
    this.progressDescriptionSubject.next('');
    this.logsSubject.next([]);

    // ENHANCED: Only reset artifact data if not preserving artifacts
    if (!preserveArtifacts) {
      this.artifactDataSubject.next(null);

    } else {

    }

    this.isProcessingSubject.next(false);
  }
}
