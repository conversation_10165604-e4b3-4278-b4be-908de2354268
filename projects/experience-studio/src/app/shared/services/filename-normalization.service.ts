import { Injectable } from '@angular/core';
import { createLogger } from '../utils/logger';

/**
 * Configuration for filename normalization rules
 */
export interface FilenameNormalizationConfig {
  caseSensitive: boolean;
  removeExtensions: string[];
  separators: string[];
  camelCaseHandling: boolean;
  fallbackPrefix: string;
  maxLength: number;
  addPageSuffix: boolean; // NEW: Add "Page" suffix to display names
  pageSuffixText: string; // NEW: Customizable page suffix text
}

/**
 * Result of filename normalization analysis
 */
export interface FilenameNormalizationResult {
  originalFilename: string;
  canonicalKey: string;
  displayName: string;
  variations: string[];
  confidence: number;
  processingSteps: string[];
}

/**
 * Node matching result for regeneration
 */
export interface NodeMatchResult {
  isMatch: boolean;
  matchedNodeId?: string;
  matchedFilename?: string;
  matchType: 'exact' | 'canonical' | 'variation' | 'none';
  confidence: number;
  reason: string;
}

/**
 * Comprehensive filename normalization service for wireframe regeneration
 * Handles various filename formats and ensures proper canvas node creation/updates
 */
@Injectable({
  providedIn: 'root'
})
export class FilenameNormalizationService {

  private readonly defaultConfig: FilenameNormalizationConfig = {
    caseSensitive: false,
    removeExtensions: ['.html', '.htm', '.xhtml', '.shtml'],
    separators: ['_', '-', ' ', '.'],
    camelCaseHandling: true,
    fallbackPrefix: 'Page',
    maxLength: 100,
    addPageSuffix: true, // NEW: Enable "Page" suffix by default
    pageSuffixText: 'Page' // NEW: Default suffix text
  };

  private config: FilenameNormalizationConfig = { ...this.defaultConfig };

  /**
   * Update normalization configuration
   */
  updateConfig(newConfig: Partial<FilenameNormalizationConfig>): void {
    this.config = { ...this.config, ...newConfig };

  }

  /**
   * Normalize filename and generate canonical key for node identification
   */
  normalizeFilename(filename: string): FilenameNormalizationResult {
    if (!filename || typeof filename !== 'string') {
      return this.createFallbackResult(filename);
    }

    const processingSteps: string[] = [];
    let current = filename.trim();
    processingSteps.push(`Input: "${current}"`);

    // Step 1: Remove file extensions
    current = this.removeExtensions(current);
    processingSteps.push(`After extension removal: "${current}"`);

    // Step 2: Handle path separators (remove paths)
    current = this.removePaths(current);
    processingSteps.push(`After path removal: "${current}"`);

    // Step 3: Generate variations for matching
    const variations = this.generateVariations(current);
    processingSteps.push(`Generated ${variations.length} variations`);

    // Step 4: Create canonical key (normalized for comparison)
    const canonicalKey = this.createCanonicalKey(current);
    processingSteps.push(`Canonical key: "${canonicalKey}"`);

    // Step 5: Create display name (human-readable)
    const displayName = this.createDisplayName(current);
    processingSteps.push(`Display name: "${displayName}"`);

    // Step 6: Calculate confidence score
    const confidence = this.calculateConfidence(filename, current);

    const result: FilenameNormalizationResult = {
      originalFilename: filename,
      canonicalKey,
      displayName,
      variations,
      confidence,
      processingSteps
    };

    return result;
  }

  /**
   * Find matching node for a given filename
   */
  findMatchingNode(
    filename: string,
    existingNodes: Array<{ id: string; title: string; displayTitle?: string }>
  ): NodeMatchResult {
    if (!filename || !existingNodes.length) {
      return {
        isMatch: false,
        matchType: 'none',
        confidence: 0,
        reason: 'No filename provided or no existing nodes'
      };
    }

    const normalized = this.normalizeFilename(filename);

    // Try exact match first
    const exactMatch = this.findExactMatch(filename, existingNodes);
    if (exactMatch) {
      return exactMatch;
    }

    // Try canonical key match
    const canonicalMatch = this.findCanonicalMatch(normalized, existingNodes);
    if (canonicalMatch) {
      return canonicalMatch;
    }

    // Try variation match
    const variationMatch = this.findVariationMatch(normalized, existingNodes);
    if (variationMatch) {
      return variationMatch;
    }

    return {
      isMatch: false,
      matchType: 'none',
      confidence: 0,
      reason: 'No matching node found'
    };
  }

  /**
   * Generate all possible filename variations for comprehensive matching
   */
  generateVariations(filename: string): string[] {
    if (!filename) return [];

    const variations = new Set<string>();
    const base = filename.trim();

    // Add original
    variations.add(base);

    // Case variations
    variations.add(base.toLowerCase());
    variations.add(base.toUpperCase());
    variations.add(this.toTitleCase(base));

    // Separator variations
    this.config.separators.forEach(separator => {
      // Replace other separators with current one
      const withSeparator = this.replaceSeparators(base, separator);
      variations.add(withSeparator);
      variations.add(withSeparator.toLowerCase());
      variations.add(this.toTitleCase(withSeparator));
    });

    // CamelCase variations
    if (this.config.camelCaseHandling) {
      variations.add(this.toCamelCase(base));
      variations.add(this.toPascalCase(base));
      variations.add(this.fromCamelCase(base));
    }

    // Remove empty variations
    const result = Array.from(variations).filter(v => v.length > 0);

    return result;
  }

  /**
   * Batch normalize multiple filenames
   */
  batchNormalize(filenames: string[]): FilenameNormalizationResult[] {
    return filenames.map(filename => this.normalizeFilename(filename));
  }

  /**
   * Create canonical key for node identification
   */
  private createCanonicalKey(filename: string): string {
    if (!filename) return '';

    return filename
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '') // Remove all non-alphanumeric characters
      .substring(0, this.config.maxLength);
  }

  /**
   * Create human-readable display name with optional "Page" suffix
   */
  private createDisplayName(filename: string): string {
    if (!filename) return this.config.fallbackPrefix;

    let displayName = filename
      // Replace separators with spaces
      .replace(/[_-]/g, ' ')
      // Split camelCase
      .replace(/([a-z])([A-Z])/g, '$1 $2')
      // Clean up multiple spaces
      .replace(/\s+/g, ' ')
      // Title case
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ')
      .trim();

    // Add "Page" suffix if enabled and not already present
    if (this.config.addPageSuffix && this.config.pageSuffixText) {
      const suffix = this.config.pageSuffixText;
      if (!displayName.toLowerCase().endsWith(suffix.toLowerCase())) {
        displayName = `${displayName} ${suffix}`;
      }
    }

    return displayName;
  }

  /**
   * Remove file extensions
   */
  private removeExtensions(filename: string): string {
    let result = filename;

    for (const ext of this.config.removeExtensions) {
      if (result.toLowerCase().endsWith(ext.toLowerCase())) {
        result = result.substring(0, result.length - ext.length);
        break;
      }
    }

    return result;
  }

  /**
   * Remove path components
   */
  private removePaths(filename: string): string {
    return filename.split('/').pop()?.split('\\').pop() || filename;
  }

  /**
   * Calculate confidence score for normalization
   */
  private calculateConfidence(original: string, processed: string): number {
    if (!original || !processed) return 0;

    const lengthRatio = Math.min(processed.length / original.length, 1);
    const hasValidChars = /[a-zA-Z0-9]/.test(processed);
    const notTooShort = processed.length >= 2;

    let confidence = 0.5; // Base confidence

    if (hasValidChars) confidence += 0.3;
    if (notTooShort) confidence += 0.2;
    confidence += lengthRatio * 0.2;

    return Math.min(confidence, 1);
  }

  /**
   * Create fallback result for invalid input
   */
  private createFallbackResult(filename: string): FilenameNormalizationResult {
    const fallbackName = `${this.config.fallbackPrefix} ${Date.now()}`;

    return {
      originalFilename: filename || '',
      canonicalKey: this.createCanonicalKey(fallbackName),
      displayName: fallbackName,
      variations: [fallbackName],
      confidence: 0,
      processingSteps: ['Invalid input - using fallback']
    };
  }

  /**
   * Find exact filename match
   */
  private findExactMatch(
    filename: string,
    existingNodes: Array<{ id: string; title: string; displayTitle?: string }>
  ): NodeMatchResult | null {
    for (const node of existingNodes) {
      if (node.title === filename || node.displayTitle === filename) {
        return {
          isMatch: true,
          matchedNodeId: node.id,
          matchedFilename: node.title,
          matchType: 'exact',
          confidence: 1.0,
          reason: `Exact match with node title: "${node.title}"`
        };
      }
    }
    return null;
  }

  /**
   * Find canonical key match
   */
  private findCanonicalMatch(
    normalized: FilenameNormalizationResult,
    existingNodes: Array<{ id: string; title: string; displayTitle?: string }>
  ): NodeMatchResult | null {
    for (const node of existingNodes) {
      const nodeNormalized = this.normalizeFilename(node.title);
      if (nodeNormalized.canonicalKey === normalized.canonicalKey) {
        return {
          isMatch: true,
          matchedNodeId: node.id,
          matchedFilename: node.title,
          matchType: 'canonical',
          confidence: 0.9,
          reason: `Canonical key match: "${normalized.canonicalKey}"`
        };
      }
    }
    return null;
  }

  /**
   * Find variation match
   */
  private findVariationMatch(
    normalized: FilenameNormalizationResult,
    existingNodes: Array<{ id: string; title: string; displayTitle?: string }>
  ): NodeMatchResult | null {
    for (const node of existingNodes) {
      const nodeNormalized = this.normalizeFilename(node.title);

      // Check if any variation of the input matches any variation of the node
      for (const inputVariation of normalized.variations) {
        for (const nodeVariation of nodeNormalized.variations) {
          if (inputVariation === nodeVariation) {
            return {
              isMatch: true,
              matchedNodeId: node.id,
              matchedFilename: node.title,
              matchType: 'variation',
              confidence: 0.7,
              reason: `Variation match: "${inputVariation}" matches "${nodeVariation}"`
            };
          }
        }
      }
    }
    return null;
  }

  /**
   * Utility: Convert to title case
   */
  private toTitleCase(str: string): string {
    return str.replace(/\w\S*/g, txt =>
      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
  }

  /**
   * Utility: Replace separators
   */
  private replaceSeparators(str: string, newSeparator: string): string {
    const pattern = new RegExp(`[${this.config.separators.map(s => s === '-' ? '\\-' : s).join('')}]`, 'g');
    return str.replace(pattern, newSeparator);
  }

  /**
   * Utility: Convert to camelCase
   */
  private toCamelCase(str: string): string {
    return str.replace(/[_\-\s]+(.)?/g, (_, char) => char ? char.toUpperCase() : '');
  }

  /**
   * Utility: Convert to PascalCase
   */
  private toPascalCase(str: string): string {
    const camel = this.toCamelCase(str);
    return camel.charAt(0).toUpperCase() + camel.slice(1);
  }

  /**
   * Utility: Convert from camelCase to spaced
   */
  private fromCamelCase(str: string): string {
    return str.replace(/([a-z])([A-Z])/g, '$1 $2');
  }
}
