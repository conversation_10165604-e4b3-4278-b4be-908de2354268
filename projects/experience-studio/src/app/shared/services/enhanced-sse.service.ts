import { Injectable, inject, DestroyRef, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Observable, Subject, BehaviorSubject, EMPTY, timer, Subscription } from 'rxjs';
import { switchMap, catchError, retry, share, takeUntil, tap, filter } from 'rxjs/operators';
import { SSEService, SSEEvent, SSEOptions } from './sse.service';
import { SSEDataProcessorService } from './sse-data-processor.service';
import { NewPollingResponseProcessorService } from './new-polling-response-processor.service';
import { createLogger } from '../utils/logger';
import { environment } from '../../../environments/environment';

/**
 * Enhanced SSE Service for project status monitoring
 *
 * This service extends the base SSE service with specific functionality for
 * project status monitoring, including:
 * - Integration with SSE data processor
 * - Fallback mechanisms to polling
 * - Enhanced error handling and reconnection
 * - Compatibility with existing polling interface
 *
 * Features:
 * - Uses Angular 19+ patterns (inject(), takeUntilDestroyed())
 * - Implements exponential backoff for reconnection
 * - Provides fallback to polling on SSE failure
 * - Maintains same interface as polling service
 * - Comprehensive error handling and logging
 */
@Injectable({
  providedIn: 'root'
})
export class EnhancedSSEService {
  private readonly destroyRef = inject(DestroyRef);
  private readonly sseService = inject(SSEService);
  private readonly sseDataProcessor = inject(SSEDataProcessorService);
  private readonly pollingProcessor = inject(NewPollingResponseProcessorService);

  // Connection state management
  private isConnectedSubject = new BehaviorSubject<boolean>(false);
  private connectionErrorSubject = new Subject<any>();
  private fallbackTriggeredSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  // Current connection details
  private currentJobId: string | null = null;
  private currentProjectId: string | null = null;
  private connectionAttempts = 0;
  private maxConnectionAttempts = 5;
  private fallbackCallback: (() => void) | null = null;

  // ENHANCED: Retry event filtering support
  private retryTimestamp: number | null = null;
  private isRetryFilteringEnabled = false;

  // ENHANCED: Connection management to prevent duplicates
  private activeConnections = new Map<string, Observable<any>>();

  // CRITICAL FIX: Track pending connections to prevent race conditions
  private pendingConnections = new Set<string>();

  // ENHANCED: Track active subscriptions for proper cleanup
  private activeSubscriptions = new Map<string, Subscription>();
  private connectionTimers = new Map<string, number>();

  // ENHANCED: Track UI states associated with connections for cleanup
  private connectionUIStates = new Map<string, {
    isLoading: boolean;
    progressDescription: string;
    lastActivity: number;
  }>();

  // Event ID caching for regeneration checkpointing (Angular 19+ pattern)
  private readonly regenerationSessionEventIds = signal<Map<string, string>>(new Map());

  // Public observables
  public readonly isConnected$ = this.isConnectedSubject.asObservable();
  public readonly connectionError$ = this.connectionErrorSubject.asObservable();
  public readonly fallbackTriggered$ = this.fallbackTriggeredSubject.asObservable();

  // CRITICAL: Expose the same observables as PollingService for 100% compatibility
  // These observables are consumed by components and must match exactly

  // Primary observables from SSE data processor
  public readonly status$ = this.sseDataProcessor.status$;
  public readonly progress$ = this.sseDataProcessor.progress$;
  public readonly progressDescription$ = this.sseDataProcessor.progressDescription$;
  public readonly logs$ = this.sseDataProcessor.logs$;
  public readonly isProcessing$ = this.sseDataProcessor.isProcessing$;
  public readonly error$ = this.sseDataProcessor.error$;

  // Additional observables from the polling processor for complete compatibility
  public readonly currentProgress$ = this.pollingProcessor.currentProgress$;
  public readonly currentStatus$ = this.pollingProcessor.currentStatus$;
  public readonly artifactData$ = this.pollingProcessor.artifactData$;
  public readonly fileList$ = this.pollingProcessor.fileList$;
  public readonly codeFiles$ = this.pollingProcessor.codeFiles$;
  public readonly previewUrl$ = this.pollingProcessor.previewUrl$;
  public readonly projectInfo$ = this.pollingProcessor.projectInfo$;

  constructor() {

    // Setup automatic cleanup
    this.destroyRef.onDestroy(() => {
      this.cleanup();
    });

    // Monitor base SSE service connection state
    this.setupSSEServiceMonitoring();

    // ENHANCED: Log initial header support status
    const polyfillInfo = this.sseService.getPolyfillInfo();

    // Expose testing method to window for debugging (development only)
    if (!environment.production) {
      (window as any).testSSEHeaders = () => this.testSSEHeaders();
      (window as any).getSSEStatus = () => this.getServiceStatus();
      (window as any).testFinalEvent = (isFinal: boolean = true) => this.testFinalEventHandling(isFinal);
      (window as any).getSSEConnections = () => this.getConnectionStatistics();

    }
  }

  /**
   * Start SSE monitoring for project status
   * @param projectId Project ID to monitor
   * @param jobId Job ID to monitor
   * @param options SSE configuration options
   * @param fallbackCallback Callback to trigger fallback to polling
   * @param generationType Optional generation type context for optimization
   */
  startMonitoring(
    projectId: string,
    jobId: string,
    options?: Partial<SSEOptions>,
    fallbackCallback?: () => void,
    generationType?: 'initial-code-gen' | 'code-regen' | 'unknown'
  ): Observable<any> {
    if (!projectId || !jobId) {

      return EMPTY;
    }

    // ENHANCED: Comprehensive connection session validation with deduplication
    const connectionKey = `${projectId}-${jobId}`;

    // ENHANCED: Use comprehensive session validation
    const validation = this.validateConnectionSession(projectId, jobId, generationType);

    if (!validation.isValid) {
      if (validation.shouldCleanup) {

        this.cleanupConnection(connectionKey);
      } else if (validation.existingConnection) {

        return validation.existingConnection;
      } else {

        return EMPTY;
      }
    }

    // CRITICAL FIX: Mark connection as pending to prevent race conditions
    this.pendingConnections.add(connectionKey);

    // ENHANCED: Log connection creation lifecycle event
    this.logConnectionLifecycleEvent('created', connectionKey, {
      projectId,
      jobId,
      generationType,
      options: options || {},
      validationPassed: true
    });

    // ENHANCED: Additional check for same job monitoring with cleanup
    if (this.currentJobId === jobId && this.currentProjectId === projectId && this.isMonitoring()) {

      this.stopMonitoring();
    }

    // ENHANCED: Log connection status and header support before starting
    this.logConnectionStatus('Before Starting SSE Monitoring');

    // Log header support status
    const polyfillInfo = this.sseService.getPolyfillInfo();

    // Store connection details
    this.currentProjectId = projectId;
    this.currentJobId = jobId;
    this.fallbackCallback = fallbackCallback || null;
    this.connectionAttempts = 0;

    // Reset data processor state
    this.sseDataProcessor.reset();

    // ENHANCED: Detect generation type for optimization
    const detectedGenerationType = this.detectGenerationType(generationType, options);

    // Configure SSE options with enhanced settings and event ID checkpointing
    // ENHANCED: Now includes header-based options for EventSource polyfill and query parameters
    // CRITICAL FIX: Increased timeouts to prevent SSE connection errors
    const enhancedOptions: Partial<SSEOptions> = {
      reconnectInterval: 5000, // INCREASED: 5 seconds between reconnects
      maxReconnectAttempts: this.maxConnectionAttempts,
      enableExponentialBackoff: true,
      backoffFactor: 1.5,
      maxBackoffInterval: 60000, // INCREASED: 1 minute max backoff
      enableHeartbeat: true,
      heartbeatInterval: 60000, // INCREASED: 1 minute heartbeat interval
      useHeadersForEventId: true, // NEW: Enable header-based last-event-id
      customHeaders: {
        'Cache-Control': 'no-cache',
        'Accept': 'text/event-stream'
      },
      // OPTIMIZATION: Add generation type context and since parameter
      generationType: detectedGenerationType,
      enableSinceParameter: true, // Enable since=0 optimization
      ...options
    };

    // ENHANCED: Add cached event ID for checkpointing (supports cross-session compatibility)
    const sessionKey = `${projectId}-${jobId}`;
    const cachedEventId = this.getCachedEventId(sessionKey);
    if (cachedEventId) {
      enhancedOptions.lastEventId = cachedEventId;

    } else {

    }

    // Start SSE connection
    const sseObservable = this.sseService.connect(jobId, enhancedOptions).pipe(
      tap((sseEvent) => {
        this.connectionAttempts++;
        this.isConnectedSubject.next(true);

        // ENHANCED: Handle automatic connection closure for initial-code-gen final events
        this.handlePotentialFinalEvent(sseEvent, connectionKey);
      }),
      switchMap((sseEvent: SSEEvent) => {

        // ENHANCED: Process each SSE event through the data processor with session key for checkpoint tracking
        const sessionKey = `${projectId}-${jobId}`;
        return this.sseDataProcessor.processSSEEvent(sseEvent, sessionKey);
      }),
      catchError((error) => {

        this.handleConnectionError(error);
        // ENHANCED: Clean up connection on error
        this.cleanupConnection(connectionKey);
        // CRITICAL FIX: Also remove from pending connections on error
        this.pendingConnections.delete(connectionKey);
        return EMPTY;
      }),
      retry({
        count: this.maxConnectionAttempts,
        delay: (_error, retryCount) => {
          const delay = Math.min(3000 * Math.pow(1.5, retryCount - 1), 30000);

          return timer(delay);
        }
      }),
      share()
    );

    // ENHANCED: Store the connection to prevent duplicates
    this.activeConnections.set(connectionKey, sseObservable);

    // CRITICAL FIX: Remove from pending connections now that it's active
    this.pendingConnections.delete(connectionKey);

    // ENHANCED: Initialize UI state tracking for this connection
    this.connectionUIStates.set(connectionKey, {
      isLoading: true,
      progressDescription: 'Connecting...',
      lastActivity: Date.now()
    });

    // ENHANCED: Log connection status after creation
    this.logConnectionStatus('After Creating SSE Connection');

    return sseObservable;
  }

  /**
   * ENHANCED: Track subscription for proper cleanup
   * This method should be called by consumers when they subscribe to SSE observables
   */
  trackSubscription(connectionKey: string, subscription: Subscription): void {

    // Store the subscription for cleanup
    this.activeSubscriptions.set(connectionKey, subscription);

    // Update UI state to reflect active subscription
    if (this.connectionUIStates.has(connectionKey)) {
      const uiState = this.connectionUIStates.get(connectionKey)!;
      uiState.isLoading = true;
      uiState.progressDescription = 'Connected';
      uiState.lastActivity = Date.now();
    }
  }

  /**
   * ENHANCED: Update UI state for a connection
   */
  updateConnectionUIState(connectionKey: string, updates: Partial<{
    isLoading: boolean;
    progressDescription: string;
  }>): void {
    if (this.connectionUIStates.has(connectionKey)) {
      const uiState = this.connectionUIStates.get(connectionKey)!;
      Object.assign(uiState, updates, { lastActivity: Date.now() });

    }
  }

  /**
   * ENHANCED: Reset all UI states for clean regeneration start
   * This method coordinates with UI components to reset their loading states
   */
  resetAllUIStates(): void {

    try {
      // 1. Clear all connection UI states
      this.connectionUIStates.clear();

      // 2. Reset connection state observables
      this.isConnectedSubject.next(false);

      // 3. Clear any pending error states
      this.connectionErrorSubject = new Subject<any>();
      this.fallbackTriggeredSubject = new Subject<string>();

      // 4. ENHANCED: Coordinate with UI services for comprehensive state reset
      this.coordinateUIServiceReset();

    } catch (error) {

    }
  }

  /**
   * ENHANCED: Coordinate with UI services for comprehensive state reset
   * This method ensures all related UI services are properly reset
   */
  private coordinateUIServiceReset(): void {
    try {
      // Note: We don't directly inject UI services here to avoid circular dependencies
      // Instead, we emit events that UI components can listen to for state reset

      // Emit UI reset event for components to listen to
      this.connectionErrorSubject.next({
        type: 'ui_reset_requested',
        timestamp: Date.now(),
        reason: 'sse_connection_cleanup'
      });

    } catch (error) {

    }
  }

  /**
   * ENHANCED: Get current UI state for a connection
   */
  getConnectionUIState(connectionKey: string): {
    isLoading: boolean;
    progressDescription: string;
    lastActivity: number;
  } | null {
    return this.connectionUIStates.get(connectionKey) || null;
  }

  /**
   * ENHANCED: Check if any connections are currently loading
   */
  hasActiveLoadingStates(): boolean {
    for (const [key, state] of this.connectionUIStates.entries()) {
      if (state.isLoading) {

        return true;
      }
    }
    return false;
  }

  /**
   * ENHANCED: Validate connection session to prevent duplicates
   * Implements comprehensive session-based deduplication with validation
   */
  private validateConnectionSession(projectId: string, jobId: string, generationType?: string): {
    isValid: boolean;
    reason: string;
    existingConnection?: Observable<any>;
    shouldCleanup: boolean;
  } {
    const connectionKey = `${projectId}-${jobId}`;
    const sessionKey = `${projectId}-${jobId}`;

    // 1. Check for active connections
    if (this.activeConnections.has(connectionKey)) {
      const existingConnection = this.activeConnections.get(connectionKey);
      const uiState = this.connectionUIStates.get(connectionKey);

      // Check if the existing connection is stale (no activity for 5 minutes)
      const isStale = uiState && (Date.now() - uiState.lastActivity) > 300000; // 5 minutes

      if (isStale) {

        return {
          isValid: false,
          reason: 'stale_connection',
          shouldCleanup: true
        };
      }

      return {
        isValid: false,
        reason: 'active_connection_exists',
        existingConnection,
        shouldCleanup: false
      };
    }

    // 2. Check for pending connections
    if (this.pendingConnections.has(connectionKey)) {
      const existingConnection = this.activeConnections.get(connectionKey);

      if (existingConnection) {
        return {
          isValid: false,
          reason: 'pending_connection_with_observable',
          existingConnection,
          shouldCleanup: false
        };
      } else {
        // Pending connection without observable - possible race condition

        return {
          isValid: false,
          reason: 'orphaned_pending_connection',
          shouldCleanup: true
        };
      }
    }

    // 3. Check for orphaned subscriptions or UI states
    const hasOrphanedSubscription = this.activeSubscriptions.has(connectionKey);
    const hasOrphanedUIState = this.connectionUIStates.has(connectionKey);

    if (hasOrphanedSubscription || hasOrphanedUIState) {

      return {
        isValid: false,
        reason: 'orphaned_resources',
        shouldCleanup: true
      };
    }

    // 4. Validate generation type consistency for regeneration
    if (generationType === 'code-regen') {
      // For regeneration, ensure no initial-code-gen connections are active
      const initialGenConnections = Array.from(this.activeConnections.keys())
        .filter(key => key.includes('initial-code-gen') || key === connectionKey);

      if (initialGenConnections.length > 0) {

        return {
          isValid: false,
          reason: 'conflicting_generation_type',
          shouldCleanup: true
        };
      }
    }

    return {
      isValid: true,
      reason: 'validation_passed',
      shouldCleanup: false
    };
  }

  /**
   * Stop SSE monitoring
   * ENHANCED: Includes connection cleanup to prevent duplicates
   */
  stopMonitoring(): void {

    this.destroy$.next();
    this.sseService.disconnect();
    this.isConnectedSubject.next(false);

    // ENHANCED: Clean up all active connections
    this.cleanupAllConnections();

    // Reset state
    // this.currentProjectId = null;
    // this.currentJobId = null;
    // this.connectionAttempts = 0;
    // this.fallbackCallback = null;
  }

  /**
   * Detect generation type for SSE optimization
   * ENHANCED: Determines generation type from context and options for since=0 parameter
   */
  private detectGenerationType(
    explicitType?: 'initial-code-gen' | 'code-regen' | 'unknown',
    options?: Partial<SSEOptions>
  ): 'initial-code-gen' | 'code-regen' | 'unknown' {
    // Use explicit type if provided
    if (explicitType) {

      return explicitType;
    }

    // Check if type is specified in options
    if (options?.generationType) {

      return options.generationType;
    }

    // HEURISTIC: Detect based on current state and context
    // If this is the first connection for a project, assume initial generation
    const isFirstConnection = !this.currentProjectId;

    // Check if regeneration is active (from stepper state service if available)
    const isRegenerationActive = this.isRegenerationContext();

    let detectedType: 'initial-code-gen' | 'code-regen' | 'unknown';

    if (isRegenerationActive) {
      detectedType = 'code-regen';
    } else{
      detectedType = 'initial-code-gen';
    }

    return detectedType;
  }

  /**
   * Check if we're in a regeneration context
   * ENHANCED: Attempts to detect regeneration state from available services
   */
  private isRegenerationContext(): boolean {
    // This is a simple heuristic - in a real implementation, you might
    // inject a service that tracks regeneration state
    // For now, we'll assume unknown context means potential initial generation
    return false;
  }

  /**
   * Handle potential final event for initial-code-gen events
   * ENHANCED: Manages connection cleanup when isFinal flag is detected
   * @param sseEvent The SSE event to check
   * @param connectionKey The connection key for cleanup
   */
  private handlePotentialFinalEvent(sseEvent: SSEEvent, connectionKey: string): void {
    // Only handle initial-code-gen events
    if (sseEvent.event !== 'initial-code-gen') {
      return;
    }

    try {
      // Parse event data to check for isFinal flag (support both isFinal and is_final)
      const eventData = JSON.parse(sseEvent.data);
      const status = eventData.status;
      const progress = eventData.progress;
      const isFinal = eventData.isFinal === true || eventData.is_final === true;

      // Check if this is a final event
      const isFinalEvent = isFinal && (
        // Both status and progress are FAILED
        (status === 'FAILED' && progress === 'FAILED') ||
        // Progress is DEPLOY and status is COMPLETED
        (progress === 'DEPLOY' && status === 'COMPLETED')
      );

      if (isFinalEvent) {

        // Schedule cleanup after event processing
        setTimeout(() => {

          this.cleanupConnection(connectionKey);
          this.isConnectedSubject.next(false);

          // Reset current job tracking for initial generation
          if (sseEvent.event === 'initial-code-gen') {
            this.currentProjectId = null;
            this.currentJobId = null;
          }
        }, 150); // Slightly longer delay to ensure data processor completes
      }

    } catch (error) {
      // If we can't parse the data, log but don't clean up

    }
  }

  /**
   * ENHANCED: Clean up a specific connection with comprehensive cleanup
   * Includes subscription disposal, timer cleanup, and UI state reset
   */
  private cleanupConnection(connectionKey: string): void {
    // ENHANCED: Log cleanup lifecycle event
    this.logConnectionLifecycleEvent('cleanup', connectionKey, {
      hasActiveConnection: this.activeConnections.has(connectionKey),
      hasPendingConnection: this.pendingConnections.has(connectionKey),
      hasActiveSubscription: this.activeSubscriptions.has(connectionKey),
      hasTimer: this.connectionTimers.has(connectionKey),
      hasUIState: this.connectionUIStates.has(connectionKey)
    });

    try {
      // 1. Clean up active subscription
      if (this.activeSubscriptions.has(connectionKey)) {
        const subscription = this.activeSubscriptions.get(connectionKey);
        if (subscription && !subscription.closed) {

          subscription.unsubscribe();
        }
        this.activeSubscriptions.delete(connectionKey);
      }

      // 2. Clear any connection timers
      if (this.connectionTimers.has(connectionKey)) {
        const timerId = this.connectionTimers.get(connectionKey);
        if (timerId) {

          clearTimeout(timerId);
        }
        this.connectionTimers.delete(connectionKey);
      }

      // 3. Reset UI state
      if (this.connectionUIStates.has(connectionKey)) {

        this.connectionUIStates.delete(connectionKey);
      }

      // 4. Clean up active connection observable
      if (this.activeConnections.has(connectionKey)) {

        this.activeConnections.delete(connectionKey);

        // ENHANCED: Ensure base SSE service is also cleaned up if this was the active connection
        const [projectId, jobId] = connectionKey.split('-');
        if (this.currentProjectId === projectId && this.currentJobId === jobId) {

          if (this.sseService.isConnected()) {
            this.sseService.disconnect();
          }
        }
      }

      // 5. Clean up pending connections
      if (this.pendingConnections.has(connectionKey)) {

        this.pendingConnections.delete(connectionKey);
      }

    } catch (error) {

      // ENHANCED: Graceful degradation with detailed error tracking and recovery
      const fallbackResults = {
        activeConnection: false,
        pendingConnection: false,
        subscription: false,
        timer: false,
        uiState: false
      };

      // Try each cleanup operation individually with error isolation
      try {
        this.activeConnections.delete(connectionKey);
        fallbackResults.activeConnection = true;
      } catch (e) {

      }

      try {
        this.pendingConnections.delete(connectionKey);
        fallbackResults.pendingConnection = true;
      } catch (e) {

      }

      try {
        this.activeSubscriptions.delete(connectionKey);
        fallbackResults.subscription = true;
      } catch (e) {

      }

      try {
        this.connectionTimers.delete(connectionKey);
        fallbackResults.timer = true;
      } catch (e) {

      }

      try {
        this.connectionUIStates.delete(connectionKey);
        fallbackResults.uiState = true;
      } catch (e) {

      }

      const successfulCleanups = Object.values(fallbackResults).filter(Boolean).length;
      const totalCleanups = Object.keys(fallbackResults).length;

      // ENHANCED: Attempt recovery if partial cleanup succeeded
      if (successfulCleanups > 0 && successfulCleanups < totalCleanups) {
        const failedCleanups = Object.entries(fallbackResults)
          .filter(([_, success]) => !success)
          .map(([type, _]) => type);

        // ENHANCED: Log error recovery attempt
        this.logErrorRecovery(
          connectionKey,
          'partial_cleanup_failure',
          'resource_leak_warning',
          false,
          { failedCleanups, successfulCleanups, totalCleanups }
        );
      } else if (successfulCleanups === totalCleanups) {
        // ENHANCED: Log successful recovery
        this.logErrorRecovery(
          connectionKey,
          'cleanup_error',
          'fallback_cleanup',
          true,
          { successfulCleanups, totalCleanups }
        );
      }
    }
  }

  /**
   * ENHANCED: Clean up all active connections with comprehensive cleanup
   * Includes all subscriptions, timers, and UI states
   */
  private cleanupAllConnections(): void {

    try {
      // 1. Clean up all active subscriptions
      this.activeSubscriptions.forEach((subscription, connectionKey) => {
        if (subscription && !subscription.closed) {

          subscription.unsubscribe();
        }
      });
      this.activeSubscriptions.clear();

      // 2. Clear all connection timers
      this.connectionTimers.forEach((timerId, connectionKey) => {
        if (timerId) {

          clearTimeout(timerId);
        }
      });
      this.connectionTimers.clear();

      // 3. Clear all UI states
      this.connectionUIStates.clear();

      // 4. Clear connection maps
      this.activeConnections.clear();
      this.pendingConnections.clear();

      // 5. Disconnect base SSE service if connected
      if (this.sseService.isConnected()) {

        this.sseService.disconnect();
      }

      // 6. Reset connection state
      this.isConnectedSubject.next(false);
      this.currentProjectId = null;
      this.currentJobId = null;

    } catch (error) {

      // ENHANCED: Graceful degradation with detailed error tracking
      const fallbackResults = {
        activeConnections: false,
        pendingConnections: false,
        activeSubscriptions: false,
        connectionTimers: false,
        connectionUIStates: false,
        connectionState: false,
        baseSSEDisconnect: false
      };

      // Try each cleanup operation individually
      try {
        this.activeConnections.clear();
        fallbackResults.activeConnections = true;
      } catch (e) {

      }

      try {
        this.pendingConnections.clear();
        fallbackResults.pendingConnections = true;
      } catch (e) {

      }

      try {
        this.activeSubscriptions.clear();
        fallbackResults.activeSubscriptions = true;
      } catch (e) {

      }

      try {
        this.connectionTimers.clear();
        fallbackResults.connectionTimers = true;
      } catch (e) {

      }

      try {
        this.connectionUIStates.clear();
        fallbackResults.connectionUIStates = true;
      } catch (e) {

      }

      try {
        this.isConnectedSubject.next(false);
        fallbackResults.connectionState = true;
      } catch (e) {

      }

      try {
        if (this.sseService.isConnected()) {
          this.sseService.disconnect();
        }
        fallbackResults.baseSSEDisconnect = true;
      } catch (e) {

      }

      const successfulCleanups = Object.values(fallbackResults).filter(Boolean).length;
      const totalCleanups = Object.keys(fallbackResults).length;

      // ENHANCED: Log critical failures that may cause memory leaks
      if (successfulCleanups < totalCleanups) {
        const failedOperations = Object.entries(fallbackResults)
          .filter(([_, success]) => !success)
          .map(([operation, _]) => operation);

      }
    }
  }

  /**
   * Check if currently monitoring
   */
  isMonitoring(): boolean {
    return this.isConnectedSubject.value && this.currentJobId !== null;
  }

  /**
   * ENHANCED: Log connection lifecycle events with structured data
   * Provides comprehensive logging for debugging and monitoring
   */
  private logConnectionLifecycleEvent(
    event: 'created' | 'connected' | 'error' | 'disconnected' | 'cleanup' | 'validation',
    connectionKey: string,
    details?: any
  ): void {
    const timestamp = new Date().toISOString();
    const connectionStats = this.getConnectionStatistics();

    const logData = {
      event,
      connectionKey,
      timestamp,
      details: details || {},
      connectionStats: {
        activeConnections: connectionStats.activeConnections,
        pendingConnections: connectionStats.pendingConnections,
        activeSubscriptions: connectionStats.activeSubscriptions,
        connectionTimers: connectionStats.connectionTimers,
        connectionUIStates: connectionStats.connectionUIStates
      },
      serviceState: {
        isConnected: this.isConnectedSubject.value,
        isMonitoring: this.isMonitoring(),
        currentProjectId: this.currentProjectId,
        currentJobId: this.currentJobId,
        baseSSEConnected: this.sseService.isConnected()
      }
    };

    switch (event) {
      case 'created':

        break;
      case 'connected':

        break;
      case 'error':

        break;
      case 'disconnected':

        break;
      case 'cleanup':

        break;
      case 'validation':

        break;
      default:

    }
  }

  /**
   * ENHANCED: Log error recovery attempts with detailed context
   */
  private logErrorRecovery(
    connectionKey: string,
    errorType: string,
    recoveryAction: string,
    success: boolean,
    details?: any
  ): void {
    const logData = {
      connectionKey,
      errorType,
      recoveryAction,
      success,
      timestamp: new Date().toISOString(),
      details: details || {},
      connectionStats: this.getConnectionStatistics()
    };

    if (success) {

    } else {

    }
  }

  /**
   * ENHANCED: Check if a specific connection exists
   */
  hasActiveConnection(projectId: string, jobId: string): boolean {
    const connectionKey = `${projectId}-${jobId}`;
    return this.activeConnections.has(connectionKey);
  }

  /**
   * CRITICAL FIX: Check if a connection is pending
   */
  hasPendingConnection(projectId: string, jobId: string): boolean {
    const connectionKey = `${projectId}-${jobId}`;
    return this.pendingConnections.has(connectionKey);
  }

  /**
   * CRITICAL FIX: Check if a connection exists or is pending
   */
  hasConnectionOrPending(projectId: string, jobId: string): boolean {
    return this.hasActiveConnection(projectId, jobId) || this.hasPendingConnection(projectId, jobId);
  }

  /**
   * ENHANCED: Get active connection count for debugging
   */
  getActiveConnectionCount(): number {
    return this.activeConnections.size;
  }

  /**
   * ENHANCED: Get comprehensive connection statistics for debugging
   * Includes subscription tracking, timers, and UI states
   */
  getConnectionStatistics(): {
    activeConnections: number;
    pendingConnections: number;
    activeSubscriptions: number;
    connectionTimers: number;
    connectionUIStates: number;
    activeKeys: string[];
    pendingKeys: string[];
    subscriptionKeys: string[];
    timerKeys: string[];
    uiStateKeys: string[];
    totalConnections: number;
    subscriptionStates: { [key: string]: boolean };
    uiStates: { [key: string]: any };
  } {
    const subscriptionStates: { [key: string]: boolean } = {};
    this.activeSubscriptions.forEach((subscription, key) => {
      subscriptionStates[key] = !subscription.closed;
    });

    const uiStates: { [key: string]: any } = {};
    this.connectionUIStates.forEach((state, key) => {
      uiStates[key] = { ...state };
    });

    return {
      activeConnections: this.activeConnections.size,
      pendingConnections: this.pendingConnections.size,
      activeSubscriptions: this.activeSubscriptions.size,
      connectionTimers: this.connectionTimers.size,
      connectionUIStates: this.connectionUIStates.size,
      activeKeys: Array.from(this.activeConnections.keys()),
      pendingKeys: Array.from(this.pendingConnections),
      subscriptionKeys: Array.from(this.activeSubscriptions.keys()),
      timerKeys: Array.from(this.connectionTimers.keys()),
      uiStateKeys: Array.from(this.connectionUIStates.keys()),
      totalConnections: this.activeConnections.size + this.pendingConnections.size,
      subscriptionStates,
      uiStates
    };
  }

  /**
   * Get current monitoring details
   */
  getCurrentMonitoringDetails() {
    return {
      projectId: this.currentProjectId,
      jobId: this.currentJobId,
      isConnected: this.isConnectedSubject.value,
      connectionAttempts: this.connectionAttempts,
      sseConnectionState: this.sseService.getConnectionState(),
      dataProcessorState: this.sseDataProcessor.getCurrentState()
    };
  }

  /**
   * Force fallback to polling
   */
  triggerFallback(reason: string = 'Manual trigger'): void {

    this.stopMonitoring();
    this.fallbackTriggeredSubject.next(reason);

    if (this.fallbackCallback) {
      this.fallbackCallback();
    }
  }

  /**
   * Setup enhanced monitoring of base SSE service
   */
  private setupSSEServiceMonitoring(): void {
    // Monitor connection errors with enhanced logging
    this.sseService.connectionError$.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(error => {

      this.handleConnectionError(error);
    });

    // Monitor connection state changes with detailed logging
    this.sseService.isConnected$.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(isConnected => {
      this.isConnectedSubject.next(isConnected);

      if (isConnected) {

        // Reset connection attempts on successful connection
        this.connectionAttempts = 0;
      } else if (this.currentJobId) {

        // Enhanced logging for first-cut generation debugging
        const connectionState = this.sseService.getConnectionState();

        // ENHANCED: Prevent duplicate connections during manual reconnection
        if (this.connectionAttempts < this.maxConnectionAttempts) {

          setTimeout(() => {
            // CRITICAL FIX: Check for existing connections before manual reconnection
            const currentConnectionKey = this.currentProjectId && this.currentJobId
              ? `${this.currentProjectId}-${this.currentJobId}`
              : null;

            if (!this.isConnectedSubject.value && this.currentJobId && currentConnectionKey) {
              // Ensure no duplicate connections exist
              if (this.activeConnections.has(currentConnectionKey)) {

                return;
              }

              if (this.sseService.isConnected()) {

                return;
              }

              // ENHANCED: Check if reconnection is allowed based on completion state
              // Note: We'll let the base SSE service handle the state check since it has the event state

              this.sseService.connect(this.currentJobId, {
                reconnectInterval: 1000, // Faster reconnection for first-cut
                maxReconnectAttempts: 15
              }).pipe(
                takeUntilDestroyed(this.destroyRef)
              ).subscribe({
                next: () => {

                },
                error: (error) => {

                }
              });
            }
          }, 1000);
        }
      }
    });

    // Note: connectionState$ is a signal, not an observable, so we don't subscribe to it
    // Connection state debugging is handled through the isConnected$ observable above
  }

  /**
   * Handle connection errors with enhanced recovery logic
   */
  private handleConnectionError(error: any): void {
    const errorDetails = {
      message: error?.message || 'Unknown error',
      type: error?.type || 'connection_error',
      code: error?.code || 'UNKNOWN',
      attempts: this.connectionAttempts,
      timestamp: new Date().toISOString()
    };

    this.connectionErrorSubject.next(error);
    this.isConnectedSubject.next(false);

    // Enhanced error recovery with better categorization
    if (this.shouldTriggerFallback(error)) {
      const reason = `Connection failed after ${this.connectionAttempts} attempts: ${errorDetails.message}`;

      this.triggerFallback(reason);
    } else {
      // Log that we're continuing to retry

    }
  }

  /**
   * Determine if fallback should be triggered
   */
  private shouldTriggerFallback(error: any): boolean {
    // Trigger fallback if:
    // 1. Max connection attempts reached
    // 2. Specific error types that indicate SSE won't work
    // 3. Network errors that persist

    if (this.connectionAttempts >= this.maxConnectionAttempts) {

      return true;
    }

    // Check for specific error types that indicate SSE incompatibility
    const errorMessage = error?.message?.toLowerCase() || '';
    const fallbackTriggerErrors = [
      'eventsource not supported',
      'cors error',
      'network error',
      'connection refused',
      'timeout'
    ];

    if (fallbackTriggerErrors.some(trigger => errorMessage.includes(trigger))) {

      return true;
    }

    return false;
  }

  /**
   * Cache event ID for regeneration checkpointing
   * @param sessionKey Session key (projectId-jobId)
   * @param eventId Event ID to cache
   */
  cacheEventId(sessionKey: string, eventId: string): void {
    const currentMap = this.regenerationSessionEventIds();
    const newMap = new Map(currentMap);
    newMap.set(sessionKey, eventId);
    this.regenerationSessionEventIds.set(newMap);

    // Also cache in the base SSE service
    this.sseService.cacheEventId(eventId, sessionKey);
  }

  /**
   * Get cached event ID for regeneration checkpointing
   * ENHANCED: Uses base SSE service for cross-session compatibility
   * @param sessionKey Session key (projectId-jobId)
   * @returns Cached event ID or null
   */
  getCachedEventId(sessionKey: string): string | null {
    // Use base SSE service for enhanced caching logic (session-specific, latest, global)
    const cachedId = this.sseService.getCachedEventId(sessionKey);

    return cachedId;
  }

  /**
   * Clear cached event ID for session
   * @param sessionKey Session key to clear
   */
  clearCachedEventId(sessionKey: string): void {
    const currentMap = this.regenerationSessionEventIds();
    const newMap = new Map(currentMap);
    newMap.delete(sessionKey);
    this.regenerationSessionEventIds.set(newMap);

    // Also clear from base SSE service
    this.sseService.clearCachedEventId(sessionKey);
  }

  /**
   * Cleanup resources
   * ENHANCED: Includes connection cleanup to prevent duplicates
   */
  private cleanup(): void {

    // ENHANCED: Clean up all connections first
    this.cleanupAllConnections();

    // Clear all cached event IDs
    this.regenerationSessionEventIds.set(new Map());

    this.destroy$.next();
    this.destroy$.complete();
    this.stopMonitoring();
  }

  /**
   * ENHANCED: Prepare for regeneration by cleaning up existing connections
   * This ensures no duplicate connections during regeneration processes
   * Now includes comprehensive cleanup of all regeneration-related connections
   */
  prepareForRegeneration(projectId: string, jobId: string): void {
    const connectionKey = `${projectId}-${jobId}`;

    try {
      // ENHANCED: Clean up ALL regeneration-related connections, not just the specific one
      // This prevents any lingering connections from interfering with the new regeneration
      const regenerationConnections = Array.from(this.activeConnections.keys())
        .filter(key => key.includes('code-regen') || key === connectionKey);

      if (regenerationConnections.length > 0) {

        regenerationConnections.forEach(key => this.cleanupConnection(key));
      }

      // Clean up the specific connection if it exists
      if (this.activeConnections.has(connectionKey)) {

        this.cleanupConnection(connectionKey);
      }

      // ENHANCED: Clean up any orphaned regeneration subscriptions
      const regenerationSubscriptions = Array.from(this.activeSubscriptions.keys())
        .filter(key => key.includes('code-regen') || key === connectionKey);

      if (regenerationSubscriptions.length > 0) {

        regenerationSubscriptions.forEach(key => {
          const subscription = this.activeSubscriptions.get(key);
          if (subscription && !subscription.closed) {
            subscription.unsubscribe();
          }
          this.activeSubscriptions.delete(key);
        });
      }

      // Stop current monitoring if active
      if (this.isMonitoring()) {

        this.stopMonitoring();
      }

      // Ensure base SSE service is disconnected
      if (this.sseService.isConnected()) {

        this.sseService.disconnect();
      }

      // ENHANCED: Reset connection state and UI states for clean regeneration start
      this.isConnectedSubject.next(false);
      this.resetAllUIStates();

    } catch (error) {

      // Fallback: comprehensive cleanup of all connections
      try {
        this.cleanupAllConnections();

      } catch (fallbackError) {

      }
    }
  }

  /**
   * ENHANCED: Log comprehensive connection status for debugging
   * Useful for troubleshooting duplicate connections and cleanup issues
   */
  logConnectionStatus(context: string = 'Debug'): void {
    const status = this.getServiceStatus();

  }

  /**
   * Test final event handling for debugging
   * ENHANCED: Tests the isFinal flag detection and connection closure
   */
  testFinalEventHandling(isFinal: boolean = true): void {

    // Create a mock initial-code-gen event with isFinal flag (support both formats)
    const mockEvent = {
      id: `test-${Date.now()}`,
      event: 'initial-code-gen',
      data: JSON.stringify({
        status: 'COMPLETED',
        progress: 'DEPLOY',
        isFinal: isFinal,
        is_final: isFinal, // Support both formats
        message: 'Test final event'
      })
    };

    // Test the final event handling logic
    const connectionKey = 'test-project-test-job';
    this.handlePotentialFinalEvent(mockEvent, connectionKey);

  }

  /**
   * Test SSE header functionality for debugging
   * ENHANCED: Tests if last-event-id headers are properly supported
   */
  async testSSEHeaders(): Promise<void> {

    const polyfillInfo = this.sseService.getPolyfillInfo();

    try {
      const headerTest = await this.sseService.testHeaderFunctionality();

      if (headerTest.success) {

      } else {

      }
    } catch (error) {

    }
  }

  /**
   * Get comprehensive service status for debugging
   * ENHANCED: Includes connection management information and header status
   */
  getServiceStatus() {
    const polyfillInfo = this.sseService.getPolyfillInfo();

    return {
      enhancedSSE: {
        isMonitoring: this.isMonitoring(),
        currentProjectId: this.currentProjectId,
        currentJobId: this.currentJobId,
        connectionAttempts: this.connectionAttempts,
        isConnected: this.isConnectedSubject.value,
        // ENHANCED: Connection management status
        activeConnectionsCount: this.activeConnections.size,
        activeConnectionKeys: Array.from(this.activeConnections.keys()),
        pendingConnectionsCount: this.pendingConnections.size,
        pendingConnectionKeys: Array.from(this.pendingConnections),
      },
      baseSSE: this.sseService.getConnectionState(),
      dataProcessor: this.sseDataProcessor.getCurrentState(),
      // ENHANCED: Header support status
      headerSupport: {
        polyfillAvailable: polyfillInfo.polyfillAvailable,
        supportsHeaders: polyfillInfo.supportsHeaders,
        headerMethod: polyfillInfo.headerMethod,
        isSSECompliant: polyfillInfo.isSSECompliant
      }
    };
  }

  /**
   * ENHANCED: Enable retry event filtering for retry operations
   * Filters SSE events to process only those that occur after retry trigger
   * @param retryTimestamp Timestamp when retry was triggered
   */
  enableRetryEventFiltering(retryTimestamp: number): void {

    this.retryTimestamp = retryTimestamp;
    this.isRetryFilteringEnabled = true;

    // Apply filtering to SSE data processor
    this.sseDataProcessor.enableRetryEventFiltering(retryTimestamp);

  }

  /**
   * ENHANCED: Disable retry event filtering
   * Removes event filtering to process all SSE events normally
   */
  disableRetryEventFiltering(): void {

    this.retryTimestamp = null;
    this.isRetryFilteringEnabled = false;

    // Remove filtering from SSE data processor
    this.sseDataProcessor.disableRetryEventFiltering();

  }

  /**
   * ENHANCED: Check if retry event filtering is currently enabled
   * @returns Whether retry event filtering is active
   */
  isRetryEventFilteringEnabled(): boolean {
    return this.isRetryFilteringEnabled;
  }

  /**
   * ENHANCED: Get current retry timestamp for filtering
   * @returns Current retry timestamp or null if not set
   */
  getRetryTimestamp(): number | null {
    return this.retryTimestamp;
  }

  /**
   * ENHANCED: Get connection error observable
   * UI components can subscribe to this for error handling and state coordination
   */
  getConnectionError(): Observable<any> {
    return this.connectionErrorSubject.asObservable();
  }

  /**
   * ENHANCED: Get fallback triggered observable
   * UI components can subscribe to this to handle fallback scenarios
   */
  getFallbackTriggered(): Observable<string> {
    return this.fallbackTriggeredSubject.asObservable();
  }

  /**
   * ENHANCED: Get UI reset events observable
   * UI components can subscribe to this to reset their states when SSE connections are cleaned up
   */
  getUIResetEvents(): Observable<any> {
    return this.connectionErrorSubject.asObservable().pipe(
      filter((event: any) => event?.type === 'ui_reset_requested')
    );
  }

  /**
   * ENHANCED: Notify UI components to reset their loading states
   * This method can be called by UI components to trigger coordinated state reset
   */
  notifyUIReset(reason: string = 'manual_reset'): void {

    this.connectionErrorSubject.next({
      type: 'ui_reset_requested',
      timestamp: Date.now(),
      reason
    });
  }

  /**
   * ENHANCED: Check if any UI components have active loading states
   * This helps coordinate cleanup timing with UI state management
   */
  hasActiveUILoadingStates(): boolean {
    return this.hasActiveLoadingStates();
  }

  /**
   * ENHANCED: Force UI state synchronization
   * This method ensures all UI states are synchronized with connection states
   */
  synchronizeUIStates(): void {

    try {
      // Check for inconsistent states
      const hasActiveConnections = this.activeConnections.size > 0;
      const hasActiveLoadingStates = this.hasActiveLoadingStates();

      if (!hasActiveConnections && hasActiveLoadingStates) {

        this.resetAllUIStates();
      }

      // Notify UI components of current state
      this.notifyUIReset('state_synchronization');

    } catch (error) {

    }
  }
}
