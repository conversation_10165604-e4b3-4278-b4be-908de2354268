import { Injectable } from '@angular/core';
import { Observable, of, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { GlobalErrorHandlerService, ErrorHandlingConfig } from './global-error-handler.service';
import { createLogger } from '../../utils/logger';

/**
 * HTTP Error Helper Service
 * Provides common error handling patterns for services
 * Reduces boilerplate code in individual services
 */
@Injectable({
  providedIn: 'root'
})
export class HttpErrorHelperService {

  constructor(private globalErrorHandler: GlobalErrorHandlerService) {}

  /**
   * Handle errors with fallback value
   * Use this when you want to return a default value on error
   * @param fallbackValue Value to return on error
   * @param context Context for error logging
   * @param config Optional error handling configuration
   * @returns Error handling operator
   */
  handleWithFallback<T>(
    fallbackValue: T,
    context: string,
    config?: Partial<ErrorHandlingConfig>
  ) {
    return catchError((error: HttpErrorResponse) => {

      // Handle error through global handler but don't show toast by default for fallback scenarios
      this.globalErrorHandler.handleHttpError(error, error.url as any, {
        showToast: false,
        logError: true,
        ...config
      });

      return of(fallbackValue);
    });
  }

  /**
   * Handle errors with retry logic
   * Use this for operations that should be retried on certain errors
   * @param context Context for error logging
   * @param config Optional error handling configuration
   * @returns Error handling operator
   */
  handleWithRetry(
    context: string,
    config?: Partial<ErrorHandlingConfig>
  ) {
    return catchError((error: HttpErrorResponse) => {

      // Handle error through global handler
      this.globalErrorHandler.handleHttpError(error, error.url as any, config);

      // Re-throw error for retry logic to handle
      return throwError(() => error);
    });
  }

  /**
   * Handle errors with silent logging
   * Use this when you want to log errors but not show user notifications
   * @param context Context for error logging
   * @returns Error handling operator
   */
  handleSilently(context: string) {
    return catchError((error: HttpErrorResponse) => {

      // Handle error through global handler but don't show toast
      this.globalErrorHandler.handleHttpError(error, error.url as any, {
        showToast: false,
        logError: true
      });

      return throwError(() => error);
    });
  }

  /**
   * Handle errors with custom user message
   * Use this when you want to show a specific message to the user
   * @param userMessage Custom message to show to user
   * @param context Context for error logging
   * @returns Error handling operator
   */
  handleWithCustomMessage(userMessage: string, context: string) {
    return catchError((error: HttpErrorResponse) => {

      // Handle error through global handler with custom message
      this.globalErrorHandler.handleHttpError(error, error.url as any, {
        showToast: true,
        logError: true,
        userFriendlyMessage: userMessage
      });

      return throwError(() => error);
    });
  }

  /**
   * Handle errors for critical operations
   * Use this for operations where errors should be prominently displayed
   * @param context Context for error logging
   * @returns Error handling operator
   */
  handleCriticalError(context: string) {
    return catchError((error: HttpErrorResponse) => {

      // Handle error through global handler with emphasis on user notification
      this.globalErrorHandler.handleHttpError(error, error.url as any, {
        showToast: true,
        logError: true,
        userFriendlyMessage: `A critical error occurred in ${context}. Please try again or contact support if the problem persists.`
      });

      return throwError(() => error);
    });
  }

  /**
   * Handle errors for background operations
   * Use this for operations that happen in the background where user notification is optional
   * @param context Context for error logging
   * @param showToUser Whether to show toast to user
   * @returns Error handling operator
   */
  handleBackgroundError(context: string, showToUser: boolean = false) {
    return catchError((error: HttpErrorResponse) => {

      // Handle error through global handler
      this.globalErrorHandler.handleHttpError(error, error.url as any, {
        showToast: showToUser,
        logError: true
      });

      return throwError(() => error);
    });
  }

  /**
   * Create a standardized error response for service-specific handling
   * @param error HTTP error response
   * @param context Context information
   * @returns Standardized error information
   */
  createErrorInfo(error: HttpErrorResponse, context: string) {
    return {
      status: error.status,
      message: error.message,
      context,
      timestamp: new Date().toISOString(),
      retryable: this.globalErrorHandler.isRetryable(error.status),
      userMessage: this.globalErrorHandler.getUserFriendlyMessage(error.status)
    };
  }
}
