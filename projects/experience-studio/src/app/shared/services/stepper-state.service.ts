import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { createLogger } from '../utils/logger';

/**
* Service to manage the state of the vertical stepper component across the application
* This allows for better state management and coordination between components
* ENHANCED: Added SSE stream synchronization support for retry operations
*/
@Injectable({
 providedIn: 'root'
})
export class StepperStateService {
 // Subject to track whether the stepper should be reset
 private resetStepperSubject = new BehaviorSubject<boolean>(false);

 // Subject to track the current project ID
 private currentProjectIdSubject = new BehaviorSubject<string>('');

 // Subject to track regeneration state - when true, stepper should be isolated
 private regenerationActiveSubject = new BehaviorSubject<boolean>(false);

 // Observable to expose the reset stepper state
 public resetStepper$: Observable<boolean> = this.resetStepperSubject.asObservable();

 // Observable to expose the current project ID
 public currentProjectId$: Observable<string> = this.currentProjectIdSubject.asObservable();

 // Observable to expose regeneration state
 public regenerationActive$: Observable<boolean> = this.regenerationActiveSubject.asObservable();
  constructor() { }
  /**
  * Triggers a reset of the stepper component
  * This should be called when starting a new project or when the stepper needs to be reset
  */
 public triggerStepperReset(): void {
   // Set to true to trigger a reset
   this.resetStepperSubject.next(true);

   // Reset back to false after a short delay to allow components to react
   setTimeout(() => {
     this.resetStepperSubject.next(false);
   }, 100);
 }
  /**
  * Sets the current project ID
  * This helps track when a new project is started
  * @param projectId The ID of the current project
  */
 public setCurrentProjectId(projectId: string): void {
   // Only trigger a reset if the project ID has changed
   const currentId = this.currentProjectIdSubject.getValue();
   if (currentId !== projectId) {
     this.currentProjectIdSubject.next(projectId);

     // If we're switching to a new project, trigger a stepper reset
     if (currentId && projectId) {
       this.triggerStepperReset();
     }
   }
 }
  /**
  * Gets the current project ID
  * @returns The current project ID
  */
 public getCurrentProjectId(): string {
   return this.currentProjectIdSubject.getValue();
 }

 /**
  * Set regeneration active state
  * When true, the stepper should be isolated from SSE events
  * @param isActive Whether regeneration is currently active
  */
 public setRegenerationActive(isActive: boolean): void {
   this.regenerationActiveSubject.next(isActive);
 }

 /**
  * Get current regeneration state
  * @returns Whether regeneration is currently active
  */
 public isRegenerationActive(): boolean {
   return this.regenerationActiveSubject.getValue();
 }

 /**
  * ENHANCED: Resynchronize stepper with existing SSE stream during retry
  * This does NOT create new connections, just resyncs with current stream
  * @param projectId The project ID for the SSE stream
  * @param jobId The job ID for the SSE stream
  */
 public resynchronizeWithSSEStream(projectId: string, jobId: string): void {

   // Update project ID if needed (this will trigger stepper updates)
   if (this.getCurrentProjectId() !== projectId) {
     this.setCurrentProjectId(projectId);
   }

   // Signal stepper components to resync with existing SSE data
   // This triggers a brief reset to pick up latest SSE state
   this.triggerStepperReset();

 }
  /**
  * Clears the current project ID
  * This should be called when no project is active
  */
 public clearCurrentProjectId(): void {
   this.currentProjectIdSubject.next('');
   this.triggerStepperReset();
 }
}

