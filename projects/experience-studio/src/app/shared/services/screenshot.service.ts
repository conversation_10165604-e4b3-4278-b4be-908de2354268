import { Injectable, inject } from '@angular/core';
import { Observable, from, throwError, of } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { createLogger } from '../utils/logger';
import { ToastService } from './toast.service';

export interface ScreenshotOptions {
  width?: number;
  height?: number;
  scale?: number;
  backgroundColor?: string;
  allowTaint?: boolean;
  useCORS?: boolean;
  scrollX?: number;
  scrollY?: number;
  windowWidth?: number;
  windowHeight?: number;
}

export interface ScreenshotResult {
  success: boolean;
  canvas?: HTMLCanvasElement;
  dataUrl?: string;
  blob?: Blob;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ScreenshotService {

  private readonly toastService = inject(ToastService);

  /**
   * Fallback method to capture iframe when content is not accessible
   */
  private captureIframeFallback(
    iframe: HTMLIFrameElement,
    html2canvas: any,
    options: ScreenshotOptions
  ): Observable<ScreenshotResult> {

    // Wait a bit for iframe to load if it hasn't already
    return new Observable<ScreenshotResult>((observer) => {
      const attemptCapture = () => {
        // Try to access iframe content one more time
        const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

        if (iframeDoc) {

          // If content is now accessible, use the direct method
          const targetElement = iframeDoc.body || iframeDoc.documentElement;
          if (targetElement) {
            this.captureElementDirectly(targetElement, html2canvas, options).subscribe(observer);
            return;
          }
        }

        // If still no access, capture the iframe container

        const iframeContainer = iframe.parentElement || iframe;
        this.captureElementDirectly(iframeContainer, html2canvas, options).subscribe(observer);
      };

      // Try immediately first
      attemptCapture();
    });
  }

  /**
   * Direct element capture helper
   */
  private captureElementDirectly(
    element: HTMLElement,
    html2canvas: any,
    options: ScreenshotOptions
  ): Observable<ScreenshotResult> {
    const defaultOptions: any = {
      allowTaint: true,
      useCORS: true,
      scale: options.scale || 1,
      backgroundColor: options.backgroundColor || '#ffffff',
      scrollX: options.scrollX || 0,
      scrollY: options.scrollY || 0,
      width: options.width,
      height: options.height,
      ...options
    };

    return from(html2canvas(element, defaultOptions) as Promise<HTMLCanvasElement>).pipe(
      switchMap((canvas: HTMLCanvasElement) => {

        if (!canvas || canvas.width === 0 || canvas.height === 0) {
          return of({
            success: false,
            error: 'Generated canvas is invalid or empty'
          });
        }

        const dataUrl = canvas.toDataURL('image/png');

        return new Observable<ScreenshotResult>((observer) => {
          canvas.toBlob((blob) => {
            if (!blob) {
              observer.next({
                success: false,
                error: 'Failed to convert canvas to blob'
              });
            } else {
              observer.next({
                success: true,
                canvas,
                dataUrl,
                blob
              });
            }
            observer.complete();
          }, 'image/png');
        });
      }),
      catchError((error) => {

        return of({
          success: false,
          error: error.message || 'Element capture failed'
        });
      })
    );
  }

  /**
   * Dynamically load html2canvas library
   */
  private loadHtml2Canvas(): Observable<any> {

    return from(import('html2canvas')).pipe(
      map((module) => {
        const html2canvas = module.default || module;

        return html2canvas;
      }),
      catchError((error) => {

        this.toastService.error('Screenshot feature requires html2canvas library. Please install it to enable screenshots.');
        return throwError(() => new Error('html2canvas library not available'));
      })
    );
  }

  /**
   * Capture screenshot of iframe content
   * This method captures the actual rendered HTML content inside the iframe
   */
  captureIframeContent(
    iframe: HTMLIFrameElement,
    options: ScreenshotOptions = {}
  ): Observable<ScreenshotResult> {

    return this.loadHtml2Canvas().pipe(
      switchMap((html2canvas) => {
        try {

          // Get iframe document
          const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

          if (!iframeDoc) {

            // Fallback: try to capture the iframe element itself
            return this.captureIframeFallback(iframe, html2canvas, options);
          }

          // Get the body element to capture
          const targetElement = iframeDoc.body || iframeDoc.documentElement;

          if (!targetElement) {
            const error = 'No content found in iframe';

            return of({
              success: false,
              error
            });
          }

          // Default options optimized for iframe content
          const defaultOptions: any = {
            allowTaint: true,
            useCORS: true,
            scale: options.scale || 1,
            backgroundColor: options.backgroundColor || '#ffffff',
            scrollX: options.scrollX || 0,
            scrollY: options.scrollY || 0,
            width: options.width,
            height: options.height,
            ...options
          };

          // Capture the iframe content
          return from(html2canvas(targetElement, defaultOptions) as Promise<HTMLCanvasElement>).pipe(
            switchMap((canvas: HTMLCanvasElement) => {

              if (!canvas || canvas.width === 0 || canvas.height === 0) {
                const error = 'Generated canvas is invalid or empty';

                return of({
                  success: false,
                  error
                });
              }

              // Convert to data URL
              const dataUrl = canvas.toDataURL('image/png');

              // Convert to blob for download
              return new Observable<ScreenshotResult>((observer) => {
                canvas.toBlob((blob) => {

                  if (!blob) {
                    observer.next({
                      success: false,
                      error: 'Failed to convert canvas to blob'
                    });
                  } else {
                    observer.next({
                      success: true,
                      canvas,
                      dataUrl,
                      blob
                    });
                  }
                  observer.complete();
                }, 'image/png');
              });
            }),
            catchError((error) => {

              return of({
                success: false,
                error: error.message || 'Screenshot capture failed'
              });
            })
          );
        } catch (error: any) {

          return of({
            success: false,
            error: error.message || 'Screenshot setup failed'
          });
        }
      }),
      catchError((error) => {

        return of({
          success: false,
          error: 'Failed to load screenshot library'
        });
      })
    );
  }

  /**
   * Capture screenshot of any DOM element
   */
  captureElement(
    element: HTMLElement,
    options: ScreenshotOptions = {}
  ): Observable<ScreenshotResult> {

    return this.loadHtml2Canvas().pipe(
      switchMap((html2canvas) => {
        const defaultOptions: any = {
          allowTaint: true,
          useCORS: true,
          scale: 1,
          backgroundColor: '#ffffff',
          ...options
        };

        return from(html2canvas(element, defaultOptions) as Promise<HTMLCanvasElement>).pipe(
          switchMap((canvas: HTMLCanvasElement) => {

            const dataUrl = canvas.toDataURL('image/png');

            return new Observable<ScreenshotResult>((observer) => {
              canvas.toBlob((blob) => {
                observer.next({
                  success: true,
                  canvas,
                  dataUrl,
                  blob: blob || undefined
                });
                observer.complete();
              }, 'image/png');
            });
          }),
          catchError((error) => {

            return of({
              success: false,
              error: error.message || 'Element screenshot failed'
            });
          })
        );
      }),
      catchError((error) => {

        return of({
          success: false,
          error: 'Failed to load screenshot library'
        });
      })
    );
  }

  /**
   * Download screenshot as PNG file
   */
  downloadScreenshot(
    screenshotResult: ScreenshotResult,
    filename: string = 'screenshot.png'
  ): void {

    if (!screenshotResult.success) {
      const errorMsg = `Cannot download screenshot - capture failed: ${screenshotResult.error || 'Unknown error'}`;

      this.toastService.error(errorMsg);
      return;
    }

    if (!screenshotResult.blob) {
      const errorMsg = 'Cannot download screenshot - no valid image data (blob is missing)';

      this.toastService.error(errorMsg);
      return;
    }

    try {
      // Create download link
      const url = URL.createObjectURL(screenshotResult.blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      URL.revokeObjectURL(url);

      this.toastService.success(`Screenshot saved as ${filename}`);
    } catch (error: any) {

      this.toastService.error('Failed to download screenshot');
    }
  }

  /**
   * Wait for iframe to be ready for capture
   */
  private waitForIframeReady(iframe: HTMLIFrameElement): Observable<boolean> {
    return new Observable<boolean>((observer) => {
      const checkIframe = () => {
        try {
          const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

          if (iframeDoc && iframeDoc.readyState === 'complete') {

            observer.next(true);
            observer.complete();
            return;
          }
        } catch (error) {
          // Cross-origin or other access issues

          observer.next(true);
          observer.complete();
          return;
        }

        // If not ready, wait a bit and try again
        setTimeout(checkIframe, 100);
      };

      // Start checking
      checkIframe();

      // Timeout after 5 seconds
      setTimeout(() => {

        observer.next(true);
        observer.complete();
      }, 5000);
    });
  }

  /**
   * Capture and download iframe content in one step
   */
  captureAndDownloadIframe(
    iframe: HTMLIFrameElement,
    filename: string = 'page-screenshot.png',
    options: ScreenshotOptions = {}
  ): Observable<void> {

    return this.waitForIframeReady(iframe).pipe(
      switchMap(() => this.captureIframeContent(iframe, options)),
      map((result) => {
        this.downloadScreenshot(result, filename);
      }),
      catchError((error) => {

        this.toastService.error('Failed to capture screenshot');
        return throwError(() => error);
      })
    );
  }

  /**
   * Generate filename based on page title and timestamp
   */
  generateFilename(pageTitle: string = 'page'): string {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
    const sanitizedTitle = pageTitle.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
    return `${sanitizedTitle}-${timestamp}.png`;
  }

  /**
   * Get optimal screenshot options for mobile frames
   */
  getMobileFrameOptions(): ScreenshotOptions {
    return {
      width: 420,
      height: 720,
      scale: 2, // Higher resolution for better quality
      backgroundColor: '#ffffff',
      allowTaint: true,
      useCORS: true
    };
  }

  /**
   * Get optimal screenshot options for web frames
   */
  getWebFrameOptions(): ScreenshotOptions {
    return {
      width: 1200,
      height: 800,
      scale: 1.5, // Good balance between quality and file size
      backgroundColor: '#ffffff',
      allowTaint: true,
      useCORS: true
    };
  }

  /**
   * Get optimal screenshot options for canvas nodes
   */
  getCanvasNodeOptions(): ScreenshotOptions {
    return {
      width: 420,
      height: 720,
      scale: 2, // Higher resolution for better quality
      backgroundColor: '#ffffff',
      allowTaint: true,
      useCORS: true
    };
  }

  /**
   * Capture viewport-aware screenshot based on current viewport mode
   * This method detects the current viewport mode and captures the appropriate frame
   */
  captureViewportAwareScreenshot(
    currentViewportMode: 'mobile' | 'web',
    mobileIframe?: HTMLIFrameElement,
    webIframe?: HTMLIFrameElement,
    pageTitle: string = 'wireframe'
  ): Observable<void> {

    const filename = this.generateFilename(pageTitle);

    if (currentViewportMode === 'mobile' && mobileIframe) {
      const options = this.getMobileFrameOptions();
      return this.captureAndDownloadIframe(mobileIframe, filename, options);
    } else if (currentViewportMode === 'web' && webIframe) {
      const options = this.getWebFrameOptions();
      return this.captureAndDownloadIframe(webIframe, filename, options);
    } else {

      return throwError(() => new Error(`No ${currentViewportMode} iframe available for screenshot`));
    }
  }

  /**
   * Test method to verify html2canvas is working
   */
  testScreenshotCapability(): Observable<boolean> {

    return this.loadHtml2Canvas().pipe(
      switchMap((html2canvas) => {
        // Create a simple test element
        const testDiv = document.createElement('div');
        testDiv.style.width = '100px';
        testDiv.style.height = '100px';
        testDiv.style.backgroundColor = '#ff0000';
        testDiv.style.position = 'absolute';
        testDiv.style.top = '-1000px';
        testDiv.innerHTML = 'Test';

        document.body.appendChild(testDiv);

        return from(html2canvas(testDiv) as Promise<HTMLCanvasElement>).pipe(
          map((canvas) => {
            document.body.removeChild(testDiv);
            const success = canvas && canvas.width > 0 && canvas.height > 0;

            return success;
          }),
          catchError((error) => {
            document.body.removeChild(testDiv);

            return of(false);
          })
        );
      }),
      catchError((error) => {

        return of(false);
      })
    );
  }
}
