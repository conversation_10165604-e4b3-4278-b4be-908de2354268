import { Injectable, signal } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface SearchMatch {
  range: {
    startLineNumber: number;
    startColumn: number;
    endLineNumber: number;
    endColumn: number;
  };
  text: string;
  lineText: string;
}

export interface SearchState {
  query: string;
  matches: SearchMatch[];
  currentMatchIndex: number;
  totalMatches: number;
  isSearching: boolean;
  caseSensitive: boolean;
  wholeWord: boolean;
  useRegex: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class MonacoSearchService {
  
  // Search state signals
  private _searchState = signal<SearchState>({
    query: '',
    matches: [],
    currentMatchIndex: -1,
    totalMatches: 0,
    isSearching: false,
    caseSensitive: false,
    wholeWord: false,
    useRegex: false
  });

  // Observable for external components to subscribe to search state changes
  private _searchStateSubject = new BehaviorSubject<SearchState>(this._searchState());

  // Current editor instance
  private currentEditor: any = null;
  private currentDecorations: string[] = [];
  private currentMatchDecorations: string[] = [];

  constructor() {}

  /**
   * Get the current search state as a signal
   */
  get searchState() {
    return this._searchState.asReadonly();
  }

  /**
   * Get the search state as an observable
   */
  get searchState$(): Observable<SearchState> {
    return this._searchStateSubject.asObservable();
  }

  /**
   * Set the current editor instance for search operations
   */
  setEditor(editor: any): void {
    this.currentEditor = editor;
    this.clearSearch();

    // Set up editor event listeners for better search integration
    if (editor) {
      // Clear search when content changes significantly
      editor.onDidChangeModelContent(() => {
        const state = this._searchState();
        if (state.query) {
          // Re-run search with current query to update matches
          this.search(state.query, {
            caseSensitive: state.caseSensitive,
            wholeWord: state.wholeWord,
            useRegex: state.useRegex
          });
        }
      });
    }
  }

  /**
   * Perform search in the current editor
   */
  search(query: string, options: Partial<Pick<SearchState, 'caseSensitive' | 'wholeWord' | 'useRegex'>> = {}): void {
    if (!this.currentEditor || !query.trim()) {
      this.clearSearch();
      return;
    }

    const currentState = this._searchState();
    const newState: SearchState = {
      ...currentState,
      query: query.trim(),
      isSearching: true,
      ...options
    };

    this.updateSearchState(newState);

    try {
      const matches = this.findMatches(query, newState);
      
      const updatedState: SearchState = {
        ...newState,
        matches,
        totalMatches: matches.length,
        currentMatchIndex: matches.length > 0 ? 0 : -1,
        isSearching: false
      };

      this.updateSearchState(updatedState);
      this.highlightMatches(matches);
      
      if (matches.length > 0) {
        this.navigateToMatch(0);
      }
    } catch (error) {
      console.error('Search error:', error);
      this.updateSearchState({
        ...newState,
        isSearching: false,
        matches: [],
        totalMatches: 0,
        currentMatchIndex: -1
      });
    }
  }

  /**
   * Navigate to the next match
   */
  findNext(): void {
    const state = this._searchState();
    if (state.matches.length === 0) return;

    const nextIndex = (state.currentMatchIndex + 1) % state.matches.length;
    this.navigateToMatch(nextIndex);
  }

  /**
   * Navigate to the previous match
   */
  findPrevious(): void {
    const state = this._searchState();
    if (state.matches.length === 0) return;

    const prevIndex = state.currentMatchIndex <= 0 
      ? state.matches.length - 1 
      : state.currentMatchIndex - 1;
    this.navigateToMatch(prevIndex);
  }

  /**
   * Clear the current search
   */
  clearSearch(): void {
    this.clearHighlights();
    this.updateSearchState({
      query: '',
      matches: [],
      currentMatchIndex: -1,
      totalMatches: 0,
      isSearching: false,
      caseSensitive: false,
      wholeWord: false,
      useRegex: false
    });
  }

  /**
   * Toggle case sensitivity
   */
  toggleCaseSensitive(): void {
    const state = this._searchState();
    const newCaseSensitive = !state.caseSensitive;
    
    if (state.query) {
      this.search(state.query, {
        caseSensitive: newCaseSensitive,
        wholeWord: state.wholeWord,
        useRegex: state.useRegex
      });
    } else {
      this.updateSearchState({ ...state, caseSensitive: newCaseSensitive });
    }
  }

  /**
   * Toggle whole word search
   */
  toggleWholeWord(): void {
    const state = this._searchState();
    const newWholeWord = !state.wholeWord;
    
    if (state.query) {
      this.search(state.query, {
        caseSensitive: state.caseSensitive,
        wholeWord: newWholeWord,
        useRegex: state.useRegex
      });
    } else {
      this.updateSearchState({ ...state, wholeWord: newWholeWord });
    }
  }

  /**
   * Toggle regex search
   */
  toggleRegex(): void {
    const state = this._searchState();
    const newUseRegex = !state.useRegex;
    
    if (state.query) {
      this.search(state.query, {
        caseSensitive: state.caseSensitive,
        wholeWord: state.wholeWord,
        useRegex: newUseRegex
      });
    } else {
      this.updateSearchState({ ...state, useRegex: newUseRegex });
    }
  }

  /**
   * Find all matches in the editor
   */
  private findMatches(query: string, options: SearchState): SearchMatch[] {
    if (!this.currentEditor) return [];

    const model = this.currentEditor.getModel();
    if (!model) return [];

    try {
      const searchOptions = {
        matchCase: options.caseSensitive,
        wholeWord: options.wholeWord,
        isRegex: options.useRegex
      };

      const matches = model.findMatches(
        query,
        true, // searchOnlyEditableRange
        searchOptions.isRegex,
        searchOptions.matchCase,
        searchOptions.wholeWord ? '\\b' : null,
        false // captureMatches
      );

      return matches.map((match: any) => ({
        range: match.range,
        text: model.getValueInRange(match.range),
        lineText: model.getLineContent(match.range.startLineNumber)
      }));
    } catch (error) {
      console.error('Error finding matches:', error);
      return [];
    }
  }

  /**
   * Highlight all matches in the editor
   */
  private highlightMatches(matches: SearchMatch[]): void {
    if (!this.currentEditor) return;

    this.clearHighlights();

    if (matches.length === 0) return;

    // Create decorations for all matches
    const decorations = matches.map((match, index) => ({
      range: match.range,
      options: {
        className: index === 0 ? 'monaco-search-current-match' : 'monaco-search-match',
        stickiness: 1 // monaco.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges
      }
    }));

    this.currentDecorations = this.currentEditor.deltaDecorations([], decorations);
  }

  /**
   * Navigate to a specific match
   */
  private navigateToMatch(index: number): void {
    const state = this._searchState();
    if (!this.currentEditor || index < 0 || index >= state.matches.length) return;

    const match = state.matches[index];
    
    // Update current match index
    this.updateSearchState({ ...state, currentMatchIndex: index });

    // Scroll to the match
    this.currentEditor.revealRangeInCenter(match.range);
    
    // Update decorations to highlight current match differently
    this.updateCurrentMatchHighlight(index);
    
    // Set cursor position
    this.currentEditor.setPosition({
      lineNumber: match.range.startLineNumber,
      column: match.range.startColumn
    });
  }

  /**
   * Update the highlight for the current match
   */
  private updateCurrentMatchHighlight(currentIndex: number): void {
    if (!this.currentEditor) return;

    const state = this._searchState();
    const decorations = state.matches.map((match, index) => ({
      range: match.range,
      options: {
        className: index === currentIndex ? 'monaco-search-current-match' : 'monaco-search-match',
        stickiness: 1
      }
    }));

    this.currentDecorations = this.currentEditor.deltaDecorations(this.currentDecorations, decorations);
  }

  /**
   * Clear all search highlights
   */
  private clearHighlights(): void {
    if (!this.currentEditor) return;

    if (this.currentDecorations.length > 0) {
      this.currentDecorations = this.currentEditor.deltaDecorations(this.currentDecorations, []);
    }
    
    if (this.currentMatchDecorations.length > 0) {
      this.currentMatchDecorations = this.currentEditor.deltaDecorations(this.currentMatchDecorations, []);
    }
  }

  /**
   * Update the search state and notify subscribers
   */
  private updateSearchState(newState: Partial<SearchState>): void {
    const currentState = this._searchState();
    const updatedState = { ...currentState, ...newState };
    this._searchState.set(updatedState);
    this._searchStateSubject.next(updatedState);
  }
}
