import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { NewPollingResponse, ProgressState, StatusType } from '../models/polling-response.interface';
import { createLogger } from '../utils/logger';

/**
 * Service to write raw polling responses to files in the codebase
 * for analysis, debugging, and further development use.
 *
 * This service follows Angular best practices:
 * - Injectable with providedIn: 'root'
 * - Reactive programming with RxJS
 * - Proper error handling
 * - Type safety with TypeScript
 * - Logging for debugging
 */
@Injectable({
  providedIn: 'root'
})
export class PollingResponseFileWriterService {

  // Session tracking
  private sessionId: string;
  private startTime: Date;

  // State management
  private isWritingEnabled$ = new BehaviorSubject<boolean>(false);
  private currentSessionStats$ = new BehaviorSubject<any>(null);

  // File organization
  private readonly baseOutputPath = 'src/assets/polling-responses';
  private readonly stateFilesPath = `${this.baseOutputPath}/by-state`;
  private readonly sessionFilesPath = `${this.baseOutputPath}/by-session`;
  private readonly rawFilesPath = `${this.baseOutputPath}/raw`;

  // Response tracking
  private responsesByState: Map<string, any[]> = new Map();
  private allResponses: any[] = [];
  private stateTransitions: any[] = [];

  constructor(private http: HttpClient) {
    this.sessionId = this.generateSessionId();
    this.startTime = new Date();
    this.initializeService();
  }

  // ============================================================================
  // PUBLIC API METHODS
  // ============================================================================

  /**
   * Enable automatic file writing for polling responses
   */
  enableFileWriting(): void {
    this.isWritingEnabled$.next(true);

  }

  /**
   * Disable automatic file writing
   */
  disableFileWriting(): void {
    this.isWritingEnabled$.next(false);

  }

  /**
   * Check if file writing is currently enabled
   */
  isFileWritingEnabled(): Observable<boolean> {
    return this.isWritingEnabled$.asObservable();
  }

  /**
   * Write a raw polling response to appropriate files
   */
  writePollingResponse(
    response: any,
    responseType: 'enhanced' | 'new' | 'legacy' = 'enhanced',
    context?: any
  ): void {
    if (!this.isWritingEnabled$.value) {
      return;
    }

    try {
      const processedResponse = this.processResponse(response, responseType, context);

      // Write to different file categories
      this.writeToStateFile(processedResponse);
      this.writeToRawFile(processedResponse);
      this.writeToSessionFile(processedResponse);

      // Track state transitions
      this.trackStateTransition(processedResponse);

      // Update session statistics
      this.updateSessionStats();

    } catch (error) {

    }
  }

  /**
   * Write enhanced polling response with additional analysis
   */
  writeEnhancedResponse(response: NewPollingResponse, context?: any): void {
    if (!this.isWritingEnabled$.value) {
      return;
    }

    const enhancedData = {
      ...response,
      _metadata: {
        sessionId: this.sessionId,
        timestamp: new Date().toISOString(),
        responseType: 'enhanced',
        context: context || null,
        analysis: this.analyzeResponse(response)
      }
    };

    this.writePollingResponse(enhancedData, 'enhanced', context);
  }

  /**
   * Get current session statistics
   */
  getSessionStats(): Observable<any> {
    return this.currentSessionStats$.asObservable();
  }

  /**
   * Export all responses for a specific state
   */
  exportStateResponses(state: string): void {
    const stateResponses = this.responsesByState.get(state) || [];
    if (stateResponses.length === 0) {

      return;
    }

    const exportData = {
      state,
      sessionId: this.sessionId,
      totalResponses: stateResponses.length,
      responses: stateResponses,
      exportedAt: new Date().toISOString()
    };

    this.downloadFile(
      new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' }),
      `polling-responses-${state}-${this.sessionId}.json`
    );
  }

  /**
   * Export complete session data
   */
  exportSessionData(): void {
    const sessionData = {
      sessionInfo: {
        sessionId: this.sessionId,
        startTime: this.startTime.toISOString(),
        endTime: new Date().toISOString(),
        duration: new Date().getTime() - this.startTime.getTime(),
        totalResponses: this.allResponses.length
      },
      stateTransitions: this.stateTransitions,
      responsesByState: Object.fromEntries(this.responsesByState),
      allResponses: this.allResponses,
      statistics: this.generateDetailedStats()
    };

    this.downloadFile(
      new Blob([JSON.stringify(sessionData, null, 2)], { type: 'application/json' }),
      `complete-session-${this.sessionId}.json`
    );
  }

  /**
   * Clear all stored responses and reset session
   */
  clearSession(): void {
    this.responsesByState.clear();
    this.allResponses = [];
    this.stateTransitions = [];
    this.sessionId = this.generateSessionId();
    this.startTime = new Date();
    this.updateSessionStats();

  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Initialize the service
   */
  private initializeService(): void {

    this.updateSessionStats();
  }

  /**
   * Process raw response into structured format
   */
  private processResponse(response: any, responseType: string, context?: any): any {
    const timestamp = new Date();

    return {
      // Original response data
      ...response,

      // Service metadata
      _serviceMetadata: {
        sessionId: this.sessionId,
        timestamp: timestamp.toISOString(),
        responseType,
        context: context || null,
        timeFromStart: timestamp.getTime() - this.startTime.getTime(),
        responseSize: JSON.stringify(response).length,
        sequenceNumber: this.allResponses.length + 1
      }
    };
  }

  /**
   * Write response to state-specific file
   */
  private writeToStateFile(processedResponse: any): void {
    const state = processedResponse.progress || 'unknown';

    if (!this.responsesByState.has(state)) {
      this.responsesByState.set(state, []);
    }

    this.responsesByState.get(state)!.push(processedResponse);

    // Create state-specific file content
    const stateFileContent = {
      state,
      sessionId: this.sessionId,
      lastUpdated: new Date().toISOString(),
      totalResponses: this.responsesByState.get(state)!.length,
      responses: this.responsesByState.get(state)
    };

    // In a real implementation, this would write to the file system
    // For now, we'll create downloadable files
    this.createStateFile(state, stateFileContent);
  }

  /**
   * Write response to raw file
   */
  private writeToRawFile(processedResponse: any): void {
    this.allResponses.push(processedResponse);

    const rawFileContent = {
      sessionId: this.sessionId,
      lastUpdated: new Date().toISOString(),
      totalResponses: this.allResponses.length,
      responses: this.allResponses
    };

    this.createRawFile(rawFileContent);
  }

  /**
   * Write response to session file
   */
  private writeToSessionFile(processedResponse: any): void {
    const sessionFileContent = {
      sessionInfo: {
        sessionId: this.sessionId,
        startTime: this.startTime.toISOString(),
        lastUpdated: new Date().toISOString(),
        totalResponses: this.allResponses.length + 1
      },
      latestResponse: processedResponse,
      recentResponses: this.allResponses.slice(-10) // Last 10 responses
    };

    this.createSessionFile(sessionFileContent);
  }

  /**
   * Track state transitions for analysis
   */
  private trackStateTransition(processedResponse: any): void {
    const currentState = processedResponse.progress;
    const currentStatus = processedResponse.status;

    const lastTransition = this.stateTransitions[this.stateTransitions.length - 1];

    // Only track if state or status changed
    if (!lastTransition ||
        lastTransition.state !== currentState ||
        lastTransition.status !== currentStatus) {

      const transition = {
        fromState: lastTransition?.state || null,
        fromStatus: lastTransition?.status || null,
        toState: currentState,
        toStatus: currentStatus,
        timestamp: new Date().toISOString(),
        timeFromStart: new Date().getTime() - this.startTime.getTime(),
        sequenceNumber: this.stateTransitions.length + 1
      };

      this.stateTransitions.push(transition);

    }
  }

  /**
   * Analyze response for insights
   */
  private analyzeResponse(response: NewPollingResponse): any {
    return {
      progress: response.progress,
      status: response.status,
      hasArtifacts: response.metadata?.some(m => m.type === 'artifact') || false,
      hasFiles: response.metadata?.some(m => m.type === 'files') || false,
      hasPreviewUrl: response.metadata?.some(m => m.type === 'ref_code') || false,
      hasFileNames: response.metadata?.some(m => m.type === 'fileNames') || false,
      historyLength: response.history?.length || 0,
      metadataCount: response.metadata?.length || 0,
      logLength: response.log?.length || 0,
      metadataTypes: response.metadata?.map(m => m.type) || []
    };
  }

  /**
   * Update session statistics
   */
  private updateSessionStats(): void {
    const stats = {
      sessionId: this.sessionId,
      startTime: this.startTime.toISOString(),
      currentTime: new Date().toISOString(),
      duration: new Date().getTime() - this.startTime.getTime(),
      totalResponses: this.allResponses.length,
      stateCount: this.responsesByState.size,
      transitionCount: this.stateTransitions.length,
      states: Array.from(this.responsesByState.keys()),
      isWritingEnabled: this.isWritingEnabled$.value
    };

    this.currentSessionStats$.next(stats);
  }

  /**
   * Generate detailed statistics
   */
  private generateDetailedStats(): any {
    const stateStats = new Map<string, any>();

    // Analyze responses by state
    this.responsesByState.forEach((responses, state) => {
      const statuses = responses.map(r => r.status);
      const statusCounts = statuses.reduce((acc, status) => {
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      stateStats.set(state, {
        totalResponses: responses.length,
        statusDistribution: statusCounts,
        firstSeen: responses[0]?._serviceMetadata?.timestamp,
        lastSeen: responses[responses.length - 1]?._serviceMetadata?.timestamp,
        avgResponseSize: responses.reduce((sum, r) => sum + (r._serviceMetadata?.responseSize || 0), 0) / responses.length
      });
    });

    return {
      overview: {
        totalResponses: this.allResponses.length,
        uniqueStates: this.responsesByState.size,
        stateTransitions: this.stateTransitions.length,
        sessionDuration: new Date().getTime() - this.startTime.getTime()
      },
      stateAnalysis: Object.fromEntries(stateStats),
      transitionAnalysis: this.analyzeTransitions()
    };
  }

  /**
   * Analyze state transitions
   */
  private analyzeTransitions(): any {
    const transitionPairs = this.stateTransitions.map(t => `${t.fromState} -> ${t.toState}`);
    const transitionCounts = transitionPairs.reduce((acc, pair) => {
      acc[pair] = (acc[pair] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalTransitions: this.stateTransitions.length,
      uniqueTransitions: Object.keys(transitionCounts).length,
      transitionFrequency: transitionCounts,
      averageTimePerTransition: this.stateTransitions.length > 0
        ? (new Date().getTime() - this.startTime.getTime()) / this.stateTransitions.length
        : 0
    };
  }

  /**
   * Create state-specific file
   */
  private createStateFile(state: string, content: any): void {
    const filename = `state-${state.replace(/[^a-zA-Z0-9]/g, '-')}-${this.sessionId}.json`;
    const blob = new Blob([JSON.stringify(content, null, 2)], { type: 'application/json' });

    // Store for potential download
    this.storeFile(`${this.stateFilesPath}/${filename}`, blob);
  }

  /**
   * Create raw response file
   */
  private createRawFile(content: any): void {
    const filename = `raw-responses-${this.sessionId}.json`;
    const blob = new Blob([JSON.stringify(content, null, 2)], { type: 'application/json' });

    // Store for potential download
    this.storeFile(`${this.rawFilesPath}/${filename}`, blob);
  }

  /**
   * Create session file
   */
  private createSessionFile(content: any): void {
    const filename = `session-${this.sessionId}.json`;
    const blob = new Blob([JSON.stringify(content, null, 2)], { type: 'application/json' });

    // Store for potential download
    this.storeFile(`${this.sessionFilesPath}/${filename}`, blob);
  }

  /**
   * Store file for later access (in a real app, this would write to file system)
   */
  private storeFile(path: string, blob: Blob): void {
    // In a browser environment, we can't directly write to the file system
    // This method would be implemented differently in a Node.js environment
    // For now, we'll log the file creation

  }

  /**
   * Download file to user's download folder
   */
  private downloadFile(blob: Blob, filename: string): void {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    return `session-${timestamp}-${random}`;
  }
}
