import { Injectable, inject } from '@angular/core';
import { Observable, of, combineLatest } from 'rxjs';
import { map, catchError, tap, switchMap } from 'rxjs/operators';
import { createLogger } from '../utils/logger';
import { FileModel } from '../components/code-viewer/code-viewer.component';
import { FileTreePersistenceService } from './file-tree-persistence.service';
import { MonacoStateManagementService, MonacoEditorState, MonacoModelState } from './monaco-state-management.service';
import { AppStateService } from './app-state.service';
import { CodeSharingService } from './code-sharing.service';

export interface CodeExtractionResult {
  files: FileModel[];
  source: 'file-tree-persistence' | 'monaco-editor-states' | 'monaco-model-states' | 'app-state' | 'code-sharing' | 'combined';
  totalFiles: number;
  extractionMethod: string;
  success: boolean;
  error?: string;
}

export interface CodeExtractionOptions {
  preferUserEdits?: boolean;
  includeUnsavedChanges?: boolean;
  fallbackToAllSources?: boolean;
  logDetails?: boolean;
}

/**
 * Comprehensive code extraction service for regeneration payload formation
 *
 * This service provides multiple extraction methods with fallback mechanisms:
 * 1. Primary: File tree persistence service (current snapshot)
 * 2. Fallback 1: Monaco editor states (with user edits)
 * 3. Fallback 2: Monaco model states (current content)
 * 4. Fallback 3: App state service (generated code)
 * 5. Fallback 4: Code sharing service (generated code)
 * 6. Combined: Merge from multiple sources for comprehensive extraction
 */
@Injectable({
  providedIn: 'root'
})
export class CodeExtractionService {

  constructor(
    private fileTreePersistenceService: FileTreePersistenceService,
    private monacoStateManagementService: MonacoStateManagementService,
    private appStateService: AppStateService,
    private codeSharingService: CodeSharingService
  ) {}

  /**
   * Extract code files using comprehensive multi-source approach
   * This is the main method that should be used for regeneration payload formation
   */
  extractCodeFiles(options: CodeExtractionOptions = {}): Observable<CodeExtractionResult> {
    const defaultOptions: CodeExtractionOptions = {
      preferUserEdits: true,
      includeUnsavedChanges: true,
      fallbackToAllSources: true,
      logDetails: true,
      ...options
    };

    return this.extractWithFallbacks(defaultOptions).pipe(
      tap(result => {
        if (defaultOptions.logDetails) {
          this.logExtractionResult(result);
        }
      }),
      catchError(error => {

        return of({
          files: [],
          source: 'combined' as const,
          totalFiles: 0,
          extractionMethod: 'error-fallback',
          success: false,
          error: error.message || 'Unknown extraction error'
        });
      })
    );
  }

  /**
   * Extract from file tree persistence service (primary source)
   */
  extractFromFileTreePersistence(): Observable<CodeExtractionResult> {

    try {
      const currentFiles = this.fileTreePersistenceService.getCurrentFiles();

      if (currentFiles && currentFiles.length > 0) {

        return of({
          files: this.validateAndCleanFiles(currentFiles),
          source: 'file-tree-persistence',
          totalFiles: currentFiles.length,
          extractionMethod: 'getCurrentFiles()',
          success: true
        });
      } else {

        return of({
          files: [],
          source: 'file-tree-persistence',
          totalFiles: 0,
          extractionMethod: 'getCurrentFiles()',
          success: false,
          error: 'No files found in file tree persistence'
        });
      }
    } catch (error) {

      return of({
        files: [],
        source: 'file-tree-persistence',
        totalFiles: 0,
        extractionMethod: 'getCurrentFiles()',
        success: false,
        error: (error as any)?.message || 'File tree persistence extraction failed'
      });
    }
  }

  /**
   * Extract from Monaco editor states (includes user edits)
   */
  extractFromMonacoEditorStates(): Observable<CodeExtractionResult> {

    return this.monacoStateManagementService.editorStates.pipe(
      map(editorStatesMap => {
        const files: FileModel[] = [];

        for (const [filePath, editorState] of editorStatesMap.entries()) {
          if (editorState.content && editorState.content.trim() !== '') {
            files.push({
              name: filePath,
              type: 'file',
              content: editorState.content,
              fileName: filePath
            });
          }
        }

        if (files.length > 0) {

          return {
            files: this.validateAndCleanFiles(files),
            source: 'monaco-editor-states' as const,
            totalFiles: files.length,
            extractionMethod: 'editorStates observable',
            success: true
          };
        } else {

          return {
            files: [],
            source: 'monaco-editor-states' as const,
            totalFiles: 0,
            extractionMethod: 'editorStates observable',
            success: false,
            error: 'No valid files found in Monaco editor states'
          };
        }
      }),
      catchError(error => {

        return of({
          files: [],
          source: 'monaco-editor-states' as const,
          totalFiles: 0,
          extractionMethod: 'editorStates observable',
          success: false,
          error: error.message || 'Monaco editor states extraction failed'
        });
      })
    );
  }

  /**
   * Validate and clean file models
   */
  private validateAndCleanFiles(files: FileModel[]): FileModel[] {
    return files.filter(file => {
      // Ensure file has required properties
      if (!file.name && !file.fileName) {

        return false;
      }

      // Ensure file has content
      if (!file.content || file.content.trim() === '') {

        return false;
      }

      return true;
    }).map(file => ({
      ...file,
      // Ensure fileName is set if only name is available
      fileName: file.fileName || file.name || 'unknown-file',
      // Ensure name is set if only fileName is available, fallback to 'unknown-file'
      name: file.name || file.fileName || 'unknown-file',
      // Ensure type is set
      type: file.type || 'file'
    }));
  }

  /**
   * Log extraction result details
   */
  private logExtractionResult(result: CodeExtractionResult): void {
    if (result.success) {

    } else {

    }
  }

  /**
   * Extract from Monaco model states (current content)
   */
  extractFromMonacoModelStates(): Observable<CodeExtractionResult> {

    return this.monacoStateManagementService.modelStates.pipe(
      map(modelStatesMap => {
        const files: FileModel[] = [];

        for (const [modelUri, modelState] of modelStatesMap.entries()) {
          if (modelState.content && modelState.content.trim() !== '') {
            // Extract file path from model URI
            const filePath = this.extractFilePathFromModelUri(modelUri);
            files.push({
              name: filePath,
              type: 'file',
              content: modelState.content,
              fileName: filePath
            });
          }
        }

        if (files.length > 0) {

          return {
            files: this.validateAndCleanFiles(files),
            source: 'monaco-model-states' as const,
            totalFiles: files.length,
            extractionMethod: 'modelStates observable',
            success: true
          };
        } else {

          return {
            files: [],
            source: 'monaco-model-states' as const,
            totalFiles: 0,
            extractionMethod: 'modelStates observable',
            success: false,
            error: 'No valid files found in Monaco model states'
          };
        }
      }),
      catchError(error => {

        return of({
          files: [],
          source: 'monaco-model-states' as const,
          totalFiles: 0,
          extractionMethod: 'modelStates observable',
          success: false,
          error: error.message || 'Monaco model states extraction failed'
        });
      })
    );
  }

  /**
   * Extract from app state service (generated code)
   */
  extractFromAppState(): Observable<CodeExtractionResult> {

    try {
      const generatedCode = this.appStateService.getProjectState().generatedCode;

      if (generatedCode) {
        const files = this.convertGeneratedCodeToFileModels(generatedCode);

        if (files.length > 0) {

          return of({
            files: this.validateAndCleanFiles(files),
            source: 'app-state',
            totalFiles: files.length,
            extractionMethod: 'getProjectState().generatedCode',
            success: true
          });
        } else {

          return of({
            files: [],
            source: 'app-state',
            totalFiles: 0,
            extractionMethod: 'getProjectState().generatedCode',
            success: false,
            error: 'Generated code could not be converted to FileModel[]'
          });
        }
      } else {

        return of({
          files: [],
          source: 'app-state',
          totalFiles: 0,
          extractionMethod: 'getProjectState().generatedCode',
          success: false,
          error: 'No generated code found in app state'
        });
      }
    } catch (error) {

      return of({
        files: [],
        source: 'app-state',
        totalFiles: 0,
        extractionMethod: 'getProjectState().generatedCode',
        success: false,
        error: (error as any)?.message || 'App state extraction failed'
      });
    }
  }

  /**
   * Extract from code sharing service (generated code)
   */
  extractFromCodeSharing(): Observable<CodeExtractionResult> {

    try {
      const generatedCode = this.codeSharingService.getGeneratedCode();

      if (generatedCode) {
        const files = this.convertGeneratedCodeToFileModels(generatedCode);

        if (files.length > 0) {

          return of({
            files: this.validateAndCleanFiles(files),
            source: 'code-sharing',
            totalFiles: files.length,
            extractionMethod: 'getGeneratedCode()',
            success: true
          });
        } else {

          return of({
            files: [],
            source: 'code-sharing',
            totalFiles: 0,
            extractionMethod: 'getGeneratedCode()',
            success: false,
            error: 'Generated code could not be converted to FileModel[]'
          });
        }
      } else {

        return of({
          files: [],
          source: 'code-sharing',
          totalFiles: 0,
          extractionMethod: 'getGeneratedCode()',
          success: false,
          error: 'No generated code found in code sharing service'
        });
      }
    } catch (error) {

      return of({
        files: [],
        source: 'code-sharing',
        totalFiles: 0,
        extractionMethod: 'getGeneratedCode()',
        success: false,
        error: (error as any)?.message || 'Code sharing service extraction failed'
      });
    }
  }

  /**
   * Extract with comprehensive fallback mechanism
   */
  private extractWithFallbacks(options: CodeExtractionOptions): Observable<CodeExtractionResult> {

    // Try primary source first: file tree persistence
    return this.extractFromFileTreePersistence().pipe(
      switchMap(result => {
        if (result.success && result.files.length > 0) {

          return of(result);
        }

        return this.extractFromMonacoEditorStates();
      }),
      switchMap(result => {
        if (result.success && result.files.length > 0) {

          return of(result);
        }

        return this.extractFromMonacoModelStates();
      }),
      switchMap(result => {
        if (result.success && result.files.length > 0) {

          return of(result);
        }

        return this.extractFromAppState();
      }),
      switchMap(result => {
        if (result.success && result.files.length > 0) {

          return of(result);
        }

        return this.extractFromCodeSharing();
      }),
      switchMap(result => {
        if (result.success && result.files.length > 0) {

          return of(result);
        }

        if (options.fallbackToAllSources) {

          return this.extractFromAllSourcesCombined();
        } else {

          return of({
            files: [],
            source: 'combined' as const,
            totalFiles: 0,
            extractionMethod: 'all-sources-failed',
            success: false,
            error: 'All extraction sources failed to find code files'
          });
        }
      })
    );
  }

  /**
   * Extract from all sources and combine results
   */
  private extractFromAllSourcesCombined(): Observable<CodeExtractionResult> {

    const sources = [
      this.extractFromFileTreePersistence(),
      this.extractFromMonacoEditorStates(),
      this.extractFromMonacoModelStates(),
      this.extractFromAppState(),
      this.extractFromCodeSharing()
    ];

    return combineLatest(sources).pipe(
      map(results => {
        const allFiles: FileModel[] = [];
        const fileMap = new Map<string, FileModel>();
        const successfulSources: string[] = [];

        // Combine files from all sources, avoiding duplicates
        for (const result of results) {
          if (result.success && result.files.length > 0) {
            successfulSources.push(result.source);

            for (const file of result.files) {
              const fileKey = file.name || file.fileName || '';
              if (fileKey && !fileMap.has(fileKey)) {
                fileMap.set(fileKey, file);
                allFiles.push(file);
              }
            }
          }
        }

        if (allFiles.length > 0) {

          return {
            files: this.validateAndCleanFiles(allFiles),
            source: 'combined' as const,
            totalFiles: allFiles.length,
            extractionMethod: `combined-from-${successfulSources.join('-')}`,
            success: true
          };
        } else {

          return {
            files: [],
            source: 'combined' as const,
            totalFiles: 0,
            extractionMethod: 'combined-all-failed',
            success: false,
            error: 'No files found in any extraction source'
          };
        }
      }),
      catchError(error => {

        return of({
          files: [],
          source: 'combined' as const,
          totalFiles: 0,
          extractionMethod: 'combined-error',
          success: false,
          error: error.message || 'Combined extraction failed'
        });
      })
    );
  }

  /**
   * Convert generated code to FileModel array
   */
  private convertGeneratedCodeToFileModels(generatedCode: any): FileModel[] {
    if (!generatedCode) {
      return [];
    }

    try {
      // Handle different formats of generated code
      if (Array.isArray(generatedCode)) {
        // Already in array format
        return generatedCode.filter(item => item && (item.name || item.fileName) && item.content)
          .map(item => ({
            name: item.name || item.fileName,
            type: item.type || 'file',
            content: item.content,
            fileName: item.fileName || item.name
          }));
      } else if (typeof generatedCode === 'object') {
        // Check if it has a files property
        if (generatedCode.files && Array.isArray(generatedCode.files)) {
          return this.convertGeneratedCodeToFileModels(generatedCode.files);
        }

        // Check if it has file-like properties
        if (generatedCode.name && generatedCode.content) {
          return [{
            name: generatedCode.name,
            type: generatedCode.type || 'file',
            content: generatedCode.content,
            fileName: generatedCode.fileName || generatedCode.name
          }];
        }

        // Try to extract files from object properties
        const files: FileModel[] = [];
        for (const [key, value] of Object.entries(generatedCode)) {
          if (value && typeof value === 'object' && (value as any).content) {
            files.push({
              name: key,
              type: 'file',
              content: (value as any).content,
              fileName: key
            });
          } else if (typeof value === 'string' && value.trim() !== '') {
            // Treat the key as filename and value as content
            files.push({
              name: key,
              type: 'file',
              content: value,
              fileName: key
            });
          }
        }
        return files;
      }

      return [];
    } catch (error) {

      return [];
    }
  }

  /**
   * Extract file path from Monaco model URI
   */
  private extractFilePathFromModelUri(modelUri: string): string {
    if (!modelUri) {
      return 'unknown-file';
    }

    try {
      // Handle different URI formats
      if (modelUri.startsWith('file://')) {
        return modelUri.replace('file://', '');
      } else if (modelUri.startsWith('inmemory://')) {
        return modelUri.replace('inmemory://', '');
      } else if (modelUri.includes('://')) {
        // Extract path after protocol
        const parts = modelUri.split('://');
        return parts.length > 1 ? parts[1] : modelUri;
      } else {
        // Assume it's already a file path
        return modelUri;
      }
    } catch (error) {

      return modelUri;
    }
  }
}
