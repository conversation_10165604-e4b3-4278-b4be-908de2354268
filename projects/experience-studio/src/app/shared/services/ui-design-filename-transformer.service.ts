import { Injectable } from '@angular/core';
import { FilenameNormalizationService, FilenameNormalizationResult } from './filename-normalization.service';
import { createLogger } from '../utils/logger';

/**
 * Result of UI Design file name transformation
 */
export interface UIDesignFilenameTransformResult {
  originalFileName: string;
  displayTitle: string;
  canonicalKey: string;
  confidence: number;
  transformationSteps: string[];
}

/**
 * Specialized service for transforming UI Design file names into user-friendly display titles
 *
 * Transforms:
 * - "login.html" → "Login Page"
 * - "dashboard.html" → "Dashboard Page"
 * - "user-profile.html" → "User Profile Page"
 * - "userSettings.html" → "User Settings Page"
 */
@Injectable({
  providedIn: 'root'
})
export class UIDesignFilenameTransformerService {

  constructor(private filenameNormalizationService: FilenameNormalizationService) {
    // Configure the normalization service for UI Design specific requirements
    this.filenameNormalizationService.updateConfig({
      addPageSuffix: true,
      pageSuffixText: 'Page',
      removeExtensions: ['.html', '.htm', '.xhtml', '.shtml', '.php', '.jsp', '.asp'],
      fallbackPrefix: 'Untitled Page'
    });

  }

  /**
   * Transform a file name into a user-friendly display title
   *
   * @param fileName - Raw file name from API response (e.g., "login.html")
   * @returns Transformed display title (e.g., "Login Page")
   */
  transformFileName(fileName: string): UIDesignFilenameTransformResult {

    if (!fileName || typeof fileName !== 'string') {
      return this.createFallbackResult(fileName);
    }

    const transformationSteps: string[] = [];
    transformationSteps.push(`Input: "${fileName}"`);

    try {
      // Use the enhanced filename normalization service
      const normalizationResult: FilenameNormalizationResult =
        this.filenameNormalizationService.normalizeFilename(fileName);

      transformationSteps.push(...normalizationResult.processingSteps);
      transformationSteps.push(`Final display title: "${normalizationResult.displayName}"`);

      const result: UIDesignFilenameTransformResult = {
        originalFileName: fileName,
        displayTitle: normalizationResult.displayName,
        canonicalKey: normalizationResult.canonicalKey,
        confidence: normalizationResult.confidence,
        transformationSteps
      };

      return result;

    } catch (error) {

      transformationSteps.push(`Error occurred: ${error}`);
      return this.createFallbackResult(fileName, transformationSteps);
    }
  }

  /**
   * Transform multiple file names in batch
   *
   * @param fileNames - Array of raw file names
   * @returns Array of transformation results
   */
  transformFileNames(fileNames: string[]): UIDesignFilenameTransformResult[] {

    const results = fileNames.map(fileName => this.transformFileName(fileName));

    return results;
  }

  /**
   * Get display title only (convenience method)
   *
   * @param fileName - Raw file name
   * @returns Display title string
   */
  getDisplayTitle(fileName: string): string {
    const result = this.transformFileName(fileName);
    return result.displayTitle;
  }

  /**
   * Validate if a file name is suitable for UI Design transformation
   *
   * @param fileName - File name to validate
   * @returns True if suitable for transformation
   */
  isValidUIDesignFileName(fileName: string): boolean {
    if (!fileName || typeof fileName !== 'string') {
      return false;
    }

    // Check if it's a web-related file
    const webExtensions = ['.html', '.htm', '.xhtml', '.shtml', '.php', '.jsp', '.asp'];
    const hasWebExtension = webExtensions.some(ext =>
      fileName.toLowerCase().endsWith(ext.toLowerCase())
    );

    // Check if it has meaningful content (not just extension)
    const nameWithoutExtension = fileName.replace(/\.[^/.]+$/, '');
    const hasMeaningfulContent = nameWithoutExtension.length > 0 &&
      /[a-zA-Z0-9]/.test(nameWithoutExtension);

    return hasWebExtension && hasMeaningfulContent;
  }

  /**
   * Create fallback result for invalid or problematic file names
   */
  private createFallbackResult(
    fileName: string,
    existingSteps: string[] = []
  ): UIDesignFilenameTransformResult {
    const fallbackTitle = `Untitled Page ${Date.now()}`;
    const steps = [
      ...existingSteps,
      'Using fallback result due to invalid input'
    ];

    return {
      originalFileName: fileName || '',
      displayTitle: fallbackTitle,
      canonicalKey: fallbackTitle.toLowerCase().replace(/[^a-z0-9]/g, ''),
      confidence: 0,
      transformationSteps: steps
    };
  }
}
