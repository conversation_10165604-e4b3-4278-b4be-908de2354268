import { Directive, ElementRef, Input, OnChanges, SimpleChanges, inject } from '@angular/core';
import { CSPCompliantContentService } from '../services/csp-compliant-content.service';

@Directive({
  selector: '[safeSrcdoc]',
  standalone: true
})
export class SafeSrcdocDirective implements OnChanges {
  @Input() safeSrcdoc: string = '';
  @Input() isWireframeContent: boolean = false; // Flag to identify wireframe content

  private readonly cspCompliantContentService = inject(CSPCompliantContentService);

  constructor(private el: ElementRef<HTMLIFrameElement>) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['safeSrcdoc'] && this.safeSrcdoc) {
      this.setSrcdocContent(this.safeSrcdoc);
    }
  }

  private setSrcdocContent(content: string): void {
    if (!content) return;

    // Decode HTML entities to prevent double encoding
    const decodedContent = this.decodeHtmlEntities(content);

    // Process content for CSP compliance (production-ready)
    // Use specialized wireframe processing if this is wireframe content
    const cspCompliantContent = this.isWireframeContent
      ? this.cspCompliantContentService.processWireframeContent(decodedContent)
      : this.cspCompliantContentService.processHTMLContent(decodedContent);

    // Ensure proper HTML styling
    const styledContent = this.ensureProperHtmlStyling(cspCompliantContent);

    // Fix link targets to prevent iframe navigation
    const finalContent = this.fixLinkTargets(styledContent);

    // Set the srcdoc attribute directly on the DOM element
    // This bypasses Angular's automatic encoding
    this.el.nativeElement.srcdoc = finalContent;
  }

  private decodeHtmlEntities(content: string): string {
    if (!content) return '';

    // Create a temporary DOM element to decode HTML entities
    const textarea = document.createElement('textarea');
    textarea.innerHTML = content;
    let decodedContent = textarea.value;

    // Additional manual decoding for common entities that might not be handled
    decodedContent = decodedContent
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&#x27;/g, "'")
      .replace(/&#x2F;/g, '/')
      .replace(/&#10;/g, '\n')
      .replace(/&nbsp;/g, ' ');

    return decodedContent;
  }

  private ensureProperHtmlStyling(content: string): string {
    if (!content) return '';

    // Check if content already has a complete HTML structure
    if (content.includes('<!DOCTYPE html>') || content.includes('<html>')) {
      return content;
    }

    // Check if content has basic CSS reset
    const hasBasicStyling = content.includes('margin:') || content.includes('padding:') || content.includes('<style>');

    if (hasBasicStyling) {
      return content;
    }

    // Inject basic CSS reset and styling for proper rendering
    const basicCSS = `
      <style>
        * {
          box-sizing: border-box;
        }
        body {
          margin: 0;
          padding: 16px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          line-height: 1.5;
          color: #333;
          background: white;
        }
        h1, h2, h3, h4, h5, h6 {
          margin-top: 0;
          margin-bottom: 0.5em;
        }
        p {
          margin-top: 0;
          margin-bottom: 1em;
        }
        img {
          max-width: 100%;
          height: auto;
        }
        button {
          padding: 8px 16px;
          border: 1px solid #ddd;
          border-radius: 4px;
          background: white;
          cursor: pointer;
        }
        button:hover {
          background: #f5f5f5;
        }
      </style>
    `;

    // If content is just a fragment, wrap it in a basic HTML structure
    if (!content.includes('<body>')) {
      return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  ${basicCSS}
</head>
<body>
  ${content}
</body>
</html>`;
    }

    // If content has body but no head styling, inject CSS into head or body
    if (content.includes('<head>')) {
      return content.replace('<head>', `<head>${basicCSS}`);
    } else if (content.includes('<body>')) {
      return content.replace('<body>', `<body>${basicCSS}`);
    }

    return content;
  }

  private fixLinkTargets(content: string): string {
    if (!content) return '';

    // Parse the HTML content
    const parser = new DOMParser();
    const doc = parser.parseFromString(content, 'text/html');

    // Find all links and set them to open in new tab
    const links = doc.querySelectorAll('a[href]');
    links.forEach(link => {
      link.setAttribute('target', '_blank');
      link.setAttribute('rel', 'noopener noreferrer');
    });

    // Return the modified HTML
    return doc.documentElement.outerHTML;
  }
}
