import { 
  Injectable, 
  inject, 
  signal, 
  computed, 
  DestroyRef, 
  ChangeDetectorRef,
  effect
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ThemeService } from '../services/theme-service/theme.service';
import { createLogger } from '../utils/logger';

/**
 * Modern Angular 19+ Theme Management Mixin
 * 
 * Provides centralized theme management with:
 * - Angular Signals for reactive state
 * - Automatic subscription cleanup
 * - OnPush change detection compatibility
 * - Type-safe theme handling
 * - Consistent API across components
 * 
 * Usage:
 * ```typescript
 * export class MyComponent extends ThemeManagerMixin {
 *   constructor() {
 *     super();
 *     // Theme is automatically available as this.theme()
 *     // Custom theme updates can be handled via this.onThemeChange()
 *   }
 * 
 *   protected onThemeChange(theme: 'light' | 'dark'): void {
 *     // Custom theme-specific logic
 *     this.updateCustomAssets(theme);
 *   }
 * }
 * ```
 */
@Injectable()
export abstract class ThemeManagerMixin {
  // Angular 19+ Signals for reactive theme state
  protected readonly theme = signal<'light' | 'dark'>('light');
  
  // Computed values for common theme-based properties
  protected readonly isDarkTheme = computed(() => this.theme() === 'dark');
  protected readonly isLightTheme = computed(() => this.theme() === 'light');
  protected readonly themeClass = computed(() => `${this.theme()}-theme`);
  
  // Injected services using modern inject() pattern
  private readonly themeService = inject(ThemeService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly cdr = inject(ChangeDetectorRef);

  constructor() {
    this.initializeThemeManagement();
  }

  /**
   * Initialize theme management with automatic subscription and cleanup
   */
  private initializeThemeManagement(): void {
    // Set initial theme from service
    this.theme.set(this.themeService.getCurrentTheme());
    
    // Subscribe to theme changes with automatic cleanup
    this.themeService.themeObservable
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((newTheme: 'light' | 'dark') => {
        this.theme.set(newTheme);
        this.handleThemeChange(newTheme);
        this.cdr.markForCheck(); // Ensure OnPush components update
      });

    // Create effect for theme changes (Angular 19+ pattern)
    effect(() => {
      const currentTheme = this.theme();

      this.onThemeChange(currentTheme);
    });
  }

  /**
   * Handle theme change with consistent logging and change detection
   */
  private handleThemeChange(theme: 'light' | 'dark'): void {

    // Trigger custom theme change logic in derived components
    this.onThemeChange(theme);
  }

  /**
   * Abstract method for components to implement custom theme change logic
   * Override this method in your component to handle theme-specific updates
   */
  protected onThemeChange(theme: 'light' | 'dark'): void {
    // Default implementation - can be overridden by components
  }

  /**
   * Utility method to get theme-specific asset path
   */
  protected getThemeAsset(basePath: string, extension: string = 'svg'): string {
    return `${basePath}-${this.theme()}.${extension}`;
  }

  /**
   * Utility method to get theme-specific CSS classes
   */
  protected getThemeClasses(...baseClasses: string[]): string[] {
    return baseClasses.map(cls => `${cls}-${this.theme()}`);
  }

  /**
   * Utility method for theme-conditional values
   */
  protected getThemeValue<T>(lightValue: T, darkValue: T): T {
    return this.isDarkTheme() ? darkValue : lightValue;
  }

  /**
   * Toggle theme (delegates to ThemeService)
   */
  protected toggleTheme(): void {
    this.themeService.toggleTheme();
  }

  /**
   * Set specific theme (delegates to ThemeService)
   */
  protected setTheme(theme: 'light' | 'dark'): void {
    this.themeService.setTheme(theme);
  }
}

/**
 * Interface for components that need theme management
 * Provides type safety for theme-related properties
 */
export interface ThemeAware {
  readonly theme: () => 'light' | 'dark';
  readonly isDarkTheme: () => boolean;
  readonly isLightTheme: () => boolean;
  readonly themeClass: () => string;
}

/**
 * Type guard to check if a component implements ThemeAware
 */
export function isThemeAware(component: any): component is ThemeAware {
  return component && 
         typeof component.theme === 'function' &&
         typeof component.isDarkTheme === 'function' &&
         typeof component.isLightTheme === 'function' &&
         typeof component.themeClass === 'function';
}

/**
 * Utility type for theme-dependent configurations
 */
export type ThemeConfig<T> = {
  light: T;
  dark: T;
};

/**
 * Helper function to create theme-dependent configurations
 */
export function createThemeConfig<T>(light: T, dark: T): ThemeConfig<T> {
  return { light, dark };
}

/**
 * Helper function to resolve theme-dependent values
 */
export function resolveThemeValue<T>(
  config: ThemeConfig<T>, 
  theme: 'light' | 'dark'
): T {
  return config[theme];
}
