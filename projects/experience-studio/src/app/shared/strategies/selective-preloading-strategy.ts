import { Injectable } from '@angular/core';
import { PreloadingStrategy, Route } from '@angular/router';
import { Observable, of } from 'rxjs';
import { createLogger } from '../utils';

/**
 * A custom preloading strategy for Angular routes.
 * This strategy allows selective preloading of modules based on route data.
 * 
 * Usage:
 * In your route configuration, add `data: { preload: true }` to routes you want to preload.
 */
@Injectable({ providedIn: 'root' })
export class SelectivePreloadingStrategy implements PreloadingStrategy {
  preloadedModules: string[] = [];
   
  /**
   * Determines whether a route should be preloaded
   * @param route The route to check for preloading
   * @param load The load function to call if preloading is needed
   * @returns An Observable that completes when preloading is done, or null if no preloading is needed
   */
  preload(route: Route, load: () => Observable<any>): Observable<any> {
    if (route.data?.['preload'] && route.path != null) {
      // Add the route path to the preloaded modules list
      this.preloadedModules.push(route.path);
      
      // Log preloading for debugging in development

      // Return the load observable to preload the module
      return load();
    } else {
      // If preload is not set or is false, don't preload
      return of(null);
    }
  }
}
