import {
  HttpRequest,
  HttpHandlerFn,
  HttpEvent,
  HttpResponse,
  HttpContextToken,
  HttpContext,
  HttpInterceptorFn
} from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { tap, share} from 'rxjs';
import { inject } from '@angular/core';
import { HttpCacheService } from '../services/cache/http-cache.service';
import { createLogger } from '../utils/logger';

// Context token to control caching behavior
export const CACHEABLE = new HttpContextToken<boolean>(() => true);

// Context token to set custom cache max age
export const CACHE_MAX_AGE = new HttpContextToken<number>(() => 5 * 60 * 1000); // 5 minutes default

// URLs that should never be cached
const UNCACHEABLE_URLS = [
  '/code-generation/status', // Status endpoints that change frequently
  '/design-generation/status',
  '/wireframe-generation/status'
];

/**
 * Check if a URL should never be cached
 * @param url The URL to check
 * @returns True if the URL should not be cached
 */
const isUncacheableUrl = (url: string): boolean => {
  return UNCACHEABLE_URLS.some(uncacheableUrl => url.includes(uncacheableUrl));
};

/**
 * HTTP interceptor for caching API responses
 */
export const CacheInterceptor: HttpInterceptorFn = (
  request: HttpRequest<unknown>,
  next: HttpHandlerFn
): Observable<HttpEvent<unknown>> => {
  const cacheService = inject(HttpCacheService);
  const logger = createLogger('CacheInterceptor');

  // Skip caching for non-GET requests
  if (request.method !== 'GET') {
    return next(request);
  }

  // Skip caching if explicitly disabled via context
  if (!request.context.get(CACHEABLE)) {
    return next(request);
  }

  // Skip caching for URLs that should never be cached
  if (isUncacheableUrl(request.url)) {
    return next(request);
  }

  // Get the cache max age from context
  const maxAge = request.context.get(CACHE_MAX_AGE);

  // Check if we have a cached response
  const cachedResponse = cacheService.get(request);
  if (cachedResponse) {
    // Return the cached response

    return of(cachedResponse);
  }

  // No cached response, forward request to next handler
  return next(request).pipe(
    // Use share() to prevent multiple subscribers from triggering multiple HTTP requests
    share(),
    tap(event => {
      // Only cache successful responses of type HttpResponse
      if (event instanceof HttpResponse) {

        // Add cache-control header to the response if not present
        if (!event.headers.has('cache-control')) {
          const responseWithCacheControl = event.clone({
            headers: event.headers.set('cache-control', `max-age=${Math.floor(maxAge / 1000)}`)
          });
          cacheService.put(request, responseWithCacheControl, maxAge);
        } else {
          cacheService.put(request, event, maxAge);
        }
      }
    })
  );
}

/**
 * Helper functions to control caching behavior
 */
export const cacheHelpers = {
  /**
   * Disable caching for a request
   */
  disableCache(): HttpContext {
    return new HttpContext().set(CACHEABLE, false);
  },

  /**
   * Set a custom cache max age for a request
   * @param maxAgeMs Max age in milliseconds
   */
  setMaxAge(maxAgeMs: number): HttpContext {
    return new HttpContext().set(CACHE_MAX_AGE, maxAgeMs);
  },

  /**
   * Both disable caching and set a custom max age
   * @param cacheable Whether to enable caching
   * @param maxAgeMs Max age in milliseconds
   */
  withOptions(cacheable: boolean, maxAgeMs?: number): HttpContext {
    const context = new HttpContext().set(CACHEABLE, cacheable);
    if (maxAgeMs !== undefined) {
      context.set(CACHE_MAX_AGE, maxAgeMs);
    }
    return context;
  }
};
