import {
  HttpRequest,
  HttpHandlerFn,
  HttpEvent,
  HttpInterceptorFn,
  HttpHeaders
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { createLogger } from '../utils/logger';

/**
 * HTTP interceptor for handling long-running requests
 * Specifically designed to prevent 499 client timeout errors
 * for wireframe generation APIs
 */
export const LongRequestInterceptor: HttpInterceptorFn = (
  request: HttpRequest<unknown>,
  next: HttpHandlerFn
): Observable<HttpEvent<unknown>> => {
  const logger = createLogger('LongRequestInterceptor');

  // Check if this is a wireframe generation API call
  const isWireframeAPI = isWireframeGenerationRequest(request.url);

  if (isWireframeAPI) {

    // Clone the request with enhanced headers for long-running requests
    const enhancedRequest = request.clone({
      headers: addLongRequestHeaders(request.headers)
    });

    return next(enhancedRequest);
  }

  // For non-wireframe requests, proceed normally
  return next(request);
};

/**
 * Check if the request is for a wireframe generation API
 */
function isWireframeGenerationRequest(url: string): boolean {
  const wireframeEndpoints = [
    '/wireframe-generation/generate',
    '/wireframe-generation/regenerate',
    '/design-generation/design-generation'
  ];

  return wireframeEndpoints.some(endpoint => url.includes(endpoint));
}

/**
 * Add headers optimized for long-running requests
 * Note: Connection and Keep-Alive headers are managed automatically by the browser
 * and cannot be set manually due to security restrictions
 */
function addLongRequestHeaders(existingHeaders: HttpHeaders): HttpHeaders {
  let headers = existingHeaders;

  // Browser automatically manages connection keep-alive, no need to set manually
  // Removed 'Connection' and 'Keep-Alive' headers as they are forbidden by browsers

  // Add cache control to prevent caching of long requests
  if (!headers.has('Cache-Control')) {
    headers = headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
  }

  // Add pragma for HTTP/1.0 compatibility
  if (!headers.has('Pragma')) {
    headers = headers.set('Pragma', 'no-cache');
  }

  // Add expires header
  if (!headers.has('Expires')) {
    headers = headers.set('Expires', '0');
  }

  return headers;
}
