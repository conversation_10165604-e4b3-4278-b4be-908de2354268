@use '../../../../../../public/assets/styles/mixins' as mixins;

// Workspace Shell Styles - Main container for MLO Workspace
.workspace-shell {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: var(--workspace-bg);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;

  // CSS Custom Properties for theming
  --workspace-bg: #fafbfc;
  --workspace-bg-dark: #0d1117;
  --text-primary: #1f2937;
  --text-primary-dark: #f0f6fc;
  --text-secondary: #6b7280;
  --text-secondary-dark: #8b949e;
  --border-light: #e5e7eb;
  --border-light-dark: #30363d;
  --card-bg: #ffffff;
  --card-bg-dark: #161b22;
  --header-bg: #ffffff;
  --header-bg-dark: #21262d;
  --primary-purple: #8c65f7;
  --primary-pink: #e84393;
  --success-green: #10b981;
  --warning-orange: #f59e0b;
  --error-red: #ef4444;

  // Dark theme overrides
  &[data-theme="dark"] {
    --workspace-bg: var(--workspace-bg-dark);
    --text-primary: var(--text-primary-dark);
    --text-secondary: var(--text-secondary-dark);
    --border-light: var(--border-light-dark);
    --card-bg: var(--card-bg-dark);
    --header-bg: var(--header-bg-dark);
  }

  .workspace-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; // Prevent layout shifts
    
    // Smooth transitions for view changes
    transition: all 0.3s ease;
  }

  // Global Loading Overlay
  .global-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    
    .loading-spinner {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
      padding: 2rem;
      background: var(--card-bg);
      border-radius: 0.75rem;
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      
      .spinner {
        width: 2rem;
        height: 2rem;
        border: 2px solid var(--border-light);
        border-top: 2px solid var(--primary-purple);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .loading-text {
        margin: 0;
        color: var(--text-secondary);
        font-size: 0.875rem;
        font-weight: 500;
      }
    }
  }

  // Global Error Toast
  .global-error-toast {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 9998;
    max-width: 400px;
    background: var(--error-red);
    color: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    animation: slideInRight 0.3s ease-out;
    
    .error-content {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 1rem;
      
      .error-message {
        flex: 1;
        font-size: 0.875rem;
        font-weight: 500;
      }
      
      .error-dismiss {
        background: transparent;
        border: none;
        color: white;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 0.25rem;
        transition: background-color 0.2s ease;
        
        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }
        
        &:focus {
          outline: 2px solid rgba(255, 255, 255, 0.5);
          outline-offset: 1px;
        }
      }
    }
  }
}

// Animations
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes slideInRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .workspace-shell {
    .global-error-toast {
      top: 0.5rem;
      right: 0.5rem;
      left: 0.5rem;
      max-width: none;
    }
    
    .global-loading-overlay {
      .loading-spinner {
        margin: 1rem;
        padding: 1.5rem;
      }
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .workspace-shell {
    --border-light: #000000;
    --border-light-dark: #ffffff;
    
    .global-loading-overlay .loading-spinner .spinner {
      border-width: 3px;
    }
    
    .global-error-toast {
      border: 2px solid #ffffff;
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .workspace-shell {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}
