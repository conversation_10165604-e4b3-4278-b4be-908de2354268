import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FileAttachPillComponent } from './file-attach-pill.component';
import { By } from '@angular/platform-browser';
import { IconsComponent } from '../icons/icons.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('FileAttachPillComponent', () => {
  let component: FileAttachPillComponent;
  let fixture: ComponentFixture<FileAttachPillComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FileAttachPillComponent, IconsComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA] // Add this to handle custom elements
    }).compileComponents();

    fixture = TestBed.createComponent(FileAttachPillComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.mainIcon).toBe('awe_attach_file');
    expect(component.mainText).toBe('Attach File');
    expect(component.currentTheme).toBe('light');
    expect(component.options).toEqual([
      { name: 'From Computer', icon: 'awe_upload', value: 'computer' },
      { name: 'From Cloud', icon: 'awe_cloud_upload', value: 'cloud' },
      { name: 'From URL', icon: 'awe_link', value: 'url' }
    ]);
    expect(component.isHovered()).toBeFalse();
    expect(component.isDropdownOpen()).toBeFalse();
  });

  it('should show text and arrow when hovered', () => {
    // Initial state - text and arrow should be hidden
    let textElement = fixture.debugElement.query(By.css('.text'));
    let arrowElement = fixture.debugElement.query(By.css('.arrow'));
    expect(textElement).toBeNull();
    expect(arrowElement).toBeNull();

    // Trigger hover
    const container = fixture.debugElement.query(By.css('.file-attach-pill-container'));
    container.triggerEventHandler('mouseenter', null);
    fixture.detectChanges();

    // Text and arrow should be visible
    textElement = fixture.debugElement.query(By.css('.text'));
    arrowElement = fixture.debugElement.query(By.css('.arrow'));
    expect(textElement).toBeTruthy();
    expect(arrowElement).toBeTruthy();
    expect(textElement.nativeElement.textContent.trim()).toBe('Attach File');
  });

  it('should toggle dropdown on button click', () => {
    const button = fixture.debugElement.query(By.css('.file-attach-pill'));
    const dropdown = fixture.debugElement.query(By.css('.dropdown'));
    
    // Initially dropdown should not be shown
    expect(dropdown.classes['show']).toBeFalsy();
    
    // Click to open
    button.triggerEventHandler('click', { stopPropagation: () => {} });
    fixture.detectChanges();
    expect(component.isDropdownOpen()).toBeTrue();
    
    // Click to close
    button.triggerEventHandler('click', { stopPropagation: () => {} });
    fixture.detectChanges();
    expect(component.isDropdownOpen()).toBeFalse();
  });

  it('should emit selected option', () => {
    const spy = spyOn(component.optionSelected, 'emit');
    const option = component.options[0];
    
    // Open dropdown
    component.isDropdownOpen.set(true);
    fixture.detectChanges();
    
    // Click first option
    const dropdownItem = fixture.debugElement.query(By.css('.dropdown-item'));
    dropdownItem.triggerEventHandler('click', { stopPropagation: () => {} });
    
    expect(spy).toHaveBeenCalledWith(option);
    expect(component.isDropdownOpen()).toBeFalse();
    expect(component.isHovered()).toBeFalse();
  });

  it('should handle keyboard navigation', () => {
    const spy = spyOn(component.optionSelected, 'emit');
    const option = component.options[0];
    
    // Open dropdown
    component.isDropdownOpen.set(true);
    fixture.detectChanges();
    
    // Test enter key
    const dropdownItem = fixture.debugElement.query(By.css('.dropdown-item'));
    dropdownItem.triggerEventHandler('keydown.enter', { stopPropagation: () => {} });
    expect(spy).toHaveBeenCalledWith(option);
    
    // Test space key
    dropdownItem.triggerEventHandler('keydown.space', { stopPropagation: () => {} });
    expect(spy).toHaveBeenCalledWith(option);
  });

  it('should close dropdown when clicking outside', () => {
    // Open dropdown
    component.isDropdownOpen.set(true);
    fixture.detectChanges();
    expect(component.isDropdownOpen()).toBeTrue();
    
    // Simulate click outside
    document.dispatchEvent(new MouseEvent('click'));
    fixture.detectChanges();
    
    expect(component.isDropdownOpen()).toBeFalse();
  });

  it('should handle mouse enter/leave on container', () => {
    // Trigger hover on container
    const container = fixture.debugElement.query(By.css('.file-attach-pill-container'));
    
    // Test mouse enter
    container.triggerEventHandler('mouseenter', null);
    fixture.detectChanges();
    expect(component.isHovered()).toBeTrue();
    
    // Test mouse leave
    container.triggerEventHandler('mouseleave', null);
    fixture.detectChanges();
    expect(component.isHovered()).toBeFalse();
  });

  it('should handle mouse enter/leave on dropdown', () => {
    // Open dropdown
    component.isDropdownOpen.set(true);
    fixture.detectChanges();
    
    const dropdown = fixture.debugElement.query(By.css('.dropdown'));
    
    // Test mouse enter on dropdown
    dropdown.triggerEventHandler('mouseenter', null);
    fixture.detectChanges();
    expect(component.isMouseOverDropdown).toBeTrue();
    
    // Test mouse leave on dropdown
    dropdown.triggerEventHandler('mouseleave', null);
    fixture.detectChanges();
    expect(component.isMouseOverDropdown).toBeFalse();
    expect(component.isDropdownOpen()).toBeFalse();
  });



  it('should apply correct theme classes to host element', () => {
    const hostElement = fixture.debugElement.nativeElement;
    
    // Light theme (default)
    expect(hostElement.classList.contains('theme-light')).toBeTrue();
    expect(hostElement.classList.contains('theme-dark')).toBeFalse();

    // Dark theme
    component.currentTheme = 'dark';
    fixture.detectChanges();
    
    expect(hostElement.classList.contains('theme-dark')).toBeTrue();
    expect(hostElement.classList.contains('theme-light')).toBeFalse();
  });
});