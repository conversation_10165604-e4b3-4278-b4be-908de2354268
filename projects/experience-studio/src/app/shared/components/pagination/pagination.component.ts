import { Component, ChangeDetectionStrategy, input, output, computed } from '@angular/core';
import { CommonModule } from '@angular/common';

/**
 * Pagination Component - Provides navigation for paginated content
 * Supports various pagination patterns with accessibility features
 * 
 * Features:
 * - Previous/Next navigation
 * - Page number buttons
 * - Jump to first/last page
 * - Responsive design
 * - Accessibility compliant
 * - Customizable page size
 */
@Component({
  selector: 'app-pagination',
  standalone: true,
  imports: [CommonModule],
  template: `
    <nav class="pagination" role="navigation" aria-label="Pagination navigation">
      @if (totalPages() > 1) {
        <div class="pagination-info">
          <span class="pagination-text">
            Showing {{ startItem() }}-{{ endItem() }} of {{ totalItems() }} results
          </span>
        </div>
        
        <div class="pagination-controls">
          <!-- Previous Button -->
          <button
            class="pagination-btn prev-btn"
            [disabled]="currentPage() === 1"
            (click)="onPageChange(currentPage() - 1)"
            [attr.aria-label]="'Go to previous page, page ' + (currentPage() - 1)">
            <i class="fas fa-chevron-left" aria-hidden="true"></i>
            <span class="btn-text">Previous</span>
          </button>
          
          <!-- Page Numbers -->
          <div class="page-numbers">
            <!-- First Page -->
            @if (showFirstPage()) {
              <button
                class="pagination-btn page-btn"
                [class.active]="currentPage() === 1"
                (click)="onPageChange(1)"
                [attr.aria-label]="'Go to page 1'"
                [attr.aria-current]="currentPage() === 1 ? 'page' : null">
                1
              </button>
              
              @if (showFirstEllipsis()) {
                <span class="pagination-ellipsis" aria-hidden="true">...</span>
              }
            }
            
            <!-- Visible Page Range -->
            @for (page of visiblePages(); track page) {
              <button
                class="pagination-btn page-btn"
                [class.active]="currentPage() === page"
                (click)="onPageChange(page)"
                [attr.aria-label]="'Go to page ' + page"
                [attr.aria-current]="currentPage() === page ? 'page' : null">
                {{ page }}
              </button>
            }
            
            <!-- Last Page -->
            @if (showLastPage()) {
              @if (showLastEllipsis()) {
                <span class="pagination-ellipsis" aria-hidden="true">...</span>
              }
              
              <button
                class="pagination-btn page-btn"
                [class.active]="currentPage() === totalPages()"
                (click)="onPageChange(totalPages())"
                [attr.aria-label]="'Go to page ' + totalPages()"
                [attr.aria-current]="currentPage() === totalPages() ? 'page' : null">
                {{ totalPages() }}
              </button>
            }
          </div>
          
          <!-- Next Button -->
          <button
            class="pagination-btn next-btn"
            [disabled]="currentPage() === totalPages()"
            (click)="onPageChange(currentPage() + 1)"
            [attr.aria-label]="'Go to next page, page ' + (currentPage() + 1)">
            <span class="btn-text">Next</span>
            <i class="fas fa-chevron-right" aria-hidden="true"></i>
          </button>
        </div>
        
        <!-- Page Size Selector -->
        @if (showPageSizeSelector()) {
          <div class="page-size-selector">
            <label for="page-size-select" class="page-size-label">Items per page:</label>
            <select
              id="page-size-select"
              class="page-size-select"
              [value]="itemsPerPage()"
              (change)="onPageSizeChange($event)">
              @for (size of pageSizeOptions(); track size) {
                <option [value]="size">{{ size }}</option>
              }
            </select>
          </div>
        }
      }
    </nav>
  `,
  styleUrls: ['./pagination.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PaginationComponent {
  // Input properties
  readonly currentPage = input.required<number>();
  readonly totalPages = input.required<number>();
  readonly totalItems = input.required<number>();
  readonly itemsPerPage = input<number>(12);
  readonly maxVisiblePages = input<number>(5);
  readonly showPageSizeSelector = input<boolean>(false);
  readonly pageSizeOptions = input<number[]>([6, 12, 24, 48]);

  // Output events
  readonly pageChange = output<number>();
  readonly pageSizeChange = output<number>();

  // Computed properties
  readonly startItem = computed(() => 
    (this.currentPage() - 1) * this.itemsPerPage() + 1
  );

  readonly endItem = computed(() => 
    Math.min(this.currentPage() * this.itemsPerPage(), this.totalItems())
  );

  readonly visiblePages = computed(() => {
    const current = this.currentPage();
    const total = this.totalPages();
    const maxVisible = this.maxVisiblePages();
    
    if (total <= maxVisible) {
      return Array.from({ length: total }, (_, i) => i + 1);
    }
    
    const half = Math.floor(maxVisible / 2);
    let start = Math.max(1, current - half);
    let end = Math.min(total, start + maxVisible - 1);
    
    // Adjust start if we're near the end
    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1);
    }
    
    // Don't show first/last page in visible range if they're shown separately
    if (this.showFirstPage() && start === 1) {
      start = 2;
    }
    if (this.showLastPage() && end === total) {
      end = total - 1;
    }
    
    const pages = [];
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
    
    return pages;
  });

  readonly showFirstPage = computed(() => {
    const visiblePages = this.visiblePages();
    return visiblePages.length > 0 && visiblePages[0] > 1;
  });

  readonly showLastPage = computed(() => {
    const visiblePages = this.visiblePages();
    const total = this.totalPages();
    return visiblePages.length > 0 && visiblePages[visiblePages.length - 1] < total;
  });

  readonly showFirstEllipsis = computed(() => {
    const visiblePages = this.visiblePages();
    return visiblePages.length > 0 && visiblePages[0] > 2;
  });

  readonly showLastEllipsis = computed(() => {
    const visiblePages = this.visiblePages();
    const total = this.totalPages();
    return visiblePages.length > 0 && visiblePages[visiblePages.length - 1] < total - 1;
  });

  /**
   * Handle page change
   */
  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages() && page !== this.currentPage()) {
      this.pageChange.emit(page);
    }
  }

  /**
   * Handle page size change
   */
  onPageSizeChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    const newSize = parseInt(target.value, 10);
    this.pageSizeChange.emit(newSize);
  }

  /**
   * Handle keyboard navigation
   */
  onKeyDown(event: KeyboardEvent, page: number): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.onPageChange(page);
    }
  }
}
