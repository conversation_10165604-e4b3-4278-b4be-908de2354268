<!-- recent-creation.component.html -->

<div id="recent-creation-wrapper" class="card align-items-center gap-5">
   @if (hasError) {
      <!-- Error state - display: none text -->
      <div class="error-message-container">
        <div class="error-message">Unable to fetch your projects. Please try again later.</div>
      </div>}
      @else {
  <!-- Toggle buttons for Recent/All view -->
  <div class="d-flex p-2 toggle-layout">
    <div class="d-flex align-items-center justify-content-around w-100">
      <button
        class="toggle-btn"
        [ngClass]="{ active: currentCategory === 'recent' }"
        (click)="switchCategory('recent')">
        Recent
      </button>
      <button
        class="toggle-btn"
        [ngClass]="{ active: currentCategory === 'all' }"
        (click)="switchCategory('all')">
        All
      </button>
    </div>
  </div>

  <!-- Cards grid container with slide animation -->
  <div id="cards-container">

      <div
        class="cards-grid row g-4 px-4"
        [ngClass]="{
          'slide-recent': currentCategory === 'recent',
          'slide-all': currentCategory === 'all',
        }">
        @for (option of getCurrentOptions(); track trackByFn($index, option)) {
          <div class="col-12 col-sm-6 col-lg-4 col-xl-3">
            <awe-cards
              [ngClass]="{
                'skeleton-card': isLoading,
                'project-loading': isProjectLoading(option.id),
                'health-check-loading': isHealthCheckLoading(option.id),
                'project-error': getProjectError(option.id),
                'project-selected': selectedId === option.id,
                'project-hovered': isCardHovered(option.id)
              }"
              (click)="handleSelection(option.id)"
              (mouseenter)="onCardMouseEnter(option.id)"
              (mouseleave)="onCardMouseLeave()"
              style="cursor: pointer; position: relative;"
              [attr.aria-busy]="isProjectLoading(option.id)"
              [attr.aria-describedby]="getProjectError(option.id) ? 'error-' + option.id : null">

              <!-- Health Check Overlay Loader -->
              @if (isHealthCheckLoading(option.id)) {
                <div class="health-check-overlay"
                     [attr.aria-label]="'Checking service health for ' + option.heading"
                     role="status">
                  <div class="health-check-loader">
                    <div class="health-check-spinner"></div>
                    <div class="health-check-text">Checking services...</div>
                  </div>
                </div>
              }

              <div content class="card p-3 gap-2 card-content">
                @if (isLoading) {
                  <!-- Skeleton loader -->
                  <div class="skeleton-title"></div>
                  <div class="skeleton-text"></div>
                  <div class="skeleton-text"></div>
                  <div class="skeleton-text"></div>
                } @else {
                  <!-- Actual content -->
                  <div class="d-flex align-items-start justify-content-between mb-2">
                    <awe-heading variant="s1" type="bold" id="title" class="flex-grow-1 me-2">{{ option.heading }}</awe-heading>
                    @if (getProjectType(option.type)) {
                      <app-project-type-tag
                        [projectType]="getProjectType(option.type)"
                        size="small"
                        variant="filled">
                      </app-project-type-tag>
                    }
                  </div>
                  <awe-body-text>{{ option.description }}</awe-body-text>
                  <div
                    class="d-flex align-items-center justify-content-between pt-2 action-timestamp-container">
                    <div
                      class="cursor-pointer p-2 card-action-btn"
                      [class.disabled]="isProjectLoading(option.id)"
                      [title]="option.actionText || getDefaultActionText(option.type)">
                      {{ option.actionText || getDefaultActionText(option.type) }}
                    </div>
                    <div class="timestamp" [title]="option.timestamp">{{ option.timestamp }}</div>
                  </div>
                }
              </div>
            </awe-cards>
          </div>
      } @empty {
        <div class="col-12">
          <div class="no-projects-placeholder d-flex flex-column align-items-center justify-content-center p-5">
            <div class="placeholder-icon mb-3">📂</div>
            <h3 class="placeholder-title mb-2">No Projects Found</h3>
            <p class="placeholder-description text-center">
              @if (currentCategory === 'recent') {
                You haven't created any projects recently. Start building something amazing!
              } @else {
                No projects available at the moment. Create your first project to get started.
              }
            </p>
          </div>
        </div>
      }
    </div>

  </div>
}
</div>
