import { 
  Component, 
  Input, 
  Output, 
  EventEmitter, 
  ChangeDetectionStrategy,
  signal,
  computed
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { 
  FilterState, 
  SortOption, 
  SORT_OPTIONS, 
  PROJECT_TYPES, 
  ProjectType 
} from '../../models/recent-creation.model';

/**
 * Filter panel component for project filtering and sorting
 * Provides project type filters and sorting options
 */
@Component({
  selector: 'app-project-filter-panel',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="filter-panel" [class.expanded]="isExpanded()">
      <!-- Filter Header -->
      <div class="filter-header">
        <div class="filter-title">
          <i class="fas fa-filter"></i>
          <span>Filters</span>
          @if (activeFilterCount() > 0) {
            <span class="filter-count">{{ activeFilterCount() }}</span>
          }
        </div>
        <div class="filter-actions">
          @if (activeFilterCount() > 0) {
            <button 
              type="button" 
              class="clear-filters-btn"
              (click)="clearAllFilters()"
              [attr.aria-label]="'Clear all filters'">
              <i class="fas fa-times"></i>
              Clear
            </button>
          }
          <button 
            type="button" 
            class="toggle-btn"
            (click)="toggleExpanded()"
            [attr.aria-label]="isExpanded() ? 'Collapse filters' : 'Expand filters'"
            [attr.aria-expanded]="isExpanded()">
            <i class="fas" [class.fa-chevron-up]="isExpanded()" [class.fa-chevron-down]="!isExpanded()"></i>
          </button>
        </div>
      </div>

      <!-- Filter Content -->
      @if (isExpanded()) {
        <div class="filter-content">
          <!-- Sort Options -->
          <div class="filter-section">
            <label class="filter-section-label" for="sort-select">Sort by</label>
            <select 
              id="sort-select"
              class="sort-select"
              [value]="filterState.sortBy"
              (change)="onSortChange($event)"
              [attr.aria-label]="'Sort projects by'">
              @for (option of sortOptions; track option.value) {
                <option [value]="option.value">{{ option.label }}</option>
              }
            </select>
          </div>

          <!-- Project Type Filters -->
          <div class="filter-section">
            <div class="filter-section-label">Project Types</div>
            <div class="checkbox-group">
              @for (projectType of availableProjectTypes; track projectType.value) {
                <label class="checkbox-item">
                  <input
                    type="checkbox"
                    [checked]="isProjectTypeSelected(projectType.value)"
                    (change)="onProjectTypeToggle(projectType.value)"
                    [attr.aria-label]="'Filter by ' + projectType.label"
                  />
                  <span class="checkbox-custom"></span>
                  <span class="checkbox-label">
                    <span 
                      class="project-type-indicator"
                      [style.background-color]="projectType.color">
                    </span>
                    {{ projectType.label }}
                  </span>
                </label>
              }
            </div>
          </div>

          <!-- Filter Summary -->
          @if (activeFilterCount() > 0) {
            <div class="filter-summary">
              <div class="summary-title">Active Filters:</div>
              <div class="summary-items">
                @if (filterState.searchTerm) {
                  <span class="summary-item">
                    Search: "{{ filterState.searchTerm }}"
                  </span>
                }
                @for (typeValue of filterState.selectedProjectTypes; track typeValue) {
                  <span class="summary-item">
                    Type: {{ getProjectTypeLabel(typeValue) }}
                  </span>
                }
              </div>
            </div>
          }
        </div>
      }
    </div>
  `,
  styleUrls: ['./project-filter-panel.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProjectFilterPanelComponent {
  @Input() filterState: FilterState = {
    searchTerm: '',
    selectedProjectTypes: [],
    sortBy: 'modified-desc',
    showFilters: false
  };
  
  @Input() resultCount: number | null = null;
  
  @Output() filterChange = new EventEmitter<Partial<FilterState>>();
  @Output() clearFilters = new EventEmitter<void>();
  
  // Component state
  private readonly _isExpanded = signal<boolean>(false);
  readonly isExpanded = this._isExpanded.asReadonly();
  
  // Available options
  readonly sortOptions = SORT_OPTIONS;
  readonly availableProjectTypes = Object.values(PROJECT_TYPES);
  
  // Computed properties
  readonly activeFilterCount = computed(() => {
    let count = 0;
    if (this.filterState.searchTerm?.trim()) count++;
    if (this.filterState.selectedProjectTypes?.length > 0) count++;
    return count;
  });
  
  /**
   * Toggle filter panel expansion
   */
  toggleExpanded(): void {
    this._isExpanded.set(!this._isExpanded());
  }
  
  /**
   * Set filter panel expansion state
   */
  setExpanded(expanded: boolean): void {
    this._isExpanded.set(expanded);
  }
  
  /**
   * Handle sort option change
   */
  onSortChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    const sortBy = target.value as SortOption;
    this.filterChange.emit({ sortBy });
  }
  
  /**
   * Handle project type filter toggle
   */
  onProjectTypeToggle(projectType: string): void {
    const current = this.filterState.selectedProjectTypes || [];
    const index = current.indexOf(projectType);
    
    let selectedProjectTypes: string[];
    if (index === -1) {
      selectedProjectTypes = [...current, projectType];
    } else {
      selectedProjectTypes = current.filter(type => type !== projectType);
    }
    
    this.filterChange.emit({ selectedProjectTypes });
  }
  
  /**
   * Check if project type is selected
   */
  isProjectTypeSelected(projectType: string): boolean {
    return this.filterState.selectedProjectTypes?.includes(projectType) || false;
  }
  
  /**
   * Get project type label by value
   */
  getProjectTypeLabel(value: string): string {
    return PROJECT_TYPES[value]?.label || value;
  }
  
  /**
   * Clear all filters
   */
  clearAllFilters(): void {
    this.clearFilters.emit();
  }
}
