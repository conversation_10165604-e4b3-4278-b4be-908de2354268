import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';
import { createLogger } from '../../../utils/logger';
import { UIDesignCanvasService } from './ui-design-canvas.service';
import { UIDesignNodeService } from './ui-design-node.service';
import { UIDesignMultiPageAlignmentService } from './ui-design-multi-page-alignment.service';

export interface ViewportStats {
  totalNodes: number;
  visibleNodes: number;
  selectedNodes: number;
  canvasZoom: number;
}

export interface ViewportDebugInfo {
  viewport: {
    x: number;
    y: number;
    zoom: number;
  };
  container: {
    width: number;
    height: number;
    centerX: number;
    centerY: number;
  };
  content: {
    bounds: {
      minX: number;
      minY: number;
      maxX: number;
      maxY: number;
      width: number;
      height: number;
    };
    centerX: number;
    centerY: number;
    width: number;
    height: number;
  };
  viewportBounds: {
    left: number;
    top: number;
    right: number;
    bottom: number;
  };
  nodes: {
    total: number;
    visible: number;
    positions: Array<{
      id: string;
      x: number;
      y: number;
      width: number;
      height: number;
      inViewport: boolean;
    }>;
  };
  formula: {
    offsetX: number;
    offsetY: number;
    topPadding: number;
    finalOffsetY: number;
  };
}

export interface MinimapConfig {
  enabled: boolean;
  width: number;
  height: number;
  scale: number;
  position: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
}

export interface ContainerSize {
  width: number;
  height: number;
}

@Injectable({
  providedIn: 'root'
})
export class UIDesignViewportService {

  private readonly containerSize$ = new BehaviorSubject<ContainerSize>({ width: 0, height: 0 });

  private readonly minimapConfig$ = new BehaviorSubject<MinimapConfig>({
    enabled: false,
    width: 200,
    height: 150,
    scale: 0.1,
    position: 'bottom-right'
  });

  private readonly viewportStats$: Observable<ViewportStats>;

  constructor(
    private canvasService: UIDesignCanvasService,
    private nodeService: UIDesignNodeService,
    private multiPageAlignmentService: UIDesignMultiPageAlignmentService
  ) {

    this.viewportStats$ = combineLatest([
      this.nodeService.nodes,
      this.nodeService.selectedNodes,
      this.canvasService.viewport
    ]).pipe(
      map(([nodes, selectedNodes, viewport]) => ({
        totalNodes: nodes.length,
        visibleNodes: nodes.filter(node => node.visible).length,
        selectedNodes: selectedNodes.length,
        canvasZoom: viewport.zoom
      }))
    );
  }

  get containerSize(): Observable<ContainerSize> {
    return this.containerSize$.asObservable();
  }

  get minimapConfig(): Observable<MinimapConfig> {
    return this.minimapConfig$.asObservable();
  }

  get viewportStats(): Observable<ViewportStats> {
    return this.viewportStats$;
  }

  updateContainerSize(width: number, height: number): void {
    this.containerSize$.next({ width, height });
  }

  toggleMinimap(): void {
    const current = this.minimapConfig$.value;
    this.minimapConfig$.next({
      ...current,
      enabled: !current.enabled
    });
  }

  enableMinimap(): void {
    const current = this.minimapConfig$.value;
    this.minimapConfig$.next({
      ...current,
      enabled: true
    });
  }

  disableMinimap(): void {
    const current = this.minimapConfig$.value;
    this.minimapConfig$.next({
      ...current,
      enabled: false
    });
  }

  updateMinimapConfig(updates: Partial<MinimapConfig>): void {
    const current = this.minimapConfig$.value;
    this.minimapConfig$.next({
      ...current,
      ...updates
    });
  }

  fitContentToView(): void {
    const containerSize = this.containerSize$.value;
    const contentBounds = this.nodeService.getContentBounds();

    if (contentBounds.width > 0 && contentBounds.height > 0) {
      this.canvasService.fitContentToView(contentBounds, containerSize);
    }
  }

  centerViewOnNodes(): void {
    const nodes = this.nodeService.getNodes();

    if (nodes.length === 0) {

      this.multiPageAlignmentService.applyReferenceViewport();
      return;
    }

    this.multiPageAlignmentService.autoArrangePages();

    const stats = this.multiPageAlignmentService.getAlignmentStats();

  }

  isPointInViewport(x: number, y: number): boolean {
    const viewport = this.canvasService.getViewport();
    const containerSize = this.containerSize$.value;

    const screenX = x * viewport.zoom + viewport.x;
    const screenY = y * viewport.zoom + viewport.y;

    return screenX >= 0 &&
           screenX <= containerSize.width &&
           screenY >= 0 &&
           screenY <= containerSize.height;
  }

  isNodeInViewport(nodeId: string): boolean {
    const node = this.nodeService.getNodeById(nodeId);
    if (!node) return false;

    const viewport = this.canvasService.getViewport();
    const containerSize = this.containerSize$.value;

    const nodeLeft = node.position.x * viewport.zoom + viewport.x;
    const nodeTop = node.position.y * viewport.zoom + viewport.y;
    const nodeRight = nodeLeft + node.data.width * viewport.zoom;
    const nodeBottom = nodeTop + node.data.height * viewport.zoom;

    return !(nodeRight < 0 ||
             nodeLeft > containerSize.width ||
             nodeBottom < 0 ||
             nodeTop > containerSize.height);
  }

  getVisibleNodes(): string[] {
    const nodes = this.nodeService.getNodes();
    return nodes
      .filter(node => node.visible && this.isNodeInViewport(node.id))
      .map(node => node.id);
  }

  updateNodeVisibility(): void {
    const nodes = this.nodeService.getNodes();

    nodes.forEach(node => {
      const isVisible = this.isNodeInViewport(node.id);
      if (node.visible !== isVisible) {
        this.nodeService.updateNode(node.id, { visible: isVisible });
      }
    });
  }

  getMinimapViewportRect(): { x: number; y: number; width: number; height: number } {
    const viewport = this.canvasService.getViewport();
    const containerSize = this.containerSize$.value;
    const minimapConfig = this.minimapConfig$.value;

    return {
      x: -viewport.x * minimapConfig.scale / viewport.zoom,
      y: -viewport.y * minimapConfig.scale / viewport.zoom,
      width: containerSize.width * minimapConfig.scale / viewport.zoom,
      height: containerSize.height * minimapConfig.scale / viewport.zoom
    };
  }

  onMinimapClick(clickX: number, clickY: number, minimapRect: DOMRect): void {
    const minimapConfig = this.minimapConfig$.value;
    const containerSize = this.containerSize$.value;
    const viewport = this.canvasService.getViewport();

    const relativeX = (clickX - minimapRect.left) / minimapRect.width;
    const relativeY = (clickY - minimapRect.top) / minimapRect.height;

    const canvasX = relativeX * minimapConfig.width / minimapConfig.scale;
    const canvasY = relativeY * minimapConfig.height / minimapConfig.scale;

    const newX = containerSize.width / 2 - canvasX * viewport.zoom;
    const newY = containerSize.height / 2 - canvasY * viewport.zoom;

    this.canvasService.updateViewport({
      x: newX,
      y: newY
    });

  }

  getCurrentStats(): ViewportStats {
    const nodes = this.nodeService.getNodes();
    const selectedNodes = this.nodeService.getSelectedNodeIds();
    const viewport = this.canvasService.getViewport();

    return {
      totalNodes: nodes.length,
      visibleNodes: nodes.filter(node => node.visible).length,
      selectedNodes: selectedNodes.length,
      canvasZoom: viewport.zoom
    };
  }

  getViewportDebugInfo(): ViewportDebugInfo {
    const containerSize = this.containerSize$.value;
    const contentBounds = this.nodeService.getContentBounds();
    const viewport = this.canvasService.getViewport();
    const nodes = this.nodeService.getNodes();

    const containerCenterX = containerSize.width / 2;
    const containerCenterY = containerSize.height / 2;
    const contentCenterX = (contentBounds.minX + contentBounds.maxX) / 2;
    const contentCenterY = (contentBounds.minY + contentBounds.maxY) / 2;

    const viewportBounds = {
      left: (-viewport.x) / viewport.zoom,
      top: (-viewport.y) / viewport.zoom,
      right: (containerSize.width - viewport.x) / viewport.zoom,
      bottom: (containerSize.height - viewport.y) / viewport.zoom
    };

    return {
      viewport: {
        x: viewport.x,
        y: viewport.y,
        zoom: viewport.zoom
      },
      container: {
        width: containerSize.width,
        height: containerSize.height,
        centerX: containerCenterX,
        centerY: containerCenterY
      },
      content: {
        bounds: contentBounds,
        centerX: contentCenterX,
        centerY: contentCenterY,
        width: contentBounds.maxX - contentBounds.minX,
        height: contentBounds.maxY - contentBounds.minY
      },
      viewportBounds,
      nodes: {
        total: nodes.length,
        visible: nodes.filter(node => this.isNodeInViewport(node.id)).length,
        positions: nodes.map(node => ({
          id: node.id,
          x: node.position.x,
          y: node.position.y,
          width: node.data.width,
          height: node.data.height,
          inViewport: this.isNodeInViewport(node.id)
        }))
      },
      formula: {
        offsetX: containerCenterX - contentCenterX,
        offsetY: containerCenterY - contentCenterY,
        topPadding: 40,
        finalOffsetY: containerCenterY - contentCenterY - 40
      }
    };
  }

  moveViewportToBottom(): void {
    const containerSize = this.containerSize$.value;
    const contentBounds = this.nodeService.getContentBounds();

    if (contentBounds.width > 0 && contentBounds.height > 0) {

      const containerCenterX = containerSize.width / 2;
      const contentCenterX = (contentBounds.minX + contentBounds.maxX) / 2;
      const offsetX = containerCenterX - contentCenterX;

      const bottomPadding = 100;
      const offsetY = containerSize.height - contentBounds.maxY - bottomPadding;

      this.canvasService.updateViewport({
        x: offsetX,
        y: offsetY
      });

    }
  }

  resetViewport(): void {

    this.canvasService.updateViewport({
      x: 39.86,
      y: 82.95,
      zoom: 0.5
    });
  }

  alignMultiplePages(): void {
    this.multiPageAlignmentService.alignMultiplePages();
    const stats = this.multiPageAlignmentService.getAlignmentStats();

  }

  getMultiPageStats(): any {
    return this.multiPageAlignmentService.getAlignmentStats();
  }

  isAtReferenceViewport(): boolean {
    return this.multiPageAlignmentService.isAtReferenceViewport();
  }

  applyReferenceViewport(): void {
    this.multiPageAlignmentService.applyReferenceViewport();
  }

  clear(): void {
    this.containerSize$.next({ width: 0, height: 0 });
    this.minimapConfig$.next({
      enabled: false,
      width: 200,
      height: 150,
      scale: 0.1,
      position: 'bottom-right'
    });
  }
}
