import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { CodeWindowUIDesignStateService } from './code-window-ui-design-state.service';

export interface RegenerationPayload {
  prompt: string;
  image: string[];
}

export interface RegenerationSession {
  aiMessageId: string;
  timestamp: number;
  isActive: boolean;
  sessionId?: string;
  messageId?: string;
  prompt?: string;
  selectedNodes?: any[];
}

export interface RegenerationCallbacks {
  validateAndResetRegenerationState: (reason: string) => void;
  clearRegenerationTimeout: () => void;
  cleanupLegacyEditMessages: () => void;
  createLoadingNodesForRegeneration: (nodes: any[]) => void;
  generateRegenerationSessionId: () => string;
  handleEditSuccess: (result: any, nodes: any[]) => void;
  handleEditFailure: (message: string) => void;
  handleCodeRegeneration: (payload: RegenerationPayload) => void;
  toastError: (message: string) => void;
  setupIntroMessageStateSubscriptionForRegeneration?: (targetMessageId: string | null) => void;
}

export interface RegenerationServices {
  uiDesignSelectionService: any;
  uiDesignEditService: any;
  uiDesignIntroService: any;
}

export interface RegenerationState {
  isInProgress: boolean;
  currentSession: RegenerationSession | null;
  activeSessions: Map<string, RegenerationSession>;
  activeMessageIds: Set<string>;
  currentActiveMessageId: string | null;
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowRegenerationPayloadService {
  private regenerationState$ = new BehaviorSubject<RegenerationState>({
    isInProgress: false,
    currentSession: null,
    activeSessions: new Map(),
    activeMessageIds: new Set(),
    currentActiveMessageId: null
  });

  // Events
  private regenerationStarted$ = new Subject<{ session: RegenerationSession; payload: RegenerationPayload }>();
  private regenerationCompleted$ = new Subject<{ session: RegenerationSession; success: boolean }>();
  private regenerationFailed$ = new Subject<{ session: RegenerationSession; error: any }>();

  // Public observables
  readonly regenerationState = this.regenerationState$.asObservable();
  readonly regenerationStarted = this.regenerationStarted$.asObservable();
  readonly regenerationCompleted = this.regenerationCompleted$.asObservable();
  readonly regenerationFailed = this.regenerationFailed$.asObservable();

  constructor(
    private uiDesignStateService: CodeWindowUIDesignStateService
  ) {}

  // Handle regeneration payload
  handleRegenerationPayload(
    payload: RegenerationPayload,
    callbacks: RegenerationCallbacks,
    services: RegenerationServices,
    state: {
      isRegenerationCallInProgress: boolean;
      lightMessages: any[];
      activeRegenerationSessions: Map<string, RegenerationSession>;
      activeAIMessageIds: Set<string>;
      currentActiveMessageId: string | null;
    }
  ): void {
    callbacks.validateAndResetRegenerationState('New regeneration request');

    if (state.isRegenerationCallInProgress) {
      return;
    }

    callbacks.clearRegenerationTimeout();

    if (this.uiDesignStateService.getUIDesignMode()) {
      this.handleUIDesignRegeneration(payload, callbacks, services, state);
    } else {
      callbacks.handleCodeRegeneration(payload);
    }
  }

  // Handle UI Design regeneration
  private handleUIDesignRegeneration(
    payload: RegenerationPayload,
    callbacks: RegenerationCallbacks,
    services: RegenerationServices,
    state: {
      lightMessages: any[];
      activeRegenerationSessions: Map<string, RegenerationSession>;
      activeAIMessageIds: Set<string>;
      currentActiveMessageId: string | null;
    }
  ): void {
    const selectedNodes = services.uiDesignSelectionService.getSelectedNodes();
    if (selectedNodes.length === 0) {
      callbacks.toastError('Please select one or more pages to edit');
      return;
    }

    callbacks.cleanupLegacyEditMessages();
    callbacks.createLoadingNodesForRegeneration(selectedNodes);

    // Set UI Design state
    services.uiDesignSelectionService.setEditingInProgress(true);
    this.uiDesignStateService.setUIDesignRegenerating(true);
    this.uiDesignStateService.setUIDesignLoading(true);

    const sessionId = callbacks.generateRegenerationSessionId();
    const aiMessageId = `ai-regeneration-${sessionId}`;

    // Create AI message
    state.lightMessages.push({
      id: aiMessageId,
      text: '',
      from: 'ai',
      theme: 'light',
      showLoadingIndicator: true,
      loadingPhase: 'intro',
      mainAPIInProgress: true,
    });

    // Create session
    const session: RegenerationSession = {
      aiMessageId: aiMessageId,
      timestamp: Date.now(),
      isActive: true,
      sessionId,
      prompt: payload.prompt,
      selectedNodes: [...selectedNodes],
    };

    state.activeRegenerationSessions.set(sessionId, session);
    state.activeAIMessageIds.add(aiMessageId);
    state.currentActiveMessageId = aiMessageId;

    // Update state
    this.regenerationState$.next({
      isInProgress: true,
      currentSession: session,
      activeSessions: new Map(state.activeRegenerationSessions),
      activeMessageIds: new Set(state.activeAIMessageIds),
      currentActiveMessageId: aiMessageId
    });

    // Build edit request
    const editRequest = services.uiDesignSelectionService.buildEditRequest(payload.prompt);
    if (!editRequest) {
      callbacks.handleEditFailure('Failed to build edit request');
      this.handleRegenerationFailure(session, 'Failed to build edit request');
      return;
    }

    // Execute regeneration
    const mainEditAPICall = services.uiDesignEditService.editUIDesignPage(editRequest);

    services.uiDesignIntroService
      .executeParallelRegeneration(
        payload.prompt,
        selectedNodes,
        mainEditAPICall,
        state.currentActiveMessageId || undefined
      )
      .subscribe({
        next: (result: any) => {
          if (result.mainAPISuccess) {
            callbacks.handleEditSuccess(result.mainAPIResult, selectedNodes);
            this.handleRegenerationSuccess(session);
          } else {
            callbacks.handleEditFailure('Main edit API failed');
            this.handleRegenerationFailure(session, 'Main edit API failed');
          }
        },
        error: (error: any) => {
          callbacks.handleEditFailure('Regeneration API calls failed');
          this.handleRegenerationFailure(session, error);
        }
      });

    // CRITICAL: Setup intro message state subscription for regeneration
    // This was missing and caused intro messages to not display during regeneration after project loading
    if (callbacks.setupIntroMessageStateSubscriptionForRegeneration) {
      callbacks.setupIntroMessageStateSubscriptionForRegeneration(state.currentActiveMessageId);
    }

    // Emit started event
    this.regenerationStarted$.next({ session, payload });
  }

  // Handle regeneration success
  private handleRegenerationSuccess(session: RegenerationSession): void {
    this.regenerationState$.next({
      ...this.regenerationState$.value,
      isInProgress: false,
      currentSession: null
    });

    this.regenerationCompleted$.next({ session, success: true });
  }

  // Handle regeneration failure
  private handleRegenerationFailure(session: RegenerationSession, error: any): void {
    this.regenerationState$.next({
      ...this.regenerationState$.value,
      isInProgress: false,
      currentSession: null
    });

    this.regenerationFailed$.next({ session, error });
  }

  // Check if regeneration is in progress
  isRegenerationInProgress(): boolean {
    return this.regenerationState$.value.isInProgress;
  }

  // Get current session
  getCurrentSession(): RegenerationSession | null {
    return this.regenerationState$.value.currentSession;
  }

  // Get active sessions
  getActiveSessions(): Map<string, RegenerationSession> {
    return this.regenerationState$.value.activeSessions;
  }

  // Reset service state
  reset(): void {
    this.regenerationState$.next({
      isInProgress: false,
      currentSession: null,
      activeSessions: new Map(),
      activeMessageIds: new Set(),
      currentActiveMessageId: null
    });
  }

  // Cleanup method for component destruction
  cleanup(): void {
    this.regenerationStarted$.complete();
    this.regenerationCompleted$.complete();
    this.regenerationFailed$.complete();
  }
}
