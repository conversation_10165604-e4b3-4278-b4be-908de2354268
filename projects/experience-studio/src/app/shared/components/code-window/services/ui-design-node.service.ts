import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { SafeHtml } from '@angular/platform-browser';
import { createLogger } from '../../../utils/logger';
import { UIDesignFilenameTransformerService } from '../../../services/ui-design-filename-transformer.service';

export interface UIDesignNodeData {
  title: string;
  displayTitle?: string;
  htmlContent: SafeHtml;
  rawContent: string;
  width: number;
  height: number;
  isLoading: boolean;
  loadingMessage?: string;
  originalNodeId?: string;
  zIndex?: number;
}

export interface UIDesignNodePosition {
  x: number;
  y: number;
}

export interface UIDesignNode {
  id: string;
  type: 'ui-design';
  data: UIDesignNodeData;
  position: UIDesignNodePosition;
  selected: boolean;
  dragging: boolean;
  visible: boolean;
}

export interface UIDesignPage {
  fileName: string;
  content: string;
}

@Injectable({
  providedIn: 'root'
})
export class UIDesignNodeService {

  private readonly nodes$ = new BehaviorSubject<UIDesignNode[]>([]);
  private readonly selectedNodes$ = new BehaviorSubject<string[]>([]);

  private readonly defaultNodeSize = { width: 420, height: 720 };
  private readonly gridSpacing = { x: 480, y: 780 };
  private readonly columnsPerRow = 2;

  constructor(private uiDesignFilenameTransformerService: UIDesignFilenameTransformerService) {
  }

  get nodes(): Observable<UIDesignNode[]> {
    return this.nodes$.asObservable();
  }

  get selectedNodes(): Observable<string[]> {
    return this.selectedNodes$.asObservable();
  }

  getNodes(): UIDesignNode[] {
    return this.nodes$.value;
  }

  getSelectedNodeIds(): string[] {
    return this.selectedNodes$.value;
  }

  addNode(node: UIDesignNode): void {
    const currentNodes = this.nodes$.value;
    this.nodes$.next([...currentNodes, node]);
  }

  removeNode(nodeId: string): void {
    const currentNodes = this.nodes$.value;
    const filteredNodes = currentNodes.filter(node => node.id !== nodeId);
    this.nodes$.next(filteredNodes);

    this.deselectNode(nodeId);
  }

  updateNode(nodeId: string, updates: Partial<UIDesignNode>): void {
    const currentNodes = this.nodes$.value;
    const updatedNodes = currentNodes.map(node =>
      node.id === nodeId ? { ...node, ...updates } : node
    );
    this.nodes$.next(updatedNodes);
  }

  selectNode(nodeId: string, multiSelect = false): void {
    const currentSelected = this.selectedNodes$.value;

    if (multiSelect) {
      if (!currentSelected.includes(nodeId)) {
        this.selectedNodes$.next([...currentSelected, nodeId]);
      }
    } else {
      this.selectedNodes$.next([nodeId]);
    }

    this.updateNodeSelectionState();
  }

  deselectNode(nodeId: string): void {
    const currentSelected = this.selectedNodes$.value;
    const filteredSelected = currentSelected.filter(id => id !== nodeId);
    this.selectedNodes$.next(filteredSelected);
    this.updateNodeSelectionState();
  }

  clearSelection(): void {
    this.selectedNodes$.next([]);
    this.updateNodeSelectionState();
  }

  private updateNodeSelectionState(): void {
    const selectedIds = this.selectedNodes$.value;
    const currentNodes = this.nodes$.value;

    const updatedNodes = currentNodes.map(node => ({
      ...node,
      selected: selectedIds.includes(node.id)
    }));

    this.nodes$.next(updatedNodes);
  }

  generateNodeId(): string {
    return `ui-design-node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private decodeHtmlEntities(content: string): string {
    if (!content) return '';

    const textarea = document.createElement('textarea');
    textarea.innerHTML = content;
    let decodedContent = textarea.value;

    decodedContent = decodedContent
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&#x27;/g, "'")
      .replace(/&#x2F;/g, '/')
      .replace(/&#10;/g, '\n')
      .replace(/&nbsp;/g, ' ');

    return decodedContent;
  }

  private ensureProperHtmlStyling(content: string): string {
    if (!content) return '';

    if (content.includes('<!DOCTYPE html>') || content.includes('<html>')) {
      return content;
    }

    const hasBasicStyling = content.includes('margin:') || content.includes('padding:') || content.includes('<style>');

    if (hasBasicStyling) {
      return content;
    }

    const basicCSS = `
      <style>
        * {
          box-sizing: border-box;
        }
        body {
          margin: 0;
          padding: 16px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          line-height: 1.5;
          color: #333;
          background: white;
        }
        h1, h2, h3, h4, h5, h6 {
          margin-top: 0;
          margin-bottom: 0.5em;
        }
        p {
          margin-top: 0;
          margin-bottom: 1em;
        }
        img {
          max-width: 100%;
          height: auto;
        }
        button {
          padding: 8px 16px;
          border: 1px solid #ddd;
          border-radius: 4px;
          background: white;
          cursor: pointer;
        }
        button:hover {
          background: #f5f5f5;
        }
      </style>
    `;

    if (!content.includes('<body>')) {
      return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  ${basicCSS}
</head>
<body>
  ${content}
</body>
</html>`;
    }

    if (content.includes('<head>')) {
      return content.replace('<head>', `<head>${basicCSS}`);
    } else if (content.includes('<body>')) {
      return content.replace('<body>', `<body>${basicCSS}`);
    }

    return content;
  }

  calculateNodePosition(index: number, totalNodes: number): UIDesignNodePosition {
    const row = Math.floor(index / this.columnsPerRow);
    const col = index % this.columnsPerRow;

    const totalCols = Math.min(totalNodes, this.columnsPerRow);
    const gridWidth = totalCols * this.gridSpacing.x;
    const startX = -gridWidth / 2 + this.gridSpacing.x / 2;

    const totalRows = Math.ceil(totalNodes / this.columnsPerRow);
    const gridHeight = totalRows * this.gridSpacing.y;
    const startY = -gridHeight / 2 + this.gridSpacing.y / 2;

    return {
      x: startX + col * this.gridSpacing.x,
      y: startY + row * this.gridSpacing.y
    };
  }

  createNodesFromPages(pages: UIDesignPage[], sanitizer: any): UIDesignNode[] {
    return pages.map((page, index) => {

      const decodedContent = this.decodeHtmlEntities(page.content);
      const processedContent = this.ensureProperHtmlStyling(decodedContent);

      const transformResult = this.uiDesignFilenameTransformerService.transformFileName(page.fileName);

      const node: UIDesignNode = {
        id: this.generateNodeId(),
        type: 'ui-design',
        data: {
          title: page.fileName,
          displayTitle: transformResult.displayTitle,
          htmlContent: sanitizer.bypassSecurityTrustHtml(processedContent),
          rawContent: processedContent,
          width: this.defaultNodeSize.width,
          height: this.defaultNodeSize.height,
          isLoading: false
        },
        position: this.calculateNodePosition(index, pages.length),
        selected: false,
        dragging: false,
        visible: true
      };

      return node;
    });
  }

  createNodesFromPagesWithPositions(
    pages: UIDesignPage[],
    sanitizer: any,
    positions: { x: number; y: number }[]
  ): UIDesignNode[] {

    return pages.map((page, index) => {

      const decodedContent = this.decodeHtmlEntities(page.content);
      const processedContent = this.ensureProperHtmlStyling(decodedContent);

      const nodePosition = positions[index] || this.calculateNodePosition(index, pages.length);

      const transformResult = this.uiDesignFilenameTransformerService.transformFileName(page.fileName);

      const node: UIDesignNode = {
        id: this.generateNodeId(),
        type: 'ui-design',
        data: {
          title: page.fileName,
          displayTitle: transformResult.displayTitle,
          htmlContent: sanitizer.bypassSecurityTrustHtml(processedContent),
          rawContent: processedContent,
          width: this.defaultNodeSize.width,
          height: this.defaultNodeSize.height,
          isLoading: false
        },
        position: nodePosition,
        selected: false,
        dragging: false,
        visible: true
      };

      return node;
    });
  }

  setNodes(nodes: UIDesignNode[]): void {
    this.nodes$.next(nodes);
    this.clearSelection();
  }

  clearAll(): void {
    this.nodes$.next([]);
    this.clearSelection();
  }

  getNodeById(nodeId: string): UIDesignNode | undefined {
    return this.nodes$.value.find(node => node.id === nodeId);
  }

  updateNodePosition(nodeId: string, position: UIDesignNodePosition): void {
    this.updateNode(nodeId, { position });
  }

  updateNodeSize(nodeId: string, width: number, height: number): void {
    const node = this.getNodeById(nodeId);
    if (node) {
      this.updateNode(nodeId, {
        data: { ...node.data, width, height }
      });
    }
  }

  setNodeDragging(nodeId: string, dragging: boolean): void {
    this.updateNode(nodeId, { dragging });
  }

  getContentBounds(): { width: number; height: number; minX: number; minY: number; maxX: number; maxY: number } {
    const nodes = this.nodes$.value;

    if (nodes.length === 0) {
      return { width: 0, height: 0, minX: 0, minY: 0, maxX: 0, maxY: 0 };
    }

    let minX = Infinity;
    let minY = Infinity;
    let maxX = -Infinity;
    let maxY = -Infinity;

    nodes.forEach(node => {
      const nodeMinX = node.position.x;
      const nodeMinY = node.position.y;
      const nodeMaxX = node.position.x + node.data.width;
      const nodeMaxY = node.position.y + node.data.height;

      minX = Math.min(minX, nodeMinX);
      minY = Math.min(minY, nodeMinY);
      maxX = Math.max(maxX, nodeMaxX);
      maxY = Math.max(maxY, nodeMaxY);
    });

    return {
      width: maxX - minX,
      height: maxY - minY,
      minX,
      minY,
      maxX,
      maxY
    };
  }

  clear(): void {
    this.clearAll();
  }

  private enhanceHTMLWithCSS(htmlContent: string): string {

    const cleanedHTML = this.cleanAndNormalizeHTML(htmlContent);

    const hasDoctype = cleanedHTML.includes('<!DOCTYPE');
    const hasHtmlTag = cleanedHTML.includes('<html');
    const hasHead = cleanedHTML.includes('<head');

    let processedHTML: string;

    if (hasDoctype && hasHtmlTag && hasHead) {

      processedHTML = this.enhanceCompleteHTML(cleanedHTML);
    } else {

      processedHTML = this.createCompleteHTMLDocument(cleanedHTML);
    }

    const optimizedHTML = this.applyIframeOptimizations(processedHTML);

    const finalHTML = this.validateAndFixHTML(optimizedHTML);

    return finalHTML;
  }

  private cleanAndNormalizeHTML(htmlContent: string): string {
    if (!htmlContent || typeof htmlContent !== 'string') {
      return '<html><body><p>No content available</p></body></html>';
    }

    let cleaned = htmlContent.trim();

    cleaned = cleaned

      .replace(/<br\s*\/?>/gi, '<br>')
      .replace(/<hr\s*\/?>/gi, '<hr>')
      .replace(/<img([^>]*)\s*\/?>/gi, '<img$1>')

      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')

      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');

    return cleaned;
  }

  private createCompleteHTMLDocument(htmlContent: string): string {

    const isBodyFragment = !htmlContent.includes('<html') && !htmlContent.includes('<head');

    if (isBodyFragment) {
      return this.wrapFragmentInCompleteDocument(htmlContent);
    } else {

      return this.fixIncompleteDocument(htmlContent);
    }
  }

  private enhanceCompleteHTML(htmlContent: string): string {

    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');

    this.enhanceHeadSection(doc);

    this.enhanceBodySection(doc);

    return doc.documentElement.outerHTML;
  }

  private enhanceHeadSection(doc: Document): void {
    let head = doc.querySelector('head');

    if (!head) {
      head = doc.createElement('head');
      doc.documentElement.insertBefore(head, doc.body);
    }

    if (!doc.querySelector('meta[charset]')) {
      const charsetMeta = doc.createElement('meta');
      charsetMeta.setAttribute('charset', 'UTF-8');
      head.insertBefore(charsetMeta, head.firstChild);
    }

    if (!doc.querySelector('meta[name="viewport"]')) {
      const viewportMeta = doc.createElement('meta');
      viewportMeta.setAttribute('name', 'viewport');
      viewportMeta.setAttribute('content', 'width=device-width, initial-scale=1.0');
      head.appendChild(viewportMeta);
    }

    this.addCSSFrameworks(head);

    this.addIframeStyles(head);
  }

  private enhanceBodySection(doc: Document): void {
    let body = doc.querySelector('body');

    if (!body) {
      body = doc.createElement('body');
      doc.documentElement.appendChild(body);
    }

    body.classList.add('iframe-content');

    if (!body.style.margin) {
      body.style.margin = '0';
    }
    if (!body.style.padding) {
      body.style.padding = '20px';
    }
  }

  private addCSSFrameworks(head: HTMLHeadElement): void {
    const frameworks = [
      {
        href: 'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
        id: 'tailwind-css'
      },
      {
        href: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
        id: 'bootstrap-css'
      },
      {
        href: 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
        id: 'google-fonts'
      },
      {
        href: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
        id: 'font-awesome'
      }
    ];

    frameworks.forEach(framework => {
      if (!head.querySelector(`link[href="${framework.href}"]`)) {
        const link = head.ownerDocument.createElement('link');
        link.rel = 'stylesheet';
        link.href = framework.href;
        link.id = framework.id;
        head.appendChild(link);
      }
    });
  }

  private addIframeStyles(head: HTMLHeadElement): void {
    const styleId = 'iframe-custom-styles';

    if (!head.querySelector(`#${styleId}`)) {
      const style = head.ownerDocument.createElement('style');
      style.id = styleId;
      style.textContent = `
        body {
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
          line-height: 1.6 !important;
          margin: 0 !important;
          padding: 20px !important;
          background: #f8fafc !important;
          min-height: 100vh;
          box-sizing: border-box;
        }

        * {
          box-sizing: border-box !important;
        }

        .container {
          max-width: 100% !important;
          margin: 0 auto !important;
        }

        img {
          max-width: 100%;
          height: auto;
        }

        button, input, select, textarea {
          font-family: inherit;
        }

        h1, h2, h3, h4, h5, h6 {
          margin-top: 0;
          margin-bottom: 0.5rem;
        }

        p {
          margin-bottom: 1rem;
        }

        .row, .col, [class*="col-"] {
          margin: 0;
        }
      `;
      head.appendChild(style);
    }
  }

  private wrapFragmentInCompleteDocument(htmlContent: string): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>UI Design Preview</title>

  <!-- CSS Frameworks -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

  <style>
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
      line-height: 1.6 !important;
      margin: 0 !important;
      padding: 20px !important;
      background: #f8fafc !important;
      min-height: 100vh;
    }
    * { box-sizing: border-box !important; }
    .container { max-width: 100% !important; margin: 0 auto !important; }
    img { max-width: 100%; height: auto; }
  </style>
</head>
<body class="iframe-content">
  ${htmlContent}
</body>
</html>`;
  }

  private fixIncompleteDocument(htmlContent: string): string {
    let fixed = htmlContent;

    if (!fixed.includes('<!DOCTYPE')) {
      fixed = '<!DOCTYPE html>\n' + fixed;
    }

    if (!fixed.includes('<html')) {
      fixed = fixed.replace('<!DOCTYPE html>', '<!DOCTYPE html>\n<html lang="en">');
      fixed += '\n</html>';
    }

    if (!fixed.includes('<head')) {
      fixed = fixed.replace('<html', '<html').replace('>', '>\n<head>\n<meta charset="UTF-8">\n<meta name="viewport" content="width=device-width, initial-scale=1.0">\n<title>UI Design</title>\n</head>');
    }

    if (!fixed.includes('<body')) {
      const headEndIndex = fixed.indexOf('</head>');
      if (headEndIndex !== -1) {
        fixed = fixed.substring(0, headEndIndex + 7) + '\n<body>\n' + fixed.substring(headEndIndex + 7) + '\n</body>';
      }
    }

    return fixed;
  }

  private applyIframeOptimizations(htmlContent: string): string {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');

    const problematicElements = doc.querySelectorAll('script[src*="analytics"], script[src*="gtag"], script[src*="facebook"]');
    problematicElements.forEach(el => el.remove());

    const links = doc.querySelectorAll('a[href]');
    links.forEach(link => {
      link.setAttribute('target', '_blank');
      link.setAttribute('rel', 'noopener noreferrer');
    });

    return doc.documentElement.outerHTML;
  }

  private validateAndFixHTML(htmlContent: string): string {

    let validated = htmlContent;

    if (!validated.startsWith('<!DOCTYPE html>')) {
      validated = '<!DOCTYPE html>\n' + validated.replace(/^<!DOCTYPE[^>]*>/i, '');
    }

    validated = validated.replace(/<meta charset="UTF-8">\s*<meta charset="UTF-8">/gi, '<meta charset="UTF-8">');
    validated = validated.replace(/<meta name="viewport"[^>]*>\s*<meta name="viewport"[^>]*>/gi, '<meta name="viewport" content="width=device-width, initial-scale=1.0">');

    if (!validated.includes('<html')) {
      validated = validated.replace('<!DOCTYPE html>', '<!DOCTYPE html>\n<html lang="en">') + '\n</html>';
    }

    return validated;
  }
}
