:host {
  display: inline-block;
}

.icon-pill-container {
  position: relative;
  display: inline-block;
}

.icon-pill {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  padding: 8px;
  background-color: transparent;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  overflow: hidden;
  width: 36px;
  height: 36px;
  transition: width 0.3s ease, box-shadow 0.2s ease;
  white-space: nowrap;

  &:hover, &:focus {
    border:0.5px solid rgba(0, 0, 0, 0.1)
  }

  &.expanded {
    width: 140px;
    justify-content: flex-start;
  }

  .icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    flex-shrink: 0;

    exp-icons {
      width: 24px;
      height: 24px;
      transform: scale(1.2);
    }

    .local-svg-icon {
      width: 24px;
      height: 24px;
      object-fit: contain;
    }
  }

  .text {
    font-size: 12px;
    color: #333;
    margin: 0 4px;
    flex-grow: 1;
  }

  .arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    margin-left: auto;
  }
}

.dropdown {
  position: absolute;
  bottom: calc(100% + 8px);
  left: 0;
  min-width: 140px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.15);
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.2s ease;
  z-index: 100;
  overflow: hidden;

  &.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

.dropdown-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding: 6px 12px;
  cursor: pointer;
  transition: background 0.2s;
  margin: 4px 0; /* Add vertical spacing between items */

  &:hover, &:focus {
    background-color: #f5f5f5;
    outline: none;
  }

  exp-icons {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    flex-shrink: 0;
  }

  .local-svg-icon {
    width: 20px;
    height: 20px;
    object-fit: contain;
  }

  .dropdown-item-text {
    font-size: 12px;
    color: #333;
    flex-grow: 1;
  }

  .dropdown-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
}
