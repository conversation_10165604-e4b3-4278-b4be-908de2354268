<div
  class="icon-pill-container"
  (mouseenter)="onMouseEnter()"
  (mouseleave)="onMouseLeave()"
>
  <button
    class="icon-pill"
    [class.expanded]="isHovered()"
    (click)="toggleDropdown($event)"
    [attr.aria-label]="'Select: ' + selectedOption.name"
    [attr.aria-expanded]="isDropdownOpen()"
    [attr.aria-haspopup]="true"
  >
    <span class="icon-wrapper">
      <ng-container *ngIf="!selectedOption.isLocalSvg; else localSvg">
        <exp-icons [iconName]="selectedOption.icon"></exp-icons>
      </ng-container>
      <ng-template #localSvg>
        <img [src]="selectedOption.icon" alt="{{ selectedOption.name }}" class="local-svg-icon">
      </ng-template>
    </span>
    <span class="text" *ngIf="isHovered()">{{ selectedOption.name }}</span>
    <span class="arrow" *ngIf="isHovered()">
      <exp-icons [iconName]="'awe_arrow_drop_up_filled'"></exp-icons>
    </span>
  </button>

  <div
    class="dropdown"
    [class.show]="isDropdownOpen()"
    role="menu"
    (mouseenter)="onDropdownMouseEnter()"
    (mouseleave)="onDropdownMouseLeave()"
  >
    <div
      *ngFor="let option of options"
      class="dropdown-item"
      role="menuitem"
      tabindex="0"
      (click)="selectOption(option, $event)"
      (keydown.enter)="selectOption(option, $event)"
      (keydown.space)="selectOption(option, $event)"
    >
      <span class="dropdown-item-text">{{ option.name }}</span>
      <span class="dropdown-item-icon">
        <ng-container *ngIf="!option.isLocalSvg; else localOptionSvg">
          <exp-icons [iconName]="option.icon"></exp-icons>
        </ng-container>
        <ng-template #localOptionSvg>
          <img [src]="option.icon" alt="{{ option.name }}" class="local-svg-icon">
        </ng-template>
      </span>
    </div>
  </div>
</div>
