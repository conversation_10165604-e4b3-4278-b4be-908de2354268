import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectionStrategy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { PromptSubmissionService } from '../../services/prompt-submission.service';
import { CardSelectionService } from '../../services/card-selection.service';
import { PromptContentComponent } from '../../../pages/image-to-code/components/prompt-content/prompt-content.component';
import { CodeWindowComponent } from '../code-window/code-window.component';

/**
 * Application Flow Component
 * 
 * This component handles the unified routing for Generate Application flow.
 * It determines whether to show the prompt screen or code window based on
 * the application state, specifically whether a prompt has been submitted.
 * 
 * URL: /generate-application
 * - Shows prompt screen if no prompt has been submitted
 * - Shows code window if prompt has been submitted
 */
@Component({
  selector: 'app-application-flow',
  standalone: true,
  imports: [CommonModule, PromptContentComponent, CodeWindowComponent],
  template: `
    <div class="application-flow-container">
      <!-- Show prompt screen when no prompt has been submitted -->
      <app-prompt-content 
        *ngIf="showPromptScreen"
        class="prompt-screen">
      </app-prompt-content>
      
      <!-- Show code window when prompt has been submitted -->
      <app-code-window 
        *ngIf="showCodeWindow"
        class="code-window">
      </app-code-window>
    </div>
  `,
  styles: [`
    .application-flow-container {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    
    .prompt-screen,
    .code-window {
      width: 100%;
      height: 100%;
      flex: 1;
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ApplicationFlowComponent implements OnInit, OnDestroy {
  private readonly router = inject(Router);
  private readonly route = inject(ActivatedRoute);
  private readonly promptSubmissionService = inject(PromptSubmissionService);
  private readonly cardSelectionService = inject(CardSelectionService);
  private readonly destroy$ = new Subject<void>();

  showPromptScreen = true;
  showCodeWindow = false;

  ngOnInit(): void {
    this.initializeCardSelection();
    this.setupPromptSubmissionTracking();
    this.determineInitialView();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Initialize card selection for Generate Application
   */
  private initializeCardSelection(): void {
    // Set the card selection state to true for Generate Application
    this.cardSelectionService.setCardSelected(true);
  }

  /**
   * Set up tracking of prompt submission state
   */
  private setupPromptSubmissionTracking(): void {
    this.promptSubmissionService.hasSubmittedPrompt$
      .pipe(takeUntil(this.destroy$))
      .subscribe(hasSubmitted => {
        this.updateViewState(hasSubmitted);
      });
  }

  /**
   * Determine the initial view based on current state
   */
  private determineInitialView(): void {
    const hasSubmitted = this.promptSubmissionService.hasPromptBeenSubmitted();
    this.updateViewState(hasSubmitted);
  }

  /**
   * Update the view state based on prompt submission status
   */
  private updateViewState(hasSubmitted: boolean): void {
    this.showPromptScreen = !hasSubmitted;
    this.showCodeWindow = hasSubmitted;
  }
}
