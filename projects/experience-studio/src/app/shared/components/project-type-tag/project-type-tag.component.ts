import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ProjectType } from '../../models/recent-creation.model';

/**
 * Reusable component for displaying project type tags/badges
 * Shows color-coded tags for different project types (Wireframe Generation, App Generation, etc.)
 */
@Component({
  selector: 'app-project-type-tag',
  standalone: true,
  imports: [CommonModule],
  template: `
    <span 
      class="project-type-tag"
      [class.small]="size === 'small'"
      [class.medium]="size === 'medium'"
      [class.large]="size === 'large'"
      [style.background-color]="backgroundColor"
      [style.border-color]="borderColor"
      [style.color]="textColor"
      [title]="projectType?.description || projectType?.label"
      [attr.aria-label]="'Project type: ' + (projectType?.label || 'Unknown')">
      <span class="tag-icon" *ngIf="showIcon">
        <i [class]="getIconClass()"></i>
      </span>
      <span class="tag-text">{{ projectType?.label || 'Unknown' }}</span>
    </span>
  `,
  styleUrls: ['./project-type-tag.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProjectTypeTagComponent {
  @Input() projectType: ProjectType | null = null;
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() variant: 'filled' | 'outlined' | 'subtle' = 'filled';
  @Input() showIcon: boolean = true;
  
  /**
   * Get background color based on variant and project type
   */
  get backgroundColor(): string {
    if (!this.projectType) return '#6c757d';
    
    switch (this.variant) {
      case 'filled':
        return this.projectType.color;
      case 'outlined':
        return 'transparent';
      case 'subtle':
        return this.projectType.color + '20'; // 20% opacity
      default:
        return this.projectType.color;
    }
  }
  
  /**
   * Get border color based on variant and project type
   */
  get borderColor(): string {
    if (!this.projectType) return '#6c757d';
    
    switch (this.variant) {
      case 'outlined':
      case 'subtle':
        return this.projectType.color;
      case 'filled':
      default:
        return this.projectType.color;
    }
  }
  
  /**
   * Get text color based on variant
   */
  get textColor(): string {
    if (!this.projectType) return '#ffffff';
    
    switch (this.variant) {
      case 'filled':
        return '#ffffff';
      case 'outlined':
      case 'subtle':
        return this.projectType.color;
      default:
        return '#ffffff';
    }
  }
  
  /**
   * Get icon class based on project type
   */
  getIconClass(): string {
    if (!this.projectType) return 'fas fa-question';
    
    switch (this.projectType.value) {
      case 'wireframe_generation':
        return 'fas fa-paint-brush';
      case 'app_generation':
        return 'fas fa-code';
      default:
        return 'fas fa-project-diagram';
    }
  }
}
