import { 
  Component, 
  Input, 
  Output, 
  EventEmitter, 
  ChangeDetectionStrategy, 
  signal,
  inject,
  DestroyRef,
  ElementRef,
  ViewChild,
  AfterViewInit
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { debounceTime, distinctUntilChanged, fromEvent } from 'rxjs';

/**
 * Reusable search input component with debouncing and modern Angular patterns
 * Uses Angular 19+ signals and dependency injection
 */
@Component({
  selector: 'app-search-input',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="search-input-container" [class.focused]="isFocused()" [class.has-value]="hasValue()">
      <div class="search-input-wrapper">
        <div class="search-icon">
          <i class="fas fa-search" [class.active]="hasValue()"></i>
        </div>
        <input
          #searchInput
          type="text"
          class="search-input"
          [placeholder]="placeholder"
          [value]="value"
          [disabled]="disabled"
          (input)="onInput($event)"
          (focus)="onFocus()"
          (blur)="onBlur()"
          (keydown.escape)="onEscape()"
          [attr.aria-label]="ariaLabel || placeholder"
          [attr.aria-describedby]="ariaDescribedBy"
          autocomplete="off"
          spellcheck="false"
        />
        @if (hasValue() && showClearButton) {
          <button
            type="button"
            class="clear-button"
            (click)="clearSearch()"
            [attr.aria-label]="'Clear search'"
            tabindex="0">
            <i class="fas fa-times"></i>
          </button>
        }
      </div>
      @if (showResultCount && resultCount !== null) {
        <div class="result-count" [attr.aria-live]="'polite'">
          {{ getResultCountText() }}
        </div>
      }
    </div>
  `,
  styleUrls: ['./search-input.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SearchInputComponent implements AfterViewInit {
  private readonly destroyRef = inject(DestroyRef);
  
  @ViewChild('searchInput', { static: true }) searchInputRef!: ElementRef<HTMLInputElement>;
  
  @Input() placeholder: string = 'Search...';
  @Input() value: string = '';
  @Input() disabled: boolean = false;
  @Input() debounceTime: number = 300;
  @Input() showClearButton: boolean = true;
  @Input() showResultCount: boolean = false;
  @Input() resultCount: number | null = null;
  @Input() ariaLabel?: string;
  @Input() ariaDescribedBy?: string;
  @Input() autoFocus: boolean = false;
  
  @Output() valueChange = new EventEmitter<string>();
  @Output() search = new EventEmitter<string>();
  @Output() clear = new EventEmitter<void>();
  @Output() focus = new EventEmitter<void>();
  @Output() blur = new EventEmitter<void>();
  
  // Component state signals
  private readonly _isFocused = signal<boolean>(false);
  private readonly _hasValue = signal<boolean>(false);
  
  readonly isFocused = this._isFocused.asReadonly();
  readonly hasValue = this._hasValue.asReadonly();
  
  ngAfterViewInit(): void {
    this.setupDebouncing();
    this.updateHasValue();
    
    if (this.autoFocus) {
      setTimeout(() => this.focusInput(), 0);
    }
  }
  
  /**
   * Setup debounced search functionality
   */
  private setupDebouncing(): void {
    fromEvent(this.searchInputRef.nativeElement, 'input')
      .pipe(
        debounceTime(this.debounceTime),
        distinctUntilChanged(),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe((event: Event) => {
        const target = event.target as HTMLInputElement;
        this.search.emit(target.value);
      });
  }
  
  /**
   * Handle input events (immediate value updates)
   */
  onInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.value = target.value;
    this.updateHasValue();
    this.valueChange.emit(this.value);
  }
  
  /**
   * Handle focus events
   */
  onFocus(): void {
    this._isFocused.set(true);
    this.focus.emit();
  }
  
  /**
   * Handle blur events
   */
  onBlur(): void {
    this._isFocused.set(false);
    this.blur.emit();
  }
  
  /**
   * Handle escape key to clear search
   */
  onEscape(): void {
    if (this.hasValue()) {
      this.clearSearch();
    }
  }
  
  /**
   * Clear the search input
   */
  clearSearch(): void {
    this.value = '';
    this.updateHasValue();
    this.searchInputRef.nativeElement.value = '';
    this.valueChange.emit('');
    this.search.emit('');
    this.clear.emit();
    this.focusInput();
  }
  
  /**
   * Focus the search input
   */
  focusInput(): void {
    this.searchInputRef.nativeElement.focus();
  }
  
  /**
   * Update the hasValue signal
   */
  private updateHasValue(): void {
    this._hasValue.set(this.value.trim().length > 0);
  }
  
  /**
   * Get result count text for accessibility
   */
  getResultCountText(): string {
    if (this.resultCount === null) return '';
    
    if (this.resultCount === 0) {
      return 'No results found';
    } else if (this.resultCount === 1) {
      return '1 result found';
    } else {
      return `${this.resultCount} results found`;
    }
  }
}
