// UI Design Code Preview Component Styles

.ui-design-container {
  height: 100vh;
  width: 100%;
  overflow: hidden;

  // Custom header styles
  .custom-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    background: var(--header-bg);
    min-height: 60px;

    &.light-theme {
      --header-bg: #ffffff;
      --border-color: #e5e7eb;
      --text-color: #374151;
    }

    &.dark-theme {
      --header-bg: #1f2937;
      --border-color: #374151;
      --text-color: #f9fafb;
    }

    .header-left,
    .header-right {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .header-center {
      flex: 1;
      display: flex;
      justify-content: center;
    }

    .project-name,
    .header-title {
      font-weight: 600;
      color: var(--text-color);
      font-size: 14px;
    }

    .canvas-info {
      font-size: 12px;
      color: var(--text-color);
      opacity: 0.7;
    }
  }

  // Adjust height utility
  .adjust-height {
    overflow: hidden;
  }

  // Loading container
  .loading-container,
  .canvas-loading-container {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
  }

  // Chat container
  .chat-container {
    height: 100%;
    overflow: hidden;
  }

  // Error container
  .error-container {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;

    .error-content {
      text-align: center;
      max-width: 400px;

      .error-icon {
        font-size: 48px;
        color: #ef4444;
        margin-bottom: 16px;
      }

      h3 {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 8px;
        color: var(--text-color);
      }

      p {
        color: var(--text-color);
        opacity: 0.7;
        margin-bottom: 24px;
        line-height: 1.5;
      }

      .retry-button {
        background: #3b82f6;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0 auto;

        &:hover {
          background: #2563eb;
          transform: translateY(-1px);
        }

        i {
          font-size: 14px;
        }
      }
    }
  }
}

// React Flow-Inspired Canvas Styles
.react-flow-canvas {
  height: 100%;
  width: 100%;
  overflow: hidden;
  background: var(--canvas-bg);
  position: relative;
  cursor: grab;
  user-select: none;

  &:active {
    cursor: grabbing;
  }

  &.light-theme {
    --canvas-bg: #f8fafc;
    --box-bg: #ffffff;
    --box-border: #e5e7eb;
    --box-shadow: rgba(0, 0, 0, 0.1);
    --text-color: #374151;
    --overlay-bg: rgba(255, 255, 255, 0.95);
    --control-bg: rgba(255, 255, 255, 0.9);
    --control-border: #e5e7eb;
    --control-shadow: rgba(0, 0, 0, 0.1);
  }

  &.dark-theme {
    --canvas-bg: #0f172a;
    --box-bg: #1e293b;
    --box-border: #334155;
    --box-shadow: rgba(0, 0, 0, 0.3);
    --text-color: #f1f5f9;
    --overlay-bg: rgba(30, 41, 59, 0.95);
    --control-bg: rgba(30, 41, 59, 0.9);
    --control-border: #334155;
    --control-shadow: rgba(0, 0, 0, 0.3);
  }

  // Canvas Controls
  .canvas-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 10;
    display: flex;
    gap: 12px;
    pointer-events: none;

    .zoom-controls,
    .view-controls {
      display: flex;
      align-items: center;
      gap: 8px;
      background: var(--control-bg);
      border: 1px solid var(--control-border);
      border-radius: 8px;
      padding: 8px;
      box-shadow: 0 4px 12px var(--control-shadow);
      backdrop-filter: blur(8px);
      pointer-events: auto;
    }

    .control-button {
      background: transparent;
      border: none;
      width: 32px;
      height: 32px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      color: var(--text-color);

      &:hover:not(:disabled) {
        background: var(--box-border);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      &.active {
        background: #3b82f6;
        color: white;
      }

      i {
        font-size: 14px;
      }
    }

    .zoom-level {
      font-size: 12px;
      font-weight: 600;
      color: var(--text-color);
      min-width: 40px;
      text-align: center;
    }
  }

  // Canvas Content with Transform
  .canvas-content {
    width: 100%;
    height: 100%;
    transform-origin: 0 0;
    transition: transform 0.1s ease;
    position: relative;
  }

  // No nodes message
  .no-nodes-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: var(--text-color);
    opacity: 0.6;

    .message-content {
      i {
        font-size: 48px;
        margin-bottom: 16px;
        display: block;
      }

      h3 {
        font-size: 18px;
        margin-bottom: 8px;
        font-weight: 600;
      }

      p {
        font-size: 14px;
        margin: 0;
      }
    }
  }

  // Mini Map
  .mini-map {
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 200px;
    height: 150px;
    z-index: 10;
    pointer-events: auto;

    .mini-map-content {
      background: var(--control-bg);
      border: 1px solid var(--control-border);
      border-radius: 8px;
      box-shadow: 0 4px 12px var(--control-shadow);
      backdrop-filter: blur(8px);
      overflow: hidden;
    }

    .mini-map-header {
      padding: 8px 12px;
      border-bottom: 1px solid var(--control-border);
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 12px;
      font-weight: 600;
      color: var(--text-color);

      .mini-map-close {
        background: transparent;
        border: none;
        width: 20px;
        height: 20px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: var(--text-color);

        &:hover {
          background: var(--box-border);
        }

        i {
          font-size: 10px;
        }
      }
    }

    .mini-map-canvas {
      position: relative;
      width: 100%;
      height: 110px;
      background: var(--canvas-bg);
      overflow: hidden;

      .mini-node {
        position: absolute;
        background: var(--box-bg);
        border: 1px solid var(--box-border);
        border-radius: 2px;
        min-width: 2px;
        min-height: 2px;

        &.selected {
          border-color: #3b82f6;
          background: rgba(59, 130, 246, 0.1);
        }
      }
    }
  }
}

// React Flow-Inspired Screen Node Styles
.screen-node {
  background: var(--box-bg);
  border: 2px solid var(--box-border);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px var(--box-shadow);
  transition: all 0.2s ease;
  cursor: pointer;
  position: absolute;
  pointer-events: auto;

  &:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 25px var(--box-shadow);
    z-index: 5;

    .node-overlay {
      opacity: 1;
    }

    .expand-icon {
      opacity: 1;
    }
  }

  &.selected {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2), 0 4px 12px var(--box-shadow);
    z-index: 10;

    .node-overlay {
      opacity: 0;
    }
  }

  &.dragging {
    z-index: 15;
    transform: scale(1.05);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
  }

  &.loading {
    opacity: 0.6;
    pointer-events: none;
  }

  .node-header {
    padding: 12px 16px;
    border-bottom: 1px solid var(--box-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--box-bg);
    min-height: 44px;

    h4 {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-color);
      margin: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1;
    }

    .node-actions {
      display: flex;
      align-items: center;
      gap: 8px;

      .node-action-button {
        background: transparent;
        border: none;
        padding: 4px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s ease;
        color: var(--text-color);
        opacity: 0.6;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          opacity: 1;
          background: rgba(0, 0, 0, 0.05);
          transform: scale(1.1);
        }

        &.download-button:hover {
          color: #16a34a;
          background: rgba(34, 197, 94, 0.1);
        }

        svg {
          width: 14px;
          height: 14px;
        }
      }

      .expand-icon {
        font-size: 12px;
        color: var(--text-color);
        opacity: 0.5;
        transition: opacity 0.2s ease;
      }
    }
  }

  .node-content {
    height: calc(100% - 44px);
    position: relative;
    overflow: hidden;

    .node-preview {
      width: 100%;
      height: 100%;

      .preview-iframe {
        width: 100%;
        height: 100%;
        border: none;
        background: white;
        pointer-events: none;

        // Hide scrollbars but allow scrolling
        &::-webkit-scrollbar {
          display: none;
        }
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* Internet Explorer 10+ */
      }
    }
  }

  .node-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--overlay-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
    backdrop-filter: blur(2px);

    .overlay-content {
      text-align: center;
      color: var(--text-color);

      i {
        font-size: 20px;
        margin-bottom: 6px;
        display: block;
      }

      span {
        font-size: 12px;
        font-weight: 500;
      }
    }
  }
}

// Full-screen Modal Styles
.fullscreen-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);

  &.light-theme {
    --modal-bg: #ffffff;
    --modal-border: #e5e7eb;
    --text-color: #374151;
    --button-bg: #f3f4f6;
    --button-hover: #e5e7eb;
    --button-active: #3b82f6;
  }

  &.dark-theme {
    --modal-bg: #1e293b;
    --modal-border: #334155;
    --text-color: #f1f5f9;
    --button-bg: #334155;
    --button-hover: #475569;
    --button-active: #3b82f6;
  }

  .modal-content {
    background: var(--modal-bg);
    border-radius: 16px;
    width: 95vw;
    height: 95vh;
    max-width: 1400px;
    max-height: 900px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  }

  .modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--modal-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--modal-bg);

    .modal-title h3 {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-color);
      margin: 0;
    }

    .close-button {
      background: var(--button-bg);
      border: none;
      width: 36px;
      height: 36px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      color: var(--text-color);

      &:hover {
        background: var(--button-hover);
        transform: scale(1.05);
      }

      i {
        font-size: 14px;
      }
    }
  }

  .view-mode-toggle {
    padding: 16px 24px;
    border-bottom: 1px solid var(--modal-border);
    display: flex;
    gap: 8px;
    background: var(--modal-bg);

    .view-mode-button {
      background: var(--button-bg);
      border: none;
      padding: 8px 16px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
      color: var(--text-color);
      font-size: 14px;
      font-weight: 500;

      &:hover {
        background: var(--button-hover);
      }

      &.active {
        background: var(--button-active);
        color: white;
      }

      i {
        font-size: 14px;
      }
    }
  }

  .modal-body {
    flex: 1;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24px;
    background: var(--canvas-bg, #f8fafc);
  }

  .fullscreen-preview {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;

    &.mobile-view {
      width: 375px;
      height: 667px;
      max-height: 80vh;
    }

    &.web-view {
      width: 100%;
      height: 100%;
      max-width: 1200px;
      max-height: 80vh;
    }

    .fullscreen-iframe {
      width: 100%;
      height: 100%;
      border: none;
      background: white;
    }
  }
}

// Responsive adjustments for React Flow Canvas
@media (max-width: 768px) {
  .react-flow-canvas {
    .canvas-controls {
      top: 10px;
      right: 10px;
      gap: 8px;

      .zoom-controls,
      .view-controls {
        padding: 6px;
        gap: 6px;
      }

      .control-button {
        width: 28px;
        height: 28px;

        i {
          font-size: 12px;
        }
      }

      .zoom-level {
        font-size: 11px;
        min-width: 35px;
      }
    }

    .mini-map {
      bottom: 10px;
      right: 10px;
      width: 160px;
      height: 120px;

      .mini-map-canvas {
        height: 85px;
      }
    }

    // Touch-friendly cursor
    cursor: default;

    &:active {
      cursor: default;
    }
  }

  .screen-node {
    .node-header {
      padding: 10px 12px;
      min-height: 40px;

      h4 {
        font-size: 13px;
      }
    }

    .node-content {
      height: calc(100% - 40px);
    }
  }

  .fullscreen-modal {
    .modal-content {
      width: 100vw;
      height: 100vh;
      border-radius: 0;
    }

    .fullscreen-preview {
      &.mobile-view {
        width: 100%;
        max-width: 375px;
        height: 70vh;
      }

      &.web-view {
        width: 100%;
        height: 70vh;
      }
    }
  }
}

@media (max-width: 480px) {
  .react-flow-canvas {
    .canvas-controls {
      flex-direction: column;
      align-items: flex-end;

      .zoom-controls {
        order: 2;
      }

      .view-controls {
        order: 1;
      }
    }

    .mini-map {
      width: 140px;
      height: 100px;

      .mini-map-canvas {
        height: 70px;
      }
    }
  }

  .screen-node {
    .node-header {
      padding: 8px 10px;
      min-height: 36px;

      h4 {
        font-size: 12px;
      }
    }

    .node-content {
      height: calc(100% - 36px);
    }
  }
}

// Wireframe Loading Overlay
.wireframe-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;

  .wireframe-loading-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;

    .container {
      position: relative;
      width: 160px;
      height: 100px;

      .loader {
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 10;
        width: 160px;
        height: 100px;
        margin-left: -80px;
        margin-top: -50px;
        border-radius: 5px;
        background: #1e3f57;
        animation: dot1_ 3s cubic-bezier(0.55,0.3,0.24,0.99) infinite;

        &:nth-child(2) {
          z-index: 11;
          width: 150px;
          height: 90px;
          margin-top: -45px;
          margin-left: -75px;
          border-radius: 3px;
          background: #3c517d;
          animation-name: dot2_;
        }

        &:nth-child(3) {
          z-index: 12;
          width: 40px;
          height: 20px;
          margin-top: 50px;
          margin-left: -20px;
          border-radius: 0 0 5px 5px;
          background: #6bb2cd;
          animation-name: dot3_;
        }
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes dot1_ {
  3%,97% {
    width: 160px;
    height: 100px;
    margin-top: -50px;
    margin-left: -80px;
  }

  30%,36% {
    width: 80px;
    height: 120px;
    margin-top: -60px;
    margin-left: -40px;
  }

  63%,69% {
    width: 40px;
    height: 80px;
    margin-top: -40px;
    margin-left: -20px;
  }
}

@keyframes dot2_ {
  3%,97% {
    height: 90px;
    width: 150px;
    margin-left: -75px;
    margin-top: -45px;
  }

  30%,36% {
    width: 70px;
    height: 96px;
    margin-left: -35px;
    margin-top: -48px;
  }

  63%,69% {
    width: 32px;
    height: 60px;
    margin-left: -16px;
    margin-top: -30px;
  }
}

@keyframes dot3_ {
  3%,97% {
    height: 20px;
    width: 40px;
    margin-left: -20px;
    margin-top: 50px;
  }

  30%,36% {
    width: 8px;
    height: 8px;
    margin-left: -5px;
    margin-top: 49px;
    border-radius: 8px;
  }

  63%,69% {
    width: 16px;
    height: 4px;
    margin-left: -8px;
    margin-top: -37px;
    border-radius: 10px;
  }
}

// Smooth animations
.smooth-split-screen {
  transition: all 0.3s ease;
}

// Cursor styles
.cursor-pointer {
  cursor: pointer;
}
