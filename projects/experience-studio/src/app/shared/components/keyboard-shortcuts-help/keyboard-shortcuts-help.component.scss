.shortcuts-help-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease-in-out;
  
  &.visible {
    opacity: 1;
    visibility: visible;
    
    .shortcuts-help-modal {
      transform: scale(1);
      opacity: 1;
    }
  }
  
  .shortcuts-help-modal {
    background: var(--shortcuts-modal-bg, #ffffff);
    border-radius: 0.75rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 600px;
    max-height: 80vh;
    width: 90vw;
    display: flex;
    flex-direction: column;
    transform: scale(0.95);
    opacity: 0;
    transition: all 0.3s ease-in-out;
    border: 1px solid var(--shortcuts-modal-border, #e1e5e9);
    
    .shortcuts-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 1.5rem;
      border-bottom: 1px solid var(--shortcuts-header-border, #e1e5e9);
      
      .shortcuts-title {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--shortcuts-title-color, #1f2937);
        
        i {
          color: var(--shortcuts-icon-color, #8c65f7);
        }
      }
      
      .close-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        background: transparent;
        border: none;
        border-radius: 0.375rem;
        color: var(--shortcuts-close-color, #6b7280);
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        
        &:hover {
          background: var(--shortcuts-close-hover-bg, #f3f4f6);
          color: var(--shortcuts-close-hover-color, #374151);
        }
        
        &:focus {
          outline: 2px solid var(--shortcuts-focus-color, #8c65f7);
          outline-offset: 1px;
        }
      }
    }
    
    .shortcuts-content {
      flex: 1;
      overflow-y: auto;
      padding: 1.5rem;
      
      .no-shortcuts {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        text-align: center;
        color: var(--shortcuts-empty-color, #6b7280);
        
        i {
          font-size: 2rem;
          margin-bottom: 0.75rem;
          color: var(--shortcuts-empty-icon, #d1d5db);
        }
        
        p {
          margin: 0;
          font-size: 0.875rem;
        }
      }
      
      .shortcut-category {
        margin-bottom: 2rem;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .category-title {
          font-size: 1rem;
          font-weight: 600;
          color: var(--shortcuts-category-color, #374151);
          margin: 0 0 1rem 0;
          padding-bottom: 0.5rem;
          border-bottom: 1px solid var(--shortcuts-category-border, #e5e7eb);
        }
        
        .shortcuts-list {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
          
          .shortcut-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.75rem;
            background: var(--shortcuts-item-bg, #f9fafb);
            border-radius: 0.5rem;
            border: 1px solid var(--shortcuts-item-border, #e5e7eb);
            
            .shortcut-keys {
              display: flex;
              align-items: center;
              gap: 0.25rem;
              
              .shortcut-key {
                display: inline-flex;
                align-items: center;
                padding: 0.25rem 0.5rem;
                background: var(--shortcuts-key-bg, #ffffff);
                border: 1px solid var(--shortcuts-key-border, #d1d5db);
                border-radius: 0.25rem;
                font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
                font-size: 0.75rem;
                font-weight: 500;
                color: var(--shortcuts-key-color, #374151);
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
              }
            }
            
            .shortcut-description {
              flex: 1;
              margin-left: 1rem;
              font-size: 0.875rem;
              color: var(--shortcuts-desc-color, #4b5563);
            }
          }
        }
      }
    }
    
    .shortcuts-footer {
      padding: 1rem 1.5rem;
      border-top: 1px solid var(--shortcuts-footer-border, #e1e5e9);
      background: var(--shortcuts-footer-bg, #f9fafb);
      border-radius: 0 0 0.75rem 0.75rem;
      
      .shortcuts-note {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0;
        font-size: 0.75rem;
        color: var(--shortcuts-note-color, #6b7280);
        
        i {
          color: var(--shortcuts-note-icon, #fbbf24);
        }
        
        kbd {
          padding: 0.125rem 0.25rem;
          background: var(--shortcuts-key-bg, #ffffff);
          border: 1px solid var(--shortcuts-key-border, #d1d5db);
          border-radius: 0.1875rem;
          font-family: inherit;
          font-size: 0.6875rem;
        }
      }
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .shortcuts-help-overlay {
    .shortcuts-help-modal {
      background: var(--shortcuts-modal-bg-dark, #1f2937);
      border-color: var(--shortcuts-modal-border-dark, #374151);
      
      .shortcuts-header {
        border-color: var(--shortcuts-header-border-dark, #374151);
        
        .shortcuts-title {
          color: var(--shortcuts-title-color-dark, #f9fafb);
        }
        
        .close-button {
          color: var(--shortcuts-close-color-dark, #9ca3af);
          
          &:hover {
            background: var(--shortcuts-close-hover-bg-dark, #374151);
            color: var(--shortcuts-close-hover-color-dark, #f3f4f6);
          }
        }
      }
      
      .shortcuts-content {
        .shortcut-category {
          .category-title {
            color: var(--shortcuts-category-color-dark, #f3f4f6);
            border-color: var(--shortcuts-category-border-dark, #4b5563);
          }
          
          .shortcuts-list .shortcut-item {
            background: var(--shortcuts-item-bg-dark, #111827);
            border-color: var(--shortcuts-item-border-dark, #374151);
            
            .shortcut-keys .shortcut-key {
              background: var(--shortcuts-key-bg-dark, #374151);
              border-color: var(--shortcuts-key-border-dark, #4b5563);
              color: var(--shortcuts-key-color-dark, #f3f4f6);
            }
            
            .shortcut-description {
              color: var(--shortcuts-desc-color-dark, #d1d5db);
            }
          }
        }
      }
      
      .shortcuts-footer {
        background: var(--shortcuts-footer-bg-dark, #111827);
        border-color: var(--shortcuts-footer-border-dark, #374151);
        
        .shortcuts-note {
          color: var(--shortcuts-note-color-dark, #9ca3af);
          
          kbd {
            background: var(--shortcuts-key-bg-dark, #374151);
            border-color: var(--shortcuts-key-border-dark, #4b5563);
            color: var(--shortcuts-key-color-dark, #f3f4f6);
          }
        }
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .shortcuts-help-overlay {
    .shortcuts-help-modal {
      width: 95vw;
      max-height: 90vh;
      
      .shortcuts-header {
        padding: 1rem;
        
        .shortcuts-title {
          font-size: 1.125rem;
        }
      }
      
      .shortcuts-content {
        padding: 1rem;
        
        .shortcut-category .shortcuts-list .shortcut-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 0.5rem;
          
          .shortcut-description {
            margin-left: 0;
          }
        }
      }
      
      .shortcuts-footer {
        padding: 0.75rem 1rem;
      }
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .shortcuts-help-overlay {
    .shortcuts-help-modal {
      border-width: 2px;
      
      .shortcuts-content .shortcut-category .shortcuts-list .shortcut-item {
        border-width: 2px;
        
        .shortcut-keys .shortcut-key {
          border-width: 2px;
          font-weight: 600;
        }
      }
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .shortcuts-help-overlay {
    transition: none;
    
    .shortcuts-help-modal {
      transition: none;
    }
  }
}
