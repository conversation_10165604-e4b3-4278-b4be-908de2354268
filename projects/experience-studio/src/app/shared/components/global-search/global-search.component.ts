import { Component, ChangeDetectionStrategy, inject, signal, computed, input, output, DestroyRef, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { fromEvent } from 'rxjs';
import { debounceTime, distinctUntilChanged, map } from 'rxjs/operators';
import { GlobalSearchService } from '../../services/global-search.service';

export interface SearchSuggestion {
  id: string;
  text: string;
  type: 'project' | 'creator' | 'tag' | 'template' | 'action';
  icon: string;
  category: string;
  metadata?: any;
}

export interface SearchResult {
  suggestions: SearchSuggestion[];
  recentSearches: string[];
  quickActions: SearchSuggestion[];
}

/**
 * Global Search Component - Advanced search with suggestions and history
 * Provides real-time search with debounced input and intelligent suggestions
 * 
 * Features:
 * - Debounced search input (300ms)
 * - Real-time suggestions dropdown
 * - Search history persistence
 * - Quick actions and shortcuts
 * - Keyboard navigation support
 * - Accessibility compliant
 */
@Component({
  selector: 'app-global-search',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="global-search" [class.focused]="isFocused()" [class.has-results]="showSuggestions()">
      <!-- Search Input -->
      <div class="search-input-container">
        <div class="search-icon">
          <i class="fas fa-search" aria-hidden="true"></i>
        </div>
        
        <input
          #searchInput
          type="text"
          class="search-input"
          [value]="query()"
          [placeholder]="placeholder()"
          (focus)="onFocus()"
          (blur)="onBlur()"
          (keydown)="onKeyDown($event)"
          [attr.aria-expanded]="showSuggestions()"
          [attr.aria-owns]="showSuggestions() ? 'search-suggestions' : null"
          aria-haspopup="listbox"
          aria-autocomplete="list"
          role="combobox">
        
        @if (query()) {
          <button 
            class="clear-search"
            (click)="clearSearch()"
            aria-label="Clear search">
            <i class="fas fa-times" aria-hidden="true"></i>
          </button>
        }
        
        @if (isSearching()) {
          <div class="search-loading">
            <i class="fas fa-spinner fa-spin" aria-hidden="true"></i>
          </div>
        }
      </div>
      
      <!-- Search Suggestions Dropdown -->
      @if (showSuggestions()) {
        <div 
          id="search-suggestions"
          class="search-suggestions"
          role="listbox"
          [attr.aria-label]="'Search suggestions'">
          
          <!-- Recent Searches -->
          @if (recentSearches().length > 0 && !query()) {
            <div class="suggestion-section">
              <div class="section-header">
                <i class="fas fa-history" aria-hidden="true"></i>
                <span>Recent Searches</span>
                <button 
                  class="clear-history"
                  (click)="clearSearchHistory()"
                  aria-label="Clear search history">
                  <i class="fas fa-times" aria-hidden="true"></i>
                </button>
              </div>
              
              @for (search of recentSearches().slice(0, 5); track search; let i = $index) {
                <button
                  class="suggestion-item recent-search"
                  [class.highlighted]="highlightedIndex() === i"
                  (click)="selectSuggestion(search, 'recent')"
                  (mouseenter)="setHighlightedIndex(i)"
                  role="option"
                  [attr.aria-selected]="highlightedIndex() === i">
                  <div class="suggestion-icon">
                    <i class="fas fa-clock" aria-hidden="true"></i>
                  </div>
                  <div class="suggestion-content">
                    <span class="suggestion-text">{{ search }}</span>
                  </div>
                </button>
              }
            </div>
          }
          
          <!-- Quick Actions -->
          @if (quickActions().length > 0 && !query()) {
            <div class="suggestion-section">
              <div class="section-header">
                <i class="fas fa-bolt" aria-hidden="true"></i>
                <span>Quick Actions</span>
              </div>
              
              @for (action of quickActions(); track action.id; let i = $index) {
                <button
                  class="suggestion-item quick-action"
                  [class.highlighted]="highlightedIndex() === (recentSearches().length + i)"
                  (click)="selectSuggestion(action.text, 'action', action)"
                  (mouseenter)="setHighlightedIndex(recentSearches().length + i)"
                  role="option"
                  [attr.aria-selected]="highlightedIndex() === (recentSearches().length + i)">
                  <div class="suggestion-icon">
                    <i [class]="action.icon" aria-hidden="true"></i>
                  </div>
                  <div class="suggestion-content">
                    <span class="suggestion-text">{{ action.text }}</span>
                    <span class="suggestion-category">{{ action.category }}</span>
                  </div>
                </button>
              }
            </div>
          }
          
          <!-- Search Results -->
          @if (suggestions().length > 0 && query()) {
            <div class="suggestion-section">
              <div class="section-header">
                <i class="fas fa-search" aria-hidden="true"></i>
                <span>Search Results</span>
              </div>
              
              @for (suggestion of suggestions(); track suggestion.id; let i = $index) {
                <button
                  class="suggestion-item search-result"
                  [class.highlighted]="highlightedIndex() === i"
                  (click)="selectSuggestion(suggestion.text, suggestion.type, suggestion)"
                  (mouseenter)="setHighlightedIndex(i)"
                  role="option"
                  [attr.aria-selected]="highlightedIndex() === i">
                  <div class="suggestion-icon">
                    <i [class]="suggestion.icon" aria-hidden="true"></i>
                  </div>
                  <div class="suggestion-content">
                    <span class="suggestion-text" [innerHTML]="highlightQuery(suggestion.text)"></span>
                    <span class="suggestion-category">{{ suggestion.category }}</span>
                  </div>
                  @if (suggestion.type === 'project') {
                    <div class="suggestion-meta">
                      <span class="rating">
                        <i class="fas fa-star" aria-hidden="true"></i>
                        {{ suggestion.metadata?.rating || 'N/A' }}
                      </span>
                    </div>
                  }
                </button>
              }
            </div>
          }
          
          <!-- No Results -->
          @if (suggestions().length === 0 && query() && !isSearching()) {
            <div class="no-results">
              <div class="no-results-icon">
                <i class="fas fa-search" aria-hidden="true"></i>
              </div>
              <div class="no-results-content">
                <span class="no-results-title">No results found</span>
                <span class="no-results-subtitle">Try adjusting your search terms</span>
              </div>
            </div>
          }
        </div>
      }
    </div>
  `,
  styleUrls: ['./global-search.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class GlobalSearchComponent implements AfterViewInit {
  @ViewChild('searchInput', { static: true }) searchInput!: ElementRef<HTMLInputElement>;
  
  private readonly destroyRef = inject(DestroyRef);
  private readonly globalSearchService = inject(GlobalSearchService);

  // Input properties
  readonly query = input<string>('');
  readonly placeholder = input<string>('Search...');

  // Output events
  readonly search = output<string>();
  readonly focus = output<void>();
  readonly select = output<{query: string, type: string, data?: any}>();

  // Component state
  private readonly _isFocused = signal<boolean>(false);
  private readonly _isSearching = signal<boolean>(false);
  private readonly _highlightedIndex = signal<number>(-1);
  private readonly _suggestions = signal<SearchSuggestion[]>([]);
  private readonly _recentSearches = signal<string[]>([]);
  private readonly _quickActions = signal<SearchSuggestion[]>([]);

  // Public readonly signals
  readonly isFocused = this._isFocused.asReadonly();
  readonly isSearching = this._isSearching.asReadonly();
  readonly highlightedIndex = this._highlightedIndex.asReadonly();
  readonly suggestions = this._suggestions.asReadonly();
  readonly recentSearches = this._recentSearches.asReadonly();
  readonly quickActions = this._quickActions.asReadonly();

  // Computed properties
  readonly showSuggestions = computed(() => 
    this._isFocused() && (
      this._suggestions().length > 0 || 
      this._recentSearches().length > 0 || 
      this._quickActions().length > 0 ||
      (this.query() && !this._isSearching())
    )
  );

  readonly totalSuggestions = computed(() => 
    this._suggestions().length + this._recentSearches().length + this._quickActions().length
  );

  constructor() {
    this.loadSearchHistory();
    this.loadQuickActions();
  }

  ngAfterViewInit(): void {
    this.setupSearchInput();
  }

  /**
   * Handle input focus
   */
  onFocus(): void {
    this._isFocused.set(true);
    this.focus.emit();
    
    // Load suggestions if we have a query
    if (this.query()) {
      this.performSearch(this.query());
    }
  }

  /**
   * Handle input blur with delay to allow for clicks
   */
  onBlur(): void {
    setTimeout(() => {
      this._isFocused.set(false);
      this._highlightedIndex.set(-1);
    }, 150);
  }

  /**
   * Handle keyboard navigation
   */
  onKeyDown(event: KeyboardEvent): void {
    const totalItems = this.totalSuggestions();
    
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        const nextIndex = this._highlightedIndex() < totalItems - 1 ? this._highlightedIndex() + 1 : 0;
        this._highlightedIndex.set(nextIndex);
        break;
        
      case 'ArrowUp':
        event.preventDefault();
        const prevIndex = this._highlightedIndex() > 0 ? this._highlightedIndex() - 1 : totalItems - 1;
        this._highlightedIndex.set(prevIndex);
        break;
        
      case 'Enter':
        event.preventDefault();
        this.selectHighlightedSuggestion();
        break;
        
      case 'Escape':
        this.clearSearch();
        if (this.searchInput?.nativeElement) {
          this.searchInput.nativeElement.blur();
        }
        break;
    }
  }

  /**
   * Clear search input
   */
  clearSearch(): void {
    this.search.emit('');
    this._suggestions.set([]);
    this._highlightedIndex.set(-1);
    if (this.searchInput?.nativeElement) {
      this.searchInput.nativeElement.focus();
    }
  }

  /**
   * Clear search history
   */
  clearSearchHistory(): void {
    this._recentSearches.set([]);
    localStorage.removeItem('mlo-search-history');
  }

  /**
   * Set highlighted index for keyboard navigation
   */
  setHighlightedIndex(index: number): void {
    this._highlightedIndex.set(index);
  }

  /**
   * Select a suggestion
   */
  selectSuggestion(text: string, type: string, data?: any): void {
    this.search.emit(text);
    this.select.emit({ query: text, type, data });
    
    // Add to search history if it's a search query
    if (type === 'recent' || type === 'project' || type === 'template') {
      this.addToSearchHistory(text);
    }
    
    this._isFocused.set(false);
    this._highlightedIndex.set(-1);
  }

  /**
   * Select currently highlighted suggestion
   */
  private selectHighlightedSuggestion(): void {
    const index = this._highlightedIndex();
    if (index === -1) return;

    const recentCount = this._recentSearches().length;
    const quickActionCount = this._quickActions().length;

    if (index < recentCount) {
      // Recent search
      const search = this._recentSearches()[index];
      this.selectSuggestion(search, 'recent');
    } else if (index < recentCount + quickActionCount) {
      // Quick action
      const action = this._quickActions()[index - recentCount];
      this.selectSuggestion(action.text, 'action', action);
    } else {
      // Search result
      const suggestion = this._suggestions()[index - recentCount - quickActionCount];
      this.selectSuggestion(suggestion.text, suggestion.type, suggestion);
    }
  }

  /**
   * Highlight query text in suggestions
   */
  highlightQuery(text: string): string {
    const query = this.query();
    if (!query) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  }

  /**
   * Setup debounced search input
   */
  private setupSearchInput(): void {
    if (!this.searchInput?.nativeElement) {

      return;
    }

    fromEvent(this.searchInput.nativeElement, 'input')
      .pipe(
        map((event: any) => event.target.value),
        debounceTime(300),
        distinctUntilChanged(),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(query => {
        this.search.emit(query);
        if (query) {
          this.performSearch(query);
        } else {
          this._suggestions.set([]);
        }
      });
  }

  /**
   * Perform search and get suggestions
   */
  private performSearch(query: string): void {
    if (!query.trim()) {
      this._suggestions.set([]);
      return;
    }

    this._isSearching.set(true);
    
    this.globalSearchService.search(query).subscribe({
      next: (results: any) => {
        this._suggestions.set(results.suggestions);
        this._isSearching.set(false);
      },
      error: (error: any) => {

        this._suggestions.set([]);
        this._isSearching.set(false);
      }
    });
  }

  /**
   * Load search history from localStorage
   */
  private loadSearchHistory(): void {
    try {
      const history = localStorage.getItem('mlo-search-history');
      if (history) {
        this._recentSearches.set(JSON.parse(history));
      }
    } catch (error) {

    }
  }

  /**
   * Add search to history
   */
  private addToSearchHistory(query: string): void {
    const current = this._recentSearches();
    const updated = [query, ...current.filter(item => item !== query)].slice(0, 10);
    
    this._recentSearches.set(updated);
    
    try {
      localStorage.setItem('mlo-search-history', JSON.stringify(updated));
    } catch (error) {

    }
  }

  /**
   * Load quick actions
   */
  private loadQuickActions(): void {
    const actions: SearchSuggestion[] = [
      {
        id: 'new-project',
        text: 'Create new project',
        type: 'action',
        icon: 'fas fa-plus',
        category: 'Quick Action'
      },
      {
        id: 'browse-templates',
        text: 'Browse templates',
        type: 'action',
        icon: 'fas fa-th-large',
        category: 'Quick Action'
      },
      {
        id: 'community-showcase',
        text: 'Explore community',
        type: 'action',
        icon: 'fas fa-users',
        category: 'Quick Action'
      }
    ];
    
    this._quickActions.set(actions);
  }
}
