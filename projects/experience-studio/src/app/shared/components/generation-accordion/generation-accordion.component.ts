import { Component, Input, OnInit, OnDestroy, ChangeDetectionStrategy, signal, inject, ViewChild, ElementRef, AfterViewInit, DestroyRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FileOpeningService } from '../../services/file-opening.service';
import { ErrorReportingService } from '../../services/error-reporting.service';
import { RegenerationIntegrationService } from '../../services/regeneration-integration.service';
import { createLogger } from '../../utils';

export interface GenerationResult {
  type: 'success' | 'error';
  version: number;
  projectName: string;
  files?: string[];
  errorMessage?: string;
  timestamp: Date;
  isLatest?: boolean;
  // ENHANCED: Add project and job IDs for retry functionality
  projectId?: string;
  jobId?: string;
}

@Component({
  selector: 'app-generation-accordion',
  standalone: true,
  imports: [CommonModule, MatButtonModule, MatIconModule],
  templateUrl: './generation-accordion.component.html',
  styleUrl: './generation-accordion.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class GenerationAccordionComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() result: GenerationResult | null = null;
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() defaultExpanded: boolean = false;
  // ENHANCED: Accept project and job IDs for retry functionality
  @Input() projectId: string | null = null;
  @Input() jobId: string | null = null;

  // ViewChild references for width detection
  @ViewChild('accordionContainer', { static: false }) accordionContainer!: ElementRef<HTMLElement>;
  @ViewChild('versionTitle', { static: false }) versionTitle!: ElementRef<HTMLElement>;
  @ViewChild('timestamp', { static: false }) timestamp!: ElementRef<HTMLElement>;

  // Angular Signals for reactive state management
  isExpanded = signal(false);
  isCompactMode = signal(false);
  private retryingState = signal(false);

  // Inject services
  private fileOpeningService = inject(FileOpeningService);
  private errorReportingService = inject(ErrorReportingService);
  private destroyRef = inject(DestroyRef);
  // ENHANCED: Inject regeneration integration service for retry state management
  private regenerationIntegrationService = inject(RegenerationIntegrationService);

  // Resize observer for width detection
  private resizeObserver: ResizeObserver | null = null;
  private readonly COMPACT_WIDTH_THRESHOLD = 600;

  ngOnInit(): void {
    this.isExpanded.set(this.defaultExpanded);
  }

  ngAfterViewInit(): void {
    // Set up width detection after view is initialized
    this.setupWidthDetection();
  }

  ngOnDestroy(): void {
    // Clean up resize observer
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = null;
    }
  }

  /**
   * Toggle accordion expansion state
   */
  toggleAccordion(): void {
    this.isExpanded.update(expanded => !expanded);
  }

  /**
   * Get the appropriate CSS class for the result type
   */
  getResultClass(): string {
    if (!this.result) return '';

    return `result-${this.result.type}`;
  }

  /**
   * Get formatted timestamp in 12-hour format
   */
  getFormattedTimestamp(): string {
    if (!this.result?.timestamp) return '';

    return this.result.timestamp.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  }

  /**
   * Get version title with project name (responsive)
   */
  getVersionTitle(): string {
    if (!this.result) return '';

    // In compact mode, show only version number
    if (this.isCompactMode()) {
      return `Version ${this.result.version}`;
    }

    return `Version ${this.result.version} - ${this.result.projectName}`;
  }

  /**
   * Get file count text
   */
  getFileCountText(): string {
    if (!this.result?.files) return '';

    const count = this.result.files.length;
    return count === 1 ? '1 file' : `${count} files`;
  }

  /**
   * Handle file click to open in code viewer
   * Only works for the latest accordion
   */
  onFileClick(filePath: string): void {
    if (!filePath || !this.result?.isLatest) {
      // Only allow file clicks for the latest accordion
      return;
    }

    // Extract file name from path
    const fileName = this.fileOpeningService.extractFileName(filePath);

    // Request to open the file
    this.fileOpeningService.openFile(fileName, filePath, 'accordion');
  }

  /**
   * ENHANCED: Check if retry is in progress
   */
  isRetrying(): boolean {
    return this.retryingState();
  }

  /**
   * ENHANCED: Handle retry regeneration button click
   * Calls error reporting service, re-enables prompt bar, and appends error message with "fix it"
   * CRITICAL: Integrates with chat-window component for prompt bar management
   */
  onRetryRegeneration(result: GenerationResult | null): void {
    if (!result || result.type !== 'error' || this.retryingState()) {
      return;
    }

    this.retryingState.set(true);

    // ENHANCED: Update regeneration integration service retry state
    this.regenerationIntegrationService.setRetryState(true);

    // ENHANCED: Get project and job IDs from inputs or result
    const projectId = this.projectId || result.projectId;
    const statusId = this.jobId || result.jobId;
    const errorMessage = result.errorMessage || 'Code regeneration failed';

    if (!projectId || !statusId) {
      this.retryingState.set(false);
      // ENHANCED: Update regeneration integration service retry state
      this.regenerationIntegrationService.setRetryState(false);
      return;
    }

    // CRITICAL: Trigger chat-window prompt bar re-enablement and error message appending
    this.triggerChatWindowRetry(errorMessage);

    // Call error reporting service to retry regeneration
    this.errorReportingService.reportCodeGenerationBuildError(
      projectId,
      statusId,
      errorMessage
    ).pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe({
      next: (response) => {

        // Note: SSE connection continues automatically - no need to create new connection
        this.retryingState.set(false);
        // ENHANCED: Update regeneration integration service retry state
        this.regenerationIntegrationService.setRetryState(false);

        // CRITICAL: Hide retry button after successful retry initiation
        this.hideRetryButton();
      },
      error: (error) => {

        this.retryingState.set(false);
        // ENHANCED: Update regeneration integration service retry state
        this.regenerationIntegrationService.setRetryState(false);
      }
    });
  }

  /**
   * ENHANCED: Trigger chat-window retry functionality
   * Re-enables prompt bar and appends error message with "fix it"
   */
  private triggerChatWindowRetry(errorMessage: string): void {

    // CRITICAL: Use regeneration integration service to coordinate with chat-window
    // This will trigger the chat-window to re-enable prompt bar and append error message
    this.regenerationIntegrationService.triggerChatWindowRetry(errorMessage);
  }

  /**
   * ENHANCED: Hide retry button after successful retry
   * This prevents multiple retry attempts and provides better UX
   */
  private hideRetryButton(): void {

    // The retry button will be hidden automatically when retryingState is false
    // and the error accordion will be replaced by new generation results
  }

  /**
   * Set up width detection using ResizeObserver
   */
  private setupWidthDetection(): void {
    if (!this.accordionContainer?.nativeElement) {
      return;
    }

    // Use ResizeObserver for better performance than window resize events
    if (typeof ResizeObserver !== 'undefined') {
      this.resizeObserver = new ResizeObserver(entries => {
        for (const entry of entries) {
          this.checkContainerWidth(entry.contentRect.width);
        }
      });

      this.resizeObserver.observe(this.accordionContainer.nativeElement);
    } else {
      // Fallback for browsers that don't support ResizeObserver
      this.setupFallbackWidthDetection();
    }

    // Initial check
    this.checkContainerWidth(this.accordionContainer.nativeElement.offsetWidth);
  }

  /**
   * Fallback width detection using window resize events
   */
  private setupFallbackWidthDetection(): void {
    const checkWidth = () => {
      if (this.accordionContainer?.nativeElement) {
        this.checkContainerWidth(this.accordionContainer.nativeElement.offsetWidth);
      }
    };

    // Initial check
    checkWidth();

    // Listen to window resize
    window.addEventListener('resize', checkWidth);

    // Store reference for cleanup (if needed)
    // Note: In a real implementation, you'd want to store this reference and remove the listener in ngOnDestroy
  }

  /**
   * Check container width and update compact mode
   */
  private checkContainerWidth(width: number): void {
    const shouldBeCompact = width < this.COMPACT_WIDTH_THRESHOLD;

    if (this.isCompactMode() !== shouldBeCompact) {
      this.isCompactMode.set(shouldBeCompact);
      this.updateElementVisibility(shouldBeCompact);
    }
  }

  /**
   * Update element visibility based on compact mode
   */
  private updateElementVisibility(isCompact: boolean): void {
    // Hide/show timestamp
    if (this.timestamp?.nativeElement) {
      this.timestamp.nativeElement.style.display = isCompact ? 'none' : '';
    }
  }
}
