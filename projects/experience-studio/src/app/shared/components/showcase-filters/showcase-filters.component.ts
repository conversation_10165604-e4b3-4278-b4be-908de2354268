import { Component, ChangeDetectionStrategy, input, output, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { FilterState } from '../../services/workspace-state.service';
import { AvailableFilters } from '../../services/community-projects.service';

/**
 * Showcase Filters Component - Advanced filtering sidebar for community projects
 * Provides comprehensive filtering options with collapsible groups
 * 
 * Features:
 * - Collapsible filter groups
 * - Multi-select checkboxes
 * - Date range picker
 * - Creator search
 * - Active filter indicators
 * - Clear filters functionality
 * - Accessibility compliant
 */
@Component({
  selector: 'app-showcase-filters',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="showcase-filters">
      <!-- Filters Header -->
      <div class="filters-header">
        <h3 class="filters-title">Filters</h3>
        @if (activeFilterCount() > 0) {
          <button 
            class="clear-all-btn"
            (click)="onClearFilters()"
            [attr.aria-label]="'Clear all ' + activeFilterCount() + ' filters'">
            <i class="fas fa-times" aria-hidden="true"></i>
            Clear All ({{ activeFilterCount() }})
          </button>
        }
      </div>

      <!-- Filter Groups -->
      <div class="filter-groups">
        <!-- Project Type Filter -->
        <div class="filter-group" [class.expanded]="expandedGroups().includes('projectTypes')">
          <button 
            class="filter-group-header"
            (click)="toggleGroup('projectTypes')"
            [attr.aria-expanded]="expandedGroups().includes('projectTypes')"
            aria-controls="project-types-content">
            <div class="group-title">
              <i class="fas fa-th-large" aria-hidden="true"></i>
              <span>Project Type</span>
              @if (selectedProjectTypes().length > 0) {
                <span class="active-count">({{ selectedProjectTypes().length }})</span>
              }
            </div>
            <i [class]="expandedGroups().includes('projectTypes') ? 'fas fa-chevron-up' : 'fas fa-chevron-down'" aria-hidden="true"></i>
          </button>
          
          @if (expandedGroups().includes('projectTypes')) {
            <div id="project-types-content" class="filter-group-content">
              @for (type of availableFilters().projectTypes; track type.id) {
                <label class="filter-option">
                  <input
                    type="checkbox"
                    [checked]="selectedProjectTypes().includes(type.id)"
                    (change)="onProjectTypeChange(type.id, $event)"
                    [attr.aria-describedby]="type.id + '-count'">
                  <span class="checkbox-custom"></span>
                  <span class="option-label">{{ type.name }}</span>
                  <span [id]="type.id + '-count'" class="option-count">({{ type.count }})</span>
                </label>
              }
            </div>
          }
        </div>

        <!-- Technology Filter -->
        <div class="filter-group" [class.expanded]="expandedGroups().includes('technologies')">
          <button 
            class="filter-group-header"
            (click)="toggleGroup('technologies')"
            [attr.aria-expanded]="expandedGroups().includes('technologies')"
            aria-controls="technologies-content">
            <div class="group-title">
              <i class="fas fa-code" aria-hidden="true"></i>
              <span>Technology</span>
              @if (selectedTechnologies().length > 0) {
                <span class="active-count">({{ selectedTechnologies().length }})</span>
              }
            </div>
            <i [class]="expandedGroups().includes('technologies') ? 'fas fa-chevron-up' : 'fas fa-chevron-down'" aria-hidden="true"></i>
          </button>
          
          @if (expandedGroups().includes('technologies')) {
            <div id="technologies-content" class="filter-group-content">
              <!-- Technology Search -->
              <div class="filter-search">
                <input
                  type="text"
                  class="search-input"
                  placeholder="Search technologies..."
                  [value]="technologySearch()"
                  (input)="onTechnologySearch($event)"
                  aria-label="Search technologies">
                <i class="fas fa-search search-icon" aria-hidden="true"></i>
              </div>
              
              @for (tech of filteredTechnologies(); track tech.id) {
                <label class="filter-option">
                  <input
                    type="checkbox"
                    [checked]="selectedTechnologies().includes(tech.id)"
                    (change)="onTechnologyChange(tech.id, $event)"
                    [attr.aria-describedby]="tech.id + '-count'">
                  <span class="checkbox-custom"></span>
                  <span class="option-label">{{ tech.name }}</span>
                  <span [id]="tech.id + '-count'" class="option-count">({{ tech.count }})</span>
                </label>
              }
              
              @if (filteredTechnologies().length === 0 && technologySearch()) {
                <div class="no-results">
                  <i class="fas fa-search" aria-hidden="true"></i>
                  <span>No technologies found</span>
                </div>
              }
            </div>
          }
        </div>

        <!-- Difficulty Filter -->
        <div class="filter-group" [class.expanded]="expandedGroups().includes('difficulty')">
          <button 
            class="filter-group-header"
            (click)="toggleGroup('difficulty')"
            [attr.aria-expanded]="expandedGroups().includes('difficulty')"
            aria-controls="difficulty-content">
            <div class="group-title">
              <i class="fas fa-signal" aria-hidden="true"></i>
              <span>Difficulty</span>
              @if (selectedDifficulty() !== 'all') {
                <span class="active-count">(1)</span>
              }
            </div>
            <i [class]="expandedGroups().includes('difficulty') ? 'fas fa-chevron-up' : 'fas fa-chevron-down'" aria-hidden="true"></i>
          </button>
          
          @if (expandedGroups().includes('difficulty')) {
            <div id="difficulty-content" class="filter-group-content">
              <label class="filter-option radio-option">
                <input
                  type="radio"
                  name="difficulty"
                  value="all"
                  [checked]="selectedDifficulty() === 'all'"
                  (change)="onDifficultyChange('all')">
                <span class="radio-custom"></span>
                <span class="option-label">All Levels</span>
              </label>
              
              @for (difficulty of availableFilters().difficulties; track difficulty.id) {
                <label class="filter-option radio-option">
                  <input
                    type="radio"
                    name="difficulty"
                    [value]="difficulty.id"
                    [checked]="selectedDifficulty() === difficulty.id"
                    (change)="onDifficultyChange(difficulty.id)"
                    [attr.aria-describedby]="difficulty.id + '-count'">
                  <span class="radio-custom"></span>
                  <span class="option-label">{{ difficulty.name }}</span>
                  <span [id]="difficulty.id + '-count'" class="option-count">({{ difficulty.count }})</span>
                </label>
              }
            </div>
          }
        </div>

        <!-- Date Range Filter -->
        <div class="filter-group" [class.expanded]="expandedGroups().includes('dateRange')">
          <button 
            class="filter-group-header"
            (click)="toggleGroup('dateRange')"
            [attr.aria-expanded]="expandedGroups().includes('dateRange')"
            aria-controls="date-range-content">
            <div class="group-title">
              <i class="fas fa-calendar" aria-hidden="true"></i>
              <span>Date Created</span>
              @if (hasDateRange()) {
                <span class="active-count">(1)</span>
              }
            </div>
            <i [class]="expandedGroups().includes('dateRange') ? 'fas fa-chevron-up' : 'fas fa-chevron-down'" aria-hidden="true"></i>
          </button>
          
          @if (expandedGroups().includes('dateRange')) {
            <div id="date-range-content" class="filter-group-content">
              <!-- Quick Date Ranges -->
              <div class="quick-ranges">
                @for (range of quickDateRanges; track range.value) {
                  <button
                    class="quick-range-btn"
                    [class.active]="selectedQuickRange() === range.value"
                    (click)="onQuickRangeSelect(range.value)">
                    {{ range.label }}
                  </button>
                }
              </div>
              
              <!-- Custom Date Range -->
              <div class="custom-date-range">
                <div class="date-input-group">
                  <label for="start-date" class="date-label">From</label>
                  <input
                    id="start-date"
                    type="date"
                    class="date-input"
                    [value]="startDate()"
                    (change)="onStartDateChange($event)">
                </div>
                
                <div class="date-input-group">
                  <label for="end-date" class="date-label">To</label>
                  <input
                    id="end-date"
                    type="date"
                    class="date-input"
                    [value]="endDate()"
                    (change)="onEndDateChange($event)">
                </div>
              </div>
              
              @if (hasDateRange()) {
                <button 
                  class="clear-date-range"
                  (click)="clearDateRange()">
                  <i class="fas fa-times" aria-hidden="true"></i>
                  Clear date range
                </button>
              }
            </div>
          }
        </div>

        <!-- Creator Filter -->
        <div class="filter-group" [class.expanded]="expandedGroups().includes('creator')">
          <button 
            class="filter-group-header"
            (click)="toggleGroup('creator')"
            [attr.aria-expanded]="expandedGroups().includes('creator')"
            aria-controls="creator-content">
            <div class="group-title">
              <i class="fas fa-user" aria-hidden="true"></i>
              <span>Creator</span>
              @if (selectedCreator()) {
                <span class="active-count">(1)</span>
              }
            </div>
            <i [class]="expandedGroups().includes('creator') ? 'fas fa-chevron-up' : 'fas fa-chevron-down'" aria-hidden="true"></i>
          </button>
          
          @if (expandedGroups().includes('creator')) {
            <div id="creator-content" class="filter-group-content">
              <!-- Creator Search -->
              <div class="filter-search">
                <input
                  type="text"
                  class="search-input"
                  placeholder="Search creators..."
                  [value]="creatorSearch()"
                  (input)="onCreatorSearch($event)"
                  aria-label="Search creators">
                <i class="fas fa-search search-icon" aria-hidden="true"></i>
              </div>
              
              @for (creator of filteredCreators(); track creator.id) {
                <label class="filter-option creator-option">
                  <input
                    type="radio"
                    name="creator"
                    [value]="creator.id"
                    [checked]="selectedCreator() === creator.id"
                    (change)="onCreatorChange(creator.id)">
                  <span class="radio-custom"></span>
                  <span class="option-label">{{ creator.name }}</span>
                  <span class="option-count">({{ creator.count }})</span>
                </label>
              }
              
              @if (selectedCreator()) {
                <button 
                  class="clear-creator"
                  (click)="clearCreator()">
                  <i class="fas fa-times" aria-hidden="true"></i>
                  Clear creator filter
                </button>
              }
            </div>
          }
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./showcase-filters.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ShowcaseFiltersComponent {
  // Input properties
  readonly filters = input.required<FilterState>();
  readonly availableFilters = input.required<AvailableFilters>();

  // Output events
  readonly filterChange = output<Partial<FilterState>>();
  readonly clearFilters = output<void>();

  // Component state
  private readonly _expandedGroups = signal<string[]>(['projectTypes', 'technologies']);
  private readonly _technologySearch = signal<string>('');
  private readonly _creatorSearch = signal<string>('');
  private readonly _selectedQuickRange = signal<string>('');

  // Public readonly signals
  readonly expandedGroups = this._expandedGroups.asReadonly();
  readonly technologySearch = this._technologySearch.asReadonly();
  readonly creatorSearch = this._creatorSearch.asReadonly();
  readonly selectedQuickRange = this._selectedQuickRange.asReadonly();

  // Quick date range options
  readonly quickDateRanges = [
    { value: 'today', label: 'Today' },
    { value: 'week', label: 'This Week' },
    { value: 'month', label: 'This Month' },
    { value: 'quarter', label: 'This Quarter' },
    { value: 'year', label: 'This Year' }
  ];

  // Computed properties
  readonly selectedProjectTypes = computed(() => this.filters().projectTypes || []);
  readonly selectedTechnologies = computed(() => this.filters().technologies || []);
  readonly selectedDifficulty = computed(() => this.filters().difficulty || 'all');
  readonly selectedCreator = computed(() => this.filters().creator || '');
  readonly startDate = computed(() => this.formatDate(this.filters().dateRange?.start));
  readonly endDate = computed(() => this.formatDate(this.filters().dateRange?.end));

  readonly hasDateRange = computed(() => 
    this.filters().dateRange?.start !== null || this.filters().dateRange?.end !== null
  );

  readonly activeFilterCount = computed(() => {
    let count = 0;
    if (this.selectedProjectTypes().length > 0) count++;
    if (this.selectedTechnologies().length > 0) count++;
    if (this.selectedDifficulty() !== 'all') count++;
    if (this.hasDateRange()) count++;
    if (this.selectedCreator()) count++;
    return count;
  });

  readonly filteredTechnologies = computed(() => {
    const search = this._technologySearch().toLowerCase();
    if (!search) return this.availableFilters().technologies;
    
    return this.availableFilters().technologies.filter(tech =>
      tech.name.toLowerCase().includes(search)
    );
  });

  readonly filteredCreators = computed(() => {
    const search = this._creatorSearch().toLowerCase();
    if (!search) return this.availableFilters().creators;
    
    return this.availableFilters().creators.filter(creator =>
      creator.name.toLowerCase().includes(search)
    );
  });

  /**
   * Toggle filter group expansion
   */
  toggleGroup(groupName: string): void {
    const expanded = this._expandedGroups();
    if (expanded.includes(groupName)) {
      this._expandedGroups.set(expanded.filter(g => g !== groupName));
    } else {
      this._expandedGroups.set([...expanded, groupName]);
    }
  }

  /**
   * Handle project type change
   */
  onProjectTypeChange(typeId: string, event: Event): void {
    const target = event.target as HTMLInputElement;
    const current = this.selectedProjectTypes();
    
    const updated = target.checked
      ? [...current, typeId]
      : current.filter(id => id !== typeId);
    
    this.filterChange.emit({ projectTypes: updated });
  }

  /**
   * Handle technology search
   */
  onTechnologySearch(event: Event): void {
    const target = event.target as HTMLInputElement;
    this._technologySearch.set(target.value);
  }

  /**
   * Handle technology change
   */
  onTechnologyChange(techId: string, event: Event): void {
    const target = event.target as HTMLInputElement;
    const current = this.selectedTechnologies();
    
    const updated = target.checked
      ? [...current, techId]
      : current.filter(id => id !== techId);
    
    this.filterChange.emit({ technologies: updated });
  }

  /**
   * Handle difficulty change
   */
  onDifficultyChange(difficulty: string): void {
    this.filterChange.emit({ difficulty: difficulty as any });
  }

  /**
   * Handle quick date range selection
   */
  onQuickRangeSelect(range: string): void {
    this._selectedQuickRange.set(range);
    const dateRange = this.getDateRangeForQuickSelection(range);
    this.filterChange.emit({ dateRange });
  }

  /**
   * Handle start date change
   */
  onStartDateChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const startDate = target.value ? new Date(target.value) : null;
    const currentRange = this.filters().dateRange;
    
    this.filterChange.emit({
      dateRange: {
        start: startDate,
        end: currentRange?.end || null
      }
    });
    this._selectedQuickRange.set('');
  }

  /**
   * Handle end date change
   */
  onEndDateChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const endDate = target.value ? new Date(target.value) : null;
    const currentRange = this.filters().dateRange;
    
    this.filterChange.emit({
      dateRange: {
        start: currentRange?.start || null,
        end: endDate
      }
    });
    this._selectedQuickRange.set('');
  }

  /**
   * Clear date range
   */
  clearDateRange(): void {
    this.filterChange.emit({
      dateRange: { start: null, end: null }
    });
    this._selectedQuickRange.set('');
  }

  /**
   * Handle creator search
   */
  onCreatorSearch(event: Event): void {
    const target = event.target as HTMLInputElement;
    this._creatorSearch.set(target.value);
  }

  /**
   * Handle creator change
   */
  onCreatorChange(creatorId: string): void {
    this.filterChange.emit({ creator: creatorId });
  }

  /**
   * Clear creator filter
   */
  clearCreator(): void {
    this.filterChange.emit({ creator: '' });
  }

  /**
   * Clear all filters
   */
  onClearFilters(): void {
    this.clearFilters.emit();
    this._selectedQuickRange.set('');
  }

  /**
   * Format date for input
   */
  private formatDate(date: Date | null | undefined): string {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  }

  /**
   * Get date range for quick selection
   */
  private getDateRangeForQuickSelection(range: string): { start: Date | null; end: Date | null } {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    switch (range) {
      case 'today':
        return { start: today, end: new Date(today.getTime() + 24 * 60 * 60 * 1000) };
      case 'week':
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        return { start: weekStart, end: now };
      case 'month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        return { start: monthStart, end: now };
      case 'quarter':
        const quarterStart = new Date(today.getFullYear(), Math.floor(today.getMonth() / 3) * 3, 1);
        return { start: quarterStart, end: now };
      case 'year':
        const yearStart = new Date(today.getFullYear(), 0, 1);
        return { start: yearStart, end: now };
      default:
        return { start: null, end: null };
    }
  }
}
