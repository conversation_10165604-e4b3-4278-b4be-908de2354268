import { Component, Input, OnInit, OnChanges, ChangeDetectionStrategy, signal, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface LayoutData {
  key: string;
  name: string;
}

@Component({
  selector: 'app-analyzing-layout-animation',
  standalone: true,
  imports: [CommonModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div class="analyzing-layout-container" [ngClass]="theme + '-theme'">
      <!-- Main view is controlled by the isAnalyzing signal -->
      <ng-container *ngIf="isAnalyzing(); else artifactView">
        <div class="analysis-container">
          <div class="analysis-header">
            <h3>Analyzing Layout for You...</h3>
            <div class="loading-spinner"></div>
          </div>

          <div class="layouts-carousel">
            <div class="layouts-carousel-track">
              <!-- CORRECTED: Use the duplicatedLayouts signal and the trackBy function -->
              <div *ngFor="let layout of duplicatedLayouts(); trackBy: trackByLayoutKey" class="layout-item">
                <div class="layout-label">{{ layout.name }}</div>

                <!-- Dynamically render the mini layout based on its key -->
                <div class="mini-layout" [ngSwitch]="layout.key">
                  <ng-container *ngSwitchCase="'HB'"><div class="mini-header"></div><div class="mini-body"></div></ng-container>
                  <ng-container *ngSwitchCase="'HBF'"><div class="mini-header"></div><div class="mini-body"></div><div class="mini-footer"></div></ng-container>
                  <ng-container *ngSwitchCase="'HLSB'"><div class="mini-header"></div><div class="mini-content"><div class="mini-sidebar-left"></div><div class="mini-body"></div></div></ng-container>
                  <ng-container *ngSwitchCase="'HLSBF'"><div class="mini-header"></div><div class="mini-content"><div class="mini-sidebar-left"></div><div class="mini-body"></div></div><div class="mini-footer"></div></ng-container>
                  <ng-container *ngSwitchCase="'HBRS'"><div class="mini-header"></div><div class="mini-content"><div class="mini-body"></div><div class="mini-sidebar-right"></div></div></ng-container>
                  <ng-container *ngSwitchCase="'HBRSF'"><div class="mini-header"></div><div class="mini-content"><div class="mini-body"></div><div class="mini-sidebar-right"></div></div><div class="mini-footer"></div></ng-container>
                  <ng-container *ngSwitchCase="'HLSBRS'"><div class="mini-header"></div><div class="mini-content"><div class="mini-sidebar-left"></div><div class="mini-body"></div><div class="mini-sidebar-right"></div></div></ng-container>
                  <ng-container *ngSwitchCase="'HLSBRSF'"><div class="mini-header"></div><div class="mini-content"><div class="mini-sidebar-left"></div><div class="mini-body"></div><div class="mini-sidebar-right"></div></div><div class="mini-footer"></div></ng-container>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ng-container>

      <!-- Artifact View: This is shown when isAnalyzing() is false -->
      <ng-template #artifactView>
        <div class="artifact-container">
            <h3>Layout Identified</h3>
            <div class="layout-item">
              <div class="layout-label">{{ getDefaultLayout().name }}</div>
              <div class="mini-layout" [ngSwitch]="getDefaultLayout().key">
                <!-- Add the same ng-containers here if you want to show the final layout -->
              </div>
            </div>
        </div>
      </ng-template>
    </div>
  `,
  styles: [`
    :host {
      display: block;
      width: 100%;
      height: 100%;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    }

    .analyzing-layout-container {
      background: transparent !important;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.3s ease, color 0.3s ease;
      overflow: hidden;
    }

    .analyzing-layout-container.light-theme {
      --bg-color: #f4f7fa;
      --text-color: #333;
      --header-color: #e3eaf3;
      --body-color: #ffffff;
      --sidebar-color: #f0f3f8;
      --border-color: #d1d9e6;
      --spinner-color: #6d6d6d;
      --label-color: #555;
      --mask-gradient: linear-gradient(to right, rgba(244, 247, 250, 0), rgba(244, 247, 250, 1) 15%, rgba(244, 247, 250, 1) 85%, rgba(244, 247, 250, 0));
    }

    .analyzing-layout-container.dark-theme {
      --bg-color: #1a1c23;
      --text-color: #e0e0e0;
      --header-color: #2a2d37;
      --body-color: #20222a;
      --sidebar-color: #252831;
      --border-color: #3a3f4c;
      --spinner-color: #b0b0b0;
      --label-color: #aaa;
      --mask-gradient: linear-gradient(to right, rgba(26, 28, 35, 0), rgba(26, 28, 35, 1) 15%, rgba(26, 28, 35, 1) 85%, rgba(26, 28, 35, 0));
    }

    .analyzing-layout-container {
      // background-color: var(--bg-color);
      color: var(--text-color);
    }

    .analysis-container, .artifact-container {
      width: 100%;
      max-width: 800px;
      padding: 2rem;
      text-align: center;
    }
    .artifact-container .layout-item {
        margin: 0 auto;
    }

    .analysis-header { display: flex; justify-content: center; align-items: center; gap: 1rem; margin-bottom: 2.5rem; }
    h3 { margin: 0; font-size: 1.5rem; font-weight: 500; }

    .loading-spinner {
      width: 24px; height: 24px;
      border: 3px solid var(--border-color);
      border-top-color: var(--spinner-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    /* CORRECTED KEYFRAMES */
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    .layouts-carousel { width: 100%; overflow: hidden; position: relative; -webkit-mask-image: var(--mask-gradient); mask-image: var(--mask-gradient); }
    .layouts-carousel-track {
      display: flex;
      width: calc(180px * 32); /* (160px item + 20px gap) * 32 items (8 * 4) */
      animation: scroll 40s linear infinite;
      gap: 20px;
    }
    @keyframes scroll {
      from { transform: translateX(0); }
      to { transform: translateX(-25%); } /* 1/4 of total width since we have 4 copies */
    }

    .layout-item {
      width: 160px; flex-shrink: 0;
      background: var(--body-color); border: 1px solid var(--border-color);
      border-radius: 8px; padding: 1rem;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      display: flex; flex-direction: column; gap: 0.75rem;
    }

    .layout-label { font-size: 0.8rem; font-weight: 500; color: var(--label-color); white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
    .mini-layout { height: 90px; display: flex; flex-direction: column; gap: 4px; border: 1px solid var(--border-color); padding: 4px; border-radius: 4px; }
    .mini-header, .mini-footer { height: 12px; flex-shrink: 0; background-color: var(--header-color); border-radius: 2px; }
    .mini-footer { margin-top: auto; }
    .mini-body { flex-grow: 1; background-color: var(--bg-color); border-radius: 2px; }
    .mini-content { flex-grow: 1; display: flex; gap: 4px; }
    .mini-sidebar-left, .mini-sidebar-right { width: 20px; flex-shrink: 0; background-color: var(--sidebar-color); border-radius: 2px; }
  `]
})
export class AnalyzingLayoutAnimationComponent implements OnInit, OnChanges {
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() layoutKey: string = 'HB';
  @Input() showAnalyzing: boolean = true;

  readonly isAnalyzing = signal<boolean>(true);
  readonly animationDuration = signal<number>(40);
  readonly duplicatedLayouts = signal<LayoutData[]>([]);

  private readonly layouts: LayoutData[] = [
    { key: 'HB', name: 'Header & Body' },
    { key: 'HBF', name: 'Header, Body & Footer' },
    { key: 'HLSB', name: 'Header, Left Sidebar & Body' },
    { key: 'HLSBF', name: 'HLSB & Footer' },
    { key: 'HBRS', name: 'Header, Body & Right Sidebar' },
    { key: 'HBRSF', name: 'HBRS & Footer' },
    { key: 'HLSBRS', name: 'Dual Sidebar' },
    { key: 'HLSBRSF', name: 'Dual Sidebar & Footer' }
  ];

  ngOnInit(): void {

    const duplicated = [...this.layouts, ...this.layouts, ...this.layouts, ...this.layouts];
    this.duplicatedLayouts.set(duplicated);
    this.isAnalyzing.set(this.showAnalyzing);
  }

  ngOnChanges(changes: SimpleChanges): void {
      if (changes['showAnalyzing']) {
        this.isAnalyzing.set(this.showAnalyzing);
      }
  }

  getLayoutClasses(layoutKey: string): string {
    return `mini-${layoutKey.toLowerCase()}`;
  }

  trackByLayoutKey(index: number, layout: LayoutData): string {
    return `${layout.key}-${index}`;
  }

  getDefaultLayout(): LayoutData {
    return this.layouts.find(layout => layout.key === this.layoutKey) || this.layouts[0];
  }

  startAnalyzing(): void {
    this.isAnalyzing.set(true);

  }

  stopAnalyzing(): void {
    this.isAnalyzing.set(false);

  }

  setAnimationSpeed(durationSeconds: number): void {
    this.animationDuration.set(durationSeconds);

  }
}
