import { Component, ChangeDetectionStrategy, input, output, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CommunityProject } from '../../services/workspace-state.service';
import { ProjectTypeTagComponent } from '../project-type-tag/project-type-tag.component';
import { PROJECT_TYPES } from '../../models/recent-creation.model';

/**
 * Community Project Card Component - Displays community projects with rich metadata
 * Supports both grid and list view modes with hover interactions
 * 
 * Features:
 * - Project type tags integration
 * - Rating and view count display
 * - Creator information with avatar
 * - Hover states and animations
 * - Favorite and share actions
 * - Responsive design
 * - Accessibility compliant
 */
@Component({
  selector: 'app-community-project-card',
  standalone: true,
  imports: [
    CommonModule,
    ProjectTypeTagComponent
  ],
  template: `
    <article 
      class="community-project-card" 
      [attr.data-view]="view()"
      [class.favorited]="isFavorited()"
      (click)="onCardClick()"
      (mouseenter)="onMouseEnter()"
      (mouseleave)="onMouseLeave()">
      
      <!-- Project Thumbnail -->
      <div class="project-thumbnail">
        @if (project().thumbnail) {
          <img 
            [src]="project().thumbnail" 
            [alt]="project().title + ' thumbnail'"
            class="thumbnail-image"
            loading="lazy">
        } @else {
          <div class="thumbnail-placeholder">
            <i [class]="getProjectIcon()" aria-hidden="true"></i>
          </div>
        }
        
        <!-- Hover Overlay -->
        <div class="thumbnail-overlay" [class.visible]="isHovered()">
          <button 
            class="overlay-action preview-btn"
            (click)="onPreview($event)"
            aria-label="Preview project">
            <i class="fas fa-eye" aria-hidden="true"></i>
            <span>Preview</span>
          </button>
          
          <button 
            class="overlay-action favorite-btn"
            [class.active]="isFavorited()"
            (click)="onFavorite($event)"
            [attr.aria-label]="isFavorited() ? 'Remove from favorites' : 'Add to favorites'">
            <i [class]="isFavorited() ? 'fas fa-heart' : 'far fa-heart'" aria-hidden="true"></i>
          </button>
        </div>
        
        <!-- Project Type Tag -->
        <div class="project-type-overlay">
          @if (getProjectType()) {
            <app-project-type-tag
              [projectType]="getProjectType()"
              size="small"
              variant="filled">
            </app-project-type-tag>
          }
        </div>
        
        <!-- Difficulty Badge -->
        <div class="difficulty-badge" [attr.data-difficulty]="project().difficulty">
          {{ getDifficultyLabel() }}
        </div>
      </div>
      
      <!-- Project Content -->
      <div class="project-content">
        <!-- Header -->
        <div class="project-header">
          <h3 class="project-title">{{ project().title }}</h3>
          
          <div class="project-actions">
            <button 
              class="action-btn share-btn"
              (click)="onShare($event)"
              aria-label="Share project">
              <i class="fas fa-share-alt" aria-hidden="true"></i>
            </button>
            
            <button 
              class="action-btn menu-btn"
              (click)="onMenu($event)"
              aria-label="More options">
              <i class="fas fa-ellipsis-v" aria-hidden="true"></i>
            </button>
          </div>
        </div>
        
        <!-- Description -->
        <p class="project-description">{{ project().description }}</p>
        
        <!-- Tags -->
        @if (project().tags.length > 0) {
          <div class="project-tags">
            @for (tag of displayTags(); track tag) {
              <span class="project-tag">{{ tag }}</span>
            }
            @if (project().tags.length > maxDisplayTags()) {
              <span class="more-tags">+{{ project().tags.length - maxDisplayTags() }}</span>
            }
          </div>
        }
        
        <!-- Creator Info -->
        <div class="creator-info">
          <div class="creator-avatar">
            <img 
              [src]="project().creator.avatar" 
              [alt]="project().creator.name + ' avatar'"
              class="avatar-image"
              loading="lazy">
          </div>
          
          <div class="creator-details">
            <span class="creator-name">{{ project().creator.name }}</span>
            <span class="project-date">{{ getFormattedDate() }}</span>
          </div>
        </div>
        
        <!-- Stats -->
        <div class="project-stats">
          <div class="stat-item rating">
            <i class="fas fa-star" aria-hidden="true"></i>
            <span class="stat-value">{{ project().rating.toFixed(1) }}</span>
          </div>
          
          <div class="stat-item views">
            <i class="fas fa-eye" aria-hidden="true"></i>
            <span class="stat-value">{{ formatViews() }}</span>
          </div>
          
          <div class="stat-item updated">
            <i class="fas fa-clock" aria-hidden="true"></i>
            <span class="stat-value">{{ getRelativeTime() }}</span>
          </div>
        </div>
        
        <!-- List View Additional Content -->
        @if (view() === 'list') {
          <div class="list-view-extras">
            <div class="project-summary">
              <div class="summary-stats">
                <span class="summary-item">
                  <strong>{{ project().rating.toFixed(1) }}</strong> rating
                </span>
                <span class="summary-item">
                  <strong>{{ formatViews() }}</strong> views
                </span>
                <span class="summary-item">
                  <strong>{{ project().tags.length }}</strong> tags
                </span>
              </div>
              
              <div class="quick-actions">
                <button 
                  class="quick-action-btn primary"
                  (click)="onCardClick()"
                  aria-label="View project">
                  <i class="fas fa-external-link-alt" aria-hidden="true"></i>
                  View Project
                </button>
                
                <button 
                  class="quick-action-btn secondary"
                  (click)="onPreview($event)"
                  aria-label="Preview project">
                  <i class="fas fa-eye" aria-hidden="true"></i>
                  Preview
                </button>
              </div>
            </div>
          </div>
        }
      </div>
    </article>
  `,
  styleUrls: ['./community-project-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CommunityProjectCardComponent {
  // Input properties
  readonly project = input.required<CommunityProject>();
  readonly view = input<'grid' | 'list'>('grid');

  // Output events
  readonly select = output<CommunityProject>();
  readonly favorite = output<CommunityProject>();
  readonly share = output<CommunityProject>();
  readonly preview = output<CommunityProject>();

  // Component state
  private readonly _isHovered = signal<boolean>(false);
  private readonly _isFavorited = signal<boolean>(false);
  private readonly _maxDisplayTags = signal<number>(3);

  // Public readonly signals
  readonly isHovered = this._isHovered.asReadonly();
  readonly isFavorited = this._isFavorited.asReadonly();
  readonly maxDisplayTags = this._maxDisplayTags.asReadonly();

  // Computed properties
  readonly displayTags = computed(() => 
    this.project().tags.slice(0, this._maxDisplayTags())
  );

  readonly getProjectType = computed(() => {
    const type = this.project().type;
    return PROJECT_TYPES[type] || null;
  });

  /**
   * Handle card click
   */
  onCardClick(): void {
    this.select.emit(this.project());
  }

  /**
   * Handle mouse enter
   */
  onMouseEnter(): void {
    this._isHovered.set(true);
  }

  /**
   * Handle mouse leave
   */
  onMouseLeave(): void {
    this._isHovered.set(false);
  }

  /**
   * Handle preview action
   */
  onPreview(event: Event): void {
    event.stopPropagation();
    this.preview.emit(this.project());
  }

  /**
   * Handle favorite action
   */
  onFavorite(event: Event): void {
    event.stopPropagation();
    this._isFavorited.set(!this._isFavorited());
    this.favorite.emit(this.project());
  }

  /**
   * Handle share action
   */
  onShare(event: Event): void {
    event.stopPropagation();
    this.share.emit(this.project());
  }

  /**
   * Handle menu action
   */
  onMenu(event: Event): void {
    event.stopPropagation();
    // Show context menu
  }

  /**
   * Get project icon based on type
   */
  getProjectIcon(): string {
    const type = this.project().type;
    return type === 'wireframe_generation' ? 'fas fa-paint-brush' : 'fas fa-code';
  }

  /**
   * Get difficulty label
   */
  getDifficultyLabel(): string {
    const difficulty = this.project().difficulty;
    const labels = {
      beginner: 'Beginner',
      intermediate: 'Intermediate',
      advanced: 'Advanced'
    };
    return labels[difficulty] || difficulty;
  }

  /**
   * Format view count
   */
  formatViews(): string {
    const views = this.project().views;
    if (views >= 1000000) {
      return (views / 1000000).toFixed(1) + 'M';
    } else if (views >= 1000) {
      return (views / 1000).toFixed(1) + 'K';
    }
    return views.toString();
  }

  /**
   * Get formatted date
   */
  getFormattedDate(): string {
    const date = new Date(this.project().createdAt);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    });
  }

  /**
   * Get relative time
   */
  getRelativeTime(): string {
    const now = new Date();
    const created = new Date(this.project().createdAt);
    const diffInMs = now.getTime() - created.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return 'Today';
    } else if (diffInDays === 1) {
      return '1 day ago';
    } else if (diffInDays < 7) {
      return `${diffInDays} days ago`;
    } else if (diffInDays < 30) {
      const weeks = Math.floor(diffInDays / 7);
      return weeks === 1 ? '1 week ago' : `${weeks} weeks ago`;
    } else if (diffInDays < 365) {
      const months = Math.floor(diffInDays / 30);
      return months === 1 ? '1 month ago' : `${months} months ago`;
    } else {
      const years = Math.floor(diffInDays / 365);
      return years === 1 ? '1 year ago' : `${years} years ago`;
    }
  }
}
