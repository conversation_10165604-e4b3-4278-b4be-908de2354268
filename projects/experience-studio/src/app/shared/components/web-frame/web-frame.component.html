<div class="web-frame-container" [class]="theme + '-theme'">
  <!-- Web Browser Frame -->
  <div class="device-frame-container">
    <div class="browser-window">
      <!-- B<PERSON><PERSON> Header with Address Bar -->
      <div class="browser-header">
        <!-- Window Controls (Mac style) -->
        <div class="window-controls">
          <div class="control-button close"></div>
          <div class="control-button minimize"></div>
          <div class="control-button maximize"></div>
        </div>

        <!-- Navigation Controls -->
        <div class="nav-controls">
          <button 
            class="nav-btn"
            (click)="onPreviousPage()"
            [disabled]="currentPageIndex === 0"
            title="Previous page">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M10 12l-4-4 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
          <button 
            class="nav-btn"
            (click)="onNextPage()"
            [disabled]="currentPageIndex === pages.length - 1"
            title="Next page">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M6 4l4 4-4 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>

        <!-- Address Bar -->
        <div class="address-bar">
          <div class="url-container">
            <span class="url-text">{{ getPageUrl() }}</span>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
          <!-- Open in New Tab button -->
          <button
            class="action-button new-tab-button"
            (click)="onOpenInNewTabClick()"
            [disabled]="!currentPage"
            title="Open in New Tab">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M14 2.5a.5.5 0 0 0-.5-.5h-6a.5.5 0 0 0 0 1h4.793L2.146 13.146a.5.5 0 0 0 .708.708L13 3.707V8.5a.5.5 0 0 0 1 0v-6z" fill="currentColor"/>
            </svg>
          </button>

          <!-- Download button -->
          <button
            class="action-button download-button"
            (click)="onDownloadClick()"
            [disabled]="!currentPage"
            title="Download as PNG">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M8.5 1a.5.5 0 0 0-1 0v5.793L5.354 4.646a.5.5 0 1 0-.708.708l3 3a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 6.793V1z" fill="currentColor"/>
              <path d="M3 9.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5z" fill="currentColor"/>
              <path d="M2.5 12a.5.5 0 0 0-.5.5v2a.5.5 0 0 0 .5.5h11a.5.5 0 0 0 .5-.5v-2a.5.5 0 0 0-.5-.5h-11z" fill="currentColor"/>
            </svg>
          </button>

          <!-- Fullscreen button -->
          <button
            class="action-button fullscreen-button"
            (click)="onFullscreenClick()"
            [disabled]="!currentPage"
            title="Open in fullscreen">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M2 2h4v2H4v2H2V2zM10 2h4v4h-2V4h-2V2zM2 10v4h4v-2H4v-2H2zM12 10v2h-2v2h4v-4h-2z" fill="currentColor"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- Browser Content Area -->
      <div class="browser-content">
        @if (currentPageContent) {
          <iframe
            #webIframe
            [safeSrcdoc]="currentPage?.content || ''"
            [isWireframeContent]="true"
            class="web-iframe"
            frameborder="0"
            sandbox="allow-same-origin allow-scripts allow-top-navigation-by-user-activation">
          </iframe>
        } @else {
          <!-- Placeholder when no content -->
          <div class="no-content-placeholder">
            <div class="placeholder-icon">🌐</div>
            <div class="placeholder-text">No content available</div>
            <div class="placeholder-description">
              Generate some content to see it displayed here in the web preview.
            </div>
          </div>
        }
      </div>
    </div>
  </div>
</div>
