import { Injectable, inject } from '@angular/core';
import { MsalService } from '@azure/msal-angular';
import { AccountInfo, AuthenticationResult, EventMessage, EventType, InteractionStatus } from '@azure/msal-browser';
import { BehaviorSubject, Observable, from, of } from 'rxjs';
import { loginRequest, protectedResources, graphConfig, AUTH_ENABLED, mockUserProfile } from '../config/auth.config';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { createLogger } from '../shared/utils';
import { cacheHelpers } from '../shared/interceptors/cache.interceptor';

export interface UserProfile {
  id: string;
  displayName: string;
  givenName: string;
  surname: string;
  userPrincipalName: string;
  mail: string;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private msalService = inject(MsalService);
  private http = inject(HttpClient);
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  private accountSubject = new BehaviorSubject<AccountInfo | null>(null);
  private userProfileSubject = new BehaviorSubject<UserProfile | null>(null);
  private isInitialized = false;
  private isLoginInProgress = false;
  private initializationPromise: Promise<void> | null = null;

  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  public account$ = this.accountSubject.asObservable();
  public userProfile$ = this.userProfileSubject.asObservable();

  constructor() {
    this.initializationPromise = this.initializeMsal();
  }

  private async initializeMsal(): Promise<void> {
    if (this.isInitialized) return;

    if (!AUTH_ENABLED) {
      this.isInitialized = true;
      this.isAuthenticatedSubject.next(true);
      this.userProfileSubject.next(mockUserProfile);
      return;
    }

    try {
      // Initialize MSAL
      await this.msalService.instance.initialize();
      this.isInitialized = true;

      // Set up event callbacks
      this.msalService.instance.addEventCallback((event: EventMessage) => {
        if (event.eventType === EventType.LOGIN_SUCCESS) {
          const payload = event.payload as AuthenticationResult;
          this.handleAuthSuccess(payload);
          this.isLoginInProgress = false;
        }
        if (event.eventType === EventType.LOGOUT_SUCCESS) {
          this.handleLogout();
        }
      });

      // Check if user is already logged in
      const accounts = this.msalService.instance.getAllAccounts();
      if (accounts.length > 0) {
        this.accountSubject.next(accounts[0]);
        this.isAuthenticatedSubject.next(true);
        await this.getUserProfile();
      } else if (!this.isLoginInProgress) {
        // Only trigger login if we're not already in a login flow
        this.login();
      }
    } catch (error) {

      this.isInitialized = false;
      throw error;
    }
  }

  private handleAuthSuccess(result: AuthenticationResult): void {
    if (!AUTH_ENABLED) return;
    if (result.account) {
      this.accountSubject.next(result.account);
      this.isAuthenticatedSubject.next(true);
      this.getUserProfile();
    }
  }

  private handleLogout(): void {
    if (!AUTH_ENABLED) return;
    this.accountSubject.next(null);
    this.isAuthenticatedSubject.next(false);
    this.userProfileSubject.next(null);
    this.isLoginInProgress = false;
  }

  public async login(): Promise<void> {
    if (!AUTH_ENABLED) {
      this.isAuthenticatedSubject.next(true);
      this.userProfileSubject.next(mockUserProfile);
      return;
    }
    await this.ensureInitialized();
    if (!this.isLoginInProgress && !this.msalService.instance.getActiveAccount()) {
      this.isLoginInProgress = true;
      this.msalService.loginRedirect({
        ...loginRequest,
        scopes: [...loginRequest.scopes, 'User.Read']
      });
    }
  }

  public async logout(): Promise<void> {
    if (!AUTH_ENABLED) {
      this.isAuthenticatedSubject.next(false);
      this.userProfileSubject.next(null);
      return;
    }
    await this.ensureInitialized();
    this.isLoginInProgress = false;
    this.msalService.logoutRedirect();
  }

  public async getAccessToken(): Promise<Observable<string>> {
    if (!AUTH_ENABLED) {
      return of('mock-access-token');
    }
    await this.ensureInitialized();
    return new Observable<string>((observer) => {
      this.msalService.acquireTokenSilent({
        scopes: protectedResources.apiExperienceStudio.scopes
      }).subscribe({
        next: (response) => {
          observer.next(response.accessToken);
          observer.complete();
        },
        error: (error) => {

          observer.error(error);
        }
      });
    });
  }

  public async getActiveAccount(): Promise<AccountInfo | null> {
    if (!AUTH_ENABLED) {
      return null;
    }
    await this.ensureInitialized();
    return this.msalService.instance.getActiveAccount();
  }

  public isLoginInProgressState(): boolean {
    if (!AUTH_ENABLED) {
      return false;
    }
    return this.isLoginInProgress;
  }

  private async ensureInitialized(): Promise<void> {
    if (!AUTH_ENABLED) return;
    if (!this.isInitialized && this.initializationPromise) {
      await this.initializationPromise;
    }
  }

  private async getUserProfile(): Promise<void> {
    if (!AUTH_ENABLED) {
      this.userProfileSubject.next(mockUserProfile);
      return;
    }
    try {
      const token = await this.msalService.acquireTokenSilent({
        scopes: ['User.Read']
      }).toPromise();

      if (token) {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token.accessToken}`);

        // User profile data should be cached for a moderate amount of time (30 minutes)
        // This reduces API calls to Microsoft Graph while still ensuring relatively fresh data
        const context = cacheHelpers.setMaxAge(30 * 60 * 1000); // 30 minutes

        // Use the HttpClient with Promise to maintain compatibility with existing code
        const profile = await this.http.get<UserProfile>(
          graphConfig.graphMeEndpoint,
          {
            headers,
            context // Add caching context
          }
        ).toPromise();

        this.userProfileSubject.next(profile || null);

      } else {

        this.userProfileSubject.next(null);
      }
    } catch (error) {

      // Try to get profile from ID token claims if available
      const account = this.msalService.instance.getActiveAccount();
      if (account?.idTokenClaims) {
        const claims = account.idTokenClaims as any;
        const profile: UserProfile = {
          id: claims.oid || '',
          displayName: claims.name || '',
          givenName: claims.given_name || '',
          surname: claims.family_name || '',
          userPrincipalName: claims.preferred_username || '',
          mail: claims.email || ''
        };
        this.userProfileSubject.next(profile);

      } else {
        this.userProfileSubject.next(null);
      }
    }
  }

  /**
   * Get user profile with cache disabled - use this when you need fresh data
   */
  public async getUserProfileFresh(): Promise<UserProfile | null> {
    if (!AUTH_ENABLED) {
      return mockUserProfile;
    }

    try {
      await this.ensureInitialized();
      const token = await this.msalService.acquireTokenSilent({
        scopes: ['User.Read']
      }).toPromise();

      if (token) {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token.accessToken}`);

        // Disable caching for this request
        const context = cacheHelpers.disableCache();

        const profile = await this.http.get<UserProfile>(
          graphConfig.graphMeEndpoint,
          {
            headers,
            context // Disable caching
          }
        ).toPromise();

        // Update the subject with the fresh data
        this.userProfileSubject.next(profile || null);

        return profile || null;
      } else {

        return null;
      }
    } catch (error) {

      return null;
    }
  }
}