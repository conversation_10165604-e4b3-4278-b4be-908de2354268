{"project_details": {"project_id": "c3c17b80-da65-4b2b-ba60-87a3da4149a5", "project_name": "CardFlow Approval System", "project_description": "A streamlined interface for MV Credit Union Admins to design, submit, and track Mastercard card designs through an efficient sequential approval workflow.", "created_at": "2025-07-22T18:07:21.918075+00:00", "last_modified": "2025-07-22T18:07:21.918075+00:00", "created_by": "5d0ddc8d-b1dd-4165-96e9-c1522914948e", "project_state": "ACTIVE"}, "project_settings": {"project_id": "c3c17b80-da65-4b2b-ba60-87a3da4149a5", "device": "web", "framework": "other", "design_system": "custom", "generation_type": "wireframe_generation"}, "repository_details": {"project_id": "c3c17b80-da65-4b2b-ba60-87a3da4149a5", "vcs_provider": "azure_devops", "clone_url": "https://<EMAIL>/ascendionava/experience-studio-preview-projects/_git/cardflow-approval-system-948e-49a5", "deployment_provider": "other", "deployed_url": null}, "conversation": [{"message_id": "55e65648-b38e-41a3-804f-e93eeb823c6f", "conversation_id": "91986103-7ffa-412d-b189-506be8461342", "message_type": "user", "content": "### 2. Card Design & Approval\n\n#### Scenario 2A: <PERSON><PERSON> submits a new card design for approval\n\n**Context:** <PERSON><PERSON> needs to create and submit a new card design for Mastercard approval.\n**Action:** Upload design, specify details, track approval status.\n**Actor:** MV Credit Union Admin\n**Goal:** Efficiently manage the card design approval process.\n\n#### Workflow Variation 1: Sequential Approval Workflow\n\n**User Goal:** Ensure all design elements comply and are approved in order.\n**Business Goal:** Maintain brand compliance and reduce approval cycle time.\n\n##### Screens\n\n**1.0 Card Design Dashboard**\n- **Goal:** Overview of all card design projects.\n- **Description:** List of drafts, in-progress, and approved designs.\n- **Design Problem:** HMW track multiple designs at various stages?\n- **Design Opportunity:** What if status filters and notifications are provided?\n\n**2.0 New Design Submission**\n- **Goal:** Submit new card design.\n- **Description:** Upload artwork, specify logo placement, colors, select card type.\n- **Design Problem:** HMW ensure all required specs are included?\n- **Design Opportunity:** What if a template or checklist is provided?\n\n**3.0 Internal Review**\n- **Goal:** Internal compliance check before Mastercard submission.\n- **Description:** Checklist, comments, approve/reject.\n- **Design Problem:** HMW streamline internal feedback?\n- **Design Opportunity:** What if collaborative commenting is enabled?\n\n**4.0 Submit to Mastercard**\n- **Goal:** Forward design for Mastercard approval.\n- **Description:** Status update, expected timeline, upload confirmation.\n- **Design Problem:** HMW provide transparency on approval timelines?\n- **Design Opportunity:** What if real-time status updates are integrated?\n\n**5.0 Track Approval**\n- **Goal:** Monitor approval progress.\n- **Description:** Timeline view, notifications on status change.\n- **Design Problem:** HMW reduce uncertainty during waiting periods?\n- **Design Opportunity:** What if the system predicts approval timelines?\n\n##### Sequence: 1.0 Dashboard -> 2.0 Submission -> 3.0 Internal Review -> 4.0 Submit -> 5.0 Track", "ui_metadata": null, "created_at": "2025-07-22T18:09:45.108707+00:00"}, {"message_id": "36f506af-19be-4cd1-95b8-5fc13293d3a2", "conversation_id": "91986103-7ffa-412d-b189-506be8461342", "message_type": "assistant", "content": "Generated initial wireframe designs based on your requirements", "ui_metadata": null, "created_at": "2025-07-22T18:09:45.317269+00:00"}, {"message_id": "f17f49b7-ef52-4fe5-969b-76baa0cbd0da", "conversation_id": "91986103-7ffa-412d-b189-506be8461342", "message_type": "capsule", "content": "5c6c42892d2591d57ff06621ceb3a6af580842d2", "ui_metadata": "[{\"content\": \"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>Card Design Dashboard | MV Credit Union</title>\\n    <script src=\\\"https://cdn.tailwindcss.com\\\"></script>\\n    <script src=\\\"https://unpkg.com/lucide@latest\\\"></script>\\n    <script src=\\\"https://cdn.jsdelivr.net/npm/chart.js\\\"></script>\\n    <style>\\n        :root {\\n            --primary-emotion: #A862A8;\\n            --secondary-emotion: #1C2B5E;\\n            --neutral-light: #F2F1F2;\\n            --neutral-medium: #C1C1C1;\\n            --accent-dark: #32373C;\\n            --success: #10B981;\\n            --warning: #F59E0B;\\n            --error: #EF4444;\\n\\n            --heading-font: 'Mint Grotesk Display ExtraBold', sans-serif;\\n            --body-font: 'Mint Grotesk Medium', sans-serif;\\n            --heading-weight: 800;\\n            --body-weight: 500;\\n\\n            --desktop-comfortable: 2rem;\\n            --desktop-spacious: 3rem;\\n            --desktop-dramatic: 6rem;\\n            --desktop-easing: cubic-bezier(0.4, 0.0, 0.2, 1);\\n            --desktop-smooth: 200ms;\\n        }\\n\\n        @font-face {\\n            font-family: 'Mint Grotesk Medium';\\n            src: url('https://www.shiftkey.com/fonts/MintGrotesk-Medium.woff2') format('woff2');\\n            font-weight: 500;\\n            font-style: normal;\\n            font-display: swap;\\n        }\\n\\n        @font-face {\\n            font-family: 'Mint Grotesk Display ExtraBold';\\n            src: url('https://www.shiftkey.com/fonts/MintGroteskDisplay-ExtraBold.woff2') format('woff2');\\n            font-weight: 800;\\n            font-style: normal;\\n            font-display: swap;\\n        }\\n        \\n        body { font-family: var(--body-font); background-color: var(--neutral-light); color: var(--accent-dark); }\\n        h1, h2, h3, h4, h5, h6, .heading { font-family: var(--heading-font); font-weight: var(--heading-weight); color: var(--secondary-emotion); }\\n        .btn, button, .cta { font-family: var(--heading-font); font-weight: var(--heading-weight); }\\n        \\n        .btn-primary { background-color: var(--primary-emotion); color: white; transition: background-color var(--desktop-smooth) var(--desktop-easing); }\\n        .btn-primary:hover { background-color: #8E4F8E; }\\n        \\n        .desktop-card { background: white; border-radius: 16px; padding: var(--desktop-comfortable); box-shadow: 0 4px 20px rgba(0,0,0,0.05); border: 1px solid #E5E7EB; }\\n        \\n        .table-row-hover:hover { background-color: var(--neutral-light); cursor: pointer; }\\n        .story-entrance { opacity: 0; transform: translateY(20px); transition: all 500ms var(--desktop-easing); }\\n        .story-entrance.visible { opacity: 1; transform: translateY(0); }\\n    </style>\\n</head>\\n<body class=\\\"antialiased\\\">\\n    <div class=\\\"flex min-h-screen\\\">\\n        <!-- Sidebar Navigation -->\\n        <aside class=\\\"w-64 bg-white border-r border-gray-200 flex flex-col story-entrance\\\">\\n            <div class=\\\"p-6 flex items-center space-x-3 border-b border-gray-200\\\">\\n                <img src=\\\"https://cdn.brandfetch.io/idI5LYj5RP/theme/dark/logo.svg?c=1dxbfHSJFAPEGdCLU4o5B\\\" alt=\\\"MV Credit Union Logo\\\" class=\\\"h-8\\\">\\n            </div>\\n            <nav class=\\\"flex-1 p-6 space-y-2\\\">\\n                <a href=\\\"Dashboard_Page.html\\\" class=\\\"flex items-center px-4 py-3 text-white rounded-lg\\\" style=\\\"background-color: var(--primary-emotion);\\\">\\n                    <i data-lucide=\\\"layout-dashboard\\\" class=\\\"w-5 h-5 mr-3\\\"></i>\\n                    <span class=\\\"body-text\\\">Dashboard</span>\\n                </a>\\n                <a href=\\\"#\\\" class=\\\"flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg\\\">\\n                    <i data-lucide=\\\"file-check-2\\\" class=\\\"w-5 h-5 mr-3\\\"></i>\\n                    <span class=\\\"body-text\\\">Approved Designs</span>\\n                </a>\\n                 <a href=\\\"#\\\" class=\\\"flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg\\\">\\n                    <i data-lucide=\\\"archive\\\" class=\\\"w-5 h-5 mr-3\\\"></i>\\n                    <span class=\\\"body-text\\\">Archive</span>\\n                </a>\\n            </nav>\\n            <div class=\\\"p-6 border-t border-gray-200\\\">\\n                 <div class=\\\"desktop-card bg-blue-50 border-blue-200\\\" style=\\\"background-color: #EFF2F9;\\\">\\n                    <h4 class=\\\"heading text-base\\\" style=\\\"color: var(--secondary-emotion);\\\">Compliance Center</h4>\\n                    <p class=\\\"text-sm text-gray-600 mt-1\\\">Ensure all designs meet Mastercard's latest guidelines.</p>\\n                    <a href=\\\"#\\\" class=\\\"text-sm mt-3 inline-block font-semibold\\\" style=\\\"color: var(--secondary-emotion);\\\">View Guidelines &rarr;</a>\\n                </div>\\n            </div>\\n        </aside>\\n\\n        <!-- Main Content -->\\n        <div class=\\\"flex-1\\\">\\n            <header class=\\\"bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10\\\">\\n                <div class=\\\"px-8 h-20 flex items-center justify-between\\\">\\n                    <h1 class=\\\"text-3xl heading\\\">Card Design Dashboard</h1>\\n                    <div class=\\\"flex items-center space-x-6\\\">\\n                        <button class=\\\"text-gray-500 hover:text-gray-800\\\"><i data-lucide=\\\"bell\\\" class=\\\"w-6 h-6\\\"></i></button>\\n                        <div class=\\\"flex items-center space-x-3\\\">\\n                            <img src=\\\"https://pixabay.com/get/g563ef6fdfbd736e126782fa46d4789c9dccb52762dabeb9172cf4e955e83712221b5a5c0f899adc91bb4262f82f00e7f509a3d253454a8776250f10bc9396835_1280.png\\\" alt=\\\"Admin user avatar\\\" class=\\\"w-10 h-10 rounded-full object-cover\\\">\\n                            <div>\\n                                <p class=\\\"font-semibold text-gray-800 body-text\\\">Maria Garcia</p>\\n                                <p class=\\\"text-sm text-gray-500 body-text\\\">Admin</p>\\n                            </div>\\n                        </div>\\n                    </div>\\n                </div>\\n            </header>\\n\\n            <main class=\\\"p-8 lg:p-12\\\">\\n                <!-- Overview Stats -->\\n                <div class=\\\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12\\\">\\n                    <div class=\\\"desktop-card story-entrance\\\">\\n                        <div class=\\\"flex justify-between items-start\\\">\\n                             <h3 class=\\\"text-lg heading\\\" style=\\\"color: var(--accent-dark);\\\">Total Projects</h3>\\n                             <div class=\\\"p-2 bg-purple-100 rounded-lg\\\"><i data-lucide=\\\"briefcase\\\" class=\\\"w-6 h-6\\\" style=\\\"color: var(--primary-emotion);\\\"></i></div>\\n                        </div>\\n                        <p class=\\\"text-5xl mt-2 heading\\\">14</p>\\n                        <p class=\\\"text-sm text-gray-500 mt-1\\\">2 new this week</p>\\n                    </div>\\n                    <div class=\\\"desktop-card story-entrance\\\" style=\\\"animation-delay: 100ms;\\\">\\n                        <div class=\\\"flex justify-between items-start\\\">\\n                             <h3 class=\\\"text-lg heading\\\" style=\\\"color: var(--accent-dark);\\\">In Review</h3>\\n                             <div class=\\\"p-2 bg-yellow-100 rounded-lg\\\"><i data-lucide=\\\"clock\\\" class=\\\"w-6 h-6 text-yellow-500\\\"></i></div>\\n                        </div>\\n                        <p class=\\\"text-5xl mt-2 heading\\\">3</p>\\n                         <p class=\\\"text-sm text-gray-500 mt-1\\\">1 awaiting MC feedback</p>\\n                    </div>\\n                    <div class=\\\"desktop-card story-entrance\\\" style=\\\"animation-delay: 200ms;\\\">\\n                         <div class=\\\"flex justify-between items-start\\\">\\n                             <h3 class=\\\"text-lg heading\\\" style=\\\"color: var(--accent-dark);\\\">Approved</h3>\\n                             <div class=\\\"p-2 bg-green-100 rounded-lg\\\"><i data-lucide=\\\"check-circle\\\" class=\\\"w-6 h-6 text-green-500\\\"></i></div>\\n                        </div>\\n                        <p class=\\\"text-5xl mt-2 heading\\\">9</p>\\n                         <p class=\\\"text-sm text-gray-500 mt-1\\\">1 approved yesterday</p>\\n                    </div>\\n                    <div class=\\\"desktop-card story-entrance\\\" style=\\\"animation-delay: 300ms;\\\">\\n                         <div class=\\\"flex justify-between items-start\\\">\\n                             <h3 class=\\\"text-lg heading\\\" style=\\\"color: var(--accent-dark);\\\">Revisions Needed</h3>\\n                             <div class=\\\"p-2 bg-red-100 rounded-lg\\\"><i data-lucide=\\\"alert-circle\\\" class=\\\"w-6 h-6 text-red-500\\\"></i></div>\\n                        </div>\\n                        <p class=\\\"text-5xl mt-2 heading\\\">2</p>\\n                         <p class=\\\"text-sm text-gray-500 mt-1\\\">Action required</p>\\n                    </div>\\n                </div>\\n\\n                <!-- Main Content Area -->\\n                <div class=\\\"grid grid-cols-1 lg:grid-cols-3 gap-8\\\">\\n                    <!-- Projects Table -->\\n                    <div class=\\\"lg:col-span-2 desktop-card story-entrance\\\" style=\\\"animation-delay: 400ms;\\\">\\n                        <div class=\\\"flex justify-between items-center mb-6\\\">\\n                            <h2 class=\\\"text-2xl heading\\\">Active Design Projects</h2>\\n                            <a href=\\\"Design_Page.html\\\" class=\\\"btn btn-primary px-5 py-3 rounded-lg flex items-center space-x-2 cta\\\">\\n                                <i data-lucide=\\\"plus\\\" class=\\\"w-5 h-5\\\"></i>\\n                                <span>Submit New Design</span>\\n                            </a>\\n                        </div>\\n                        <div class=\\\"overflow-x-auto\\\">\\n                            <table class=\\\"w-full text-left\\\">\\n                                <thead class=\\\"border-b-2 border-gray-200\\\">\\n                                    <tr>\\n                                        <th class=\\\"p-4 text-sm font-semibold text-gray-500 body-text\\\">Project Name</th>\\n                                        <th class=\\\"p-4 text-sm font-semibold text-gray-500 body-text\\\">Card Type</th>\\n                                        <th class=\\\"p-4 text-sm font-semibold text-gray-500 body-text\\\">Last Update</th>\\n                                        <th class=\\\"p-4 text-sm font-semibold text-gray-500 body-text\\\">Status</th>\\n                                    </tr>\\n                                </thead>\\n                                <tbody>\\n                                    <tr class=\\\"border-b border-gray-100 table-row-hover\\\" onclick=\\\"window.location.href='Track_Page.html'\\\">\\n                                        <td class=\\\"p-4 font-semibold text-gray-800\\\">MVU Platinum Rewards</td>\\n                                        <td class=\\\"p-4 text-gray-600\\\">Credit</td>\\n                                        <td class=\\\"p-4 text-gray-600\\\">2 hours ago</td>\\n                                        <td class=\\\"p-4\\\"><span class=\\\"px-3 py-1 text-sm rounded-full bg-yellow-100 text-yellow-800 font-medium\\\">Internal Review</span></td>\\n                                    </tr>\\n                                    <tr class=\\\"border-b border-gray-100 table-row-hover\\\" onclick=\\\"window.location.href='Track_Page.html'\\\">\\n                                        <td class=\\\"p-4 font-semibold text-gray-800\\\">Community First Debit</td>\\n                                        <td class=\\\"p-4 text-gray-600\\\">Debit</td>\\n                                        <td class=\\\"p-4 text-gray-600\\\">1 day ago</td>\\n                                        <td class=\\\"p-4\\\"><span class=\\\"px-3 py-1 text-sm rounded-full bg-blue-100 text-blue-800 font-medium\\\">Awaiting MC</span></td>\\n                                    </tr>\\n                                    <tr class=\\\"border-b border-gray-100 table-row-hover\\\" onclick=\\\"window.location.href='Track_Page.html'\\\">\\n                                        <td class=\\\"p-4 font-semibold text-gray-800\\\">Student Advantage Card</td>\\n                                        <td class=\\\"p-4 text-gray-600\\\">Credit</td>\\n                                        <td class=\\\"p-4 text-gray-600\\\">3 days ago</td>\\n                                        <td class=\\\"p-4\\\"><span class=\\\"px-3 py-1 text-sm rounded-full bg-red-100 text-red-800 font-medium\\\">Revisions Required</span></td>\\n                                    </tr>\\n                                    <tr class=\\\"border-b border-gray-100 table-row-hover\\\" onclick=\\\"window.location.href='Track_Page.html'\\\">\\n                                        <td class=\\\"p-4 font-semibold text-gray-800\\\">MVU Business Elite</td>\\n                                        <td class=\\\"p-4 text-gray-600\\\">Credit</td>\\n                                        <td class=\\\"p-4 text-gray-600\\\">1 week ago</td>\\n                                        <td class=\\\"p-4\\\"><span class=\\\"px-3 py-1 text-sm rounded-full bg-green-100 text-green-800 font-medium\\\">Approved</span></td>\\n                                    </tr>\\n                                </tbody>\\n                            </table>\\n                        </div>\\n                    </div>\\n\\n                    <!-- Status Chart -->\\n                    <div class=\\\"desktop-card story-entrance\\\" style=\\\"animation-delay: 500ms;\\\">\\n                        <h2 class=\\\"text-2xl heading mb-6\\\">Projects Overview</h2>\\n                        <div class=\\\"relative h-64\\\"><canvas id=\\\"statusChart\\\"></canvas></div>\\n                        <div class=\\\"mt-6 space-y-3 text-sm body-text\\\">\\n                            <div class=\\\"flex justify-between\\\"><span class=\\\"flex items-center\\\"><span class=\\\"w-3 h-3 rounded-full bg-green-500 mr-2\\\"></span>Approved</span> <span class=\\\"font-semibold\\\">9</span></div>\\n                            <div class=\\\"flex justify-between\\\"><span class=\\\"flex items-center\\\"><span class=\\\"w-3 h-3 rounded-full bg-yellow-500 mr-2\\\"></span>Internal Review</span> <span class=\\\"font-semibold\\\">1</span></div>\\n                            <div class=\\\"flex justify-between\\\"><span class=\\\"flex items-center\\\"><span class=\\\"w-3 h-3 rounded-full bg-blue-500 mr-2\\\"></span>Awaiting MC</span> <span class=\\\"font-semibold\\\">2</span></div>\\n                            <div class=\\\"flex justify-between\\\"><span class=\\\"flex items-center\\\"><span class=\\\"w-3 h-3 rounded-full bg-red-500 mr-2\\\"></span>Revisions Required</span> <span class=\\\"font-semibold\\\">2</span></div>\\n                        </div>\\n                    </div>\\n                </div>\\n            </main>\\n        </div>\\n    </div>\\n\\n    <script>\\n        document.addEventListener('DOMContentLoaded', () => {\\n            lucide.createIcons();\\n\\n            const observer = new IntersectionObserver((entries) => {\\n                entries.forEach(entry => {\\n                    if (entry.isIntersecting) {\\n                        entry.target.classList.add('visible');\\n                    }\\n                });\\n            }, { threshold: 0.1 });\\n\\n            document.querySelectorAll('.story-entrance').forEach(el => observer.observe(el));\\n\\n            const ctx = document.getElementById('statusChart').getContext('2d');\\n            new Chart(ctx, {\\n                type: 'doughnut',\\n                data: {\\n                    labels: ['Approved', 'Internal Review', 'Awaiting MC', 'Revisions Required'],\\n                    datasets: [{\\n                        data: [9, 1, 2, 2],\\n                        backgroundColor: ['#10B981', '#F59E0B', '#3B82F6', '#EF4444'],\\n                        borderWidth: 0,\\n                        hoverOffset: 8\\n                    }]\\n                },\\n                options: {\\n                    responsive: true,\\n                    maintainAspectRatio: false,\\n                    cutout: '70%',\\n                    plugins: {\\n                        legend: { display: false },\\n                        tooltip: {\\n                            backgroundColor: '#32373C',\\n                            titleFont: { family: 'Mint Grotesk Display ExtraBold' },\\n                            bodyFont: { family: 'Mint Grotesk Medium' },\\n                            padding: 12,\\n                            cornerRadius: 8,\\n                            boxPadding: 4\\n                        }\\n                    },\\n                    animation: {\\n                        animateScale: true,\\n                        animateRotate: true\\n                    }\\n                }\\n            });\\n        });\\n    </script>\\n</body>\\n</html>\", \"fileName\": \"Dashboard_Page.html\"}, {\"content\": \"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>New Card Submission | MV Credit Union</title>\\n    <script src=\\\"https://cdn.tailwindcss.com\\\"></script>\\n    <script src=\\\"https://unpkg.com/lucide@latest\\\"></script>\\n    <style>\\n        :root {\\n            --primary-emotion: #A862A8;\\n            --secondary-emotion: #1C2B5E;\\n            --neutral-light: #F2F1F2;\\n            --accent-dark: #32373C;\\n            --success: #10B981;\\n            --heading-font: 'Mint Grotesk Display ExtraBold', sans-serif;\\n            --body-font: 'Mint Grotesk Medium', sans-serif;\\n            --heading-weight: 800;\\n            --body-weight: 500;\\n            --desktop-comfortable: 2rem;\\n            --desktop-easing: cubic-bezier(0.4, 0.0, 0.2, 1);\\n            --desktop-smooth: 300ms;\\n        }\\n\\n        @font-face {\\n            font-family: 'Mint Grotesk Medium';\\n            src: url('https://www.shiftkey.com/fonts/MintGrotesk-Medium.woff2') format('woff2');\\n            font-weight: 500;\\n            font-style: normal;\\n            font-display: swap;\\n        }\\n\\n        @font-face {\\n            font-family: 'Mint Grotesk Display ExtraBold';\\n            src: url('https://www.shiftkey.com/fonts/MintGroteskDisplay-ExtraBold.woff2') format('woff2');\\n            font-weight: 800;\\n            font-style: normal;\\n            font-display: swap;\\n        }\\n        \\n        body { font-family: var(--body-font); background-color: var(--neutral-light); color: var(--accent-dark); }\\n        h1, h2, h3, h4, h5, h6, .heading { font-family: var(--heading-font); font-weight: var(--heading-weight); color: var(--secondary-emotion); }\\n        .btn, button, .cta { font-family: var(--heading-font); font-weight: var(--heading-weight); }\\n        \\n        .btn-primary { background-color: var(--primary-emotion); color: white; transition: all var(--desktop-smooth) var(--desktop-easing); }\\n        .btn-primary:hover { background-color: #8E4F8E; transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.1); }\\n        .btn-secondary { background-color: #E5E7EB; color: var(--accent-dark); transition: all var(--desktop-smooth) var(--desktop-easing); }\\n        .btn-secondary:hover { background-color: #D1D5DB; }\\n        \\n        .desktop-card { background: white; border-radius: 16px; padding: var(--desktop-comfortable); box-shadow: 0 4px 20px rgba(0,0,0,0.05); border: 1px solid #E5E7EB; }\\n        .form-input { border: 1px solid #D1D5DB; border-radius: 8px; padding: 12px 16px; width: 100%; transition: all var(--desktop-smooth) var(--desktop-easing); }\\n        .form-input:focus { border-color: var(--primary-emotion); box-shadow: 0 0 0 3px rgba(168, 98, 168, 0.2); outline: none; }\\n        .wizard-step { display: none; opacity: 0; transition: opacity var(--desktop-smooth) var(--desktop-easing); }\\n        .wizard-step.active { display: block; opacity: 1; }\\n    </style>\\n</head>\\n<body class=\\\"antialiased\\\">\\n    <div class=\\\"flex min-h-screen\\\">\\n        <!-- Sidebar Navigation -->\\n        <aside class=\\\"w-64 bg-white border-r border-gray-200 flex flex-col\\\">\\n             <div class=\\\"p-6 flex items-center space-x-3 border-b border-gray-200\\\">\\n                <a href=\\\"Dashboard_Page.html\\\">\\n                    <img src=\\\"https://cdn.brandfetch.io/idI5LYj5RP/theme/dark/logo.svg?c=1dxbfHSJFAPEGdCLU4o5B\\\" alt=\\\"MV Credit Union Logo\\\" class=\\\"h-8\\\">\\n                </a>\\n            </div>\\n            <nav class=\\\"flex-1 p-6 space-y-2\\\">\\n                <a href=\\\"Dashboard_Page.html\\\" class=\\\"flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg\\\">\\n                    <i data-lucide=\\\"layout-dashboard\\\" class=\\\"w-5 h-5 mr-3\\\"></i>\\n                    <span>Dashboard</span>\\n                </a>\\n            </nav>\\n        </aside>\\n\\n        <!-- Main Content -->\\n        <div class=\\\"flex-1 bg-white\\\">\\n            <header class=\\\"bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10\\\">\\n                <div class=\\\"px-8 h-20 flex items-center justify-between\\\">\\n                    <h1 class=\\\"text-3xl heading\\\">New Card Design Submission</h1>\\n                    <div class=\\\"flex items-center space-x-6\\\">\\n                        <p class=\\\"text-sm text-gray-500\\\">Step <span id=\\\"current-step\\\">1</span> of 3</p>\\n                    </div>\\n                </div>\\n            </header>\\n\\n            <main class=\\\"p-8 lg:p-12\\\">\\n                <div class=\\\"max-w-4xl mx-auto\\\">\\n                    <!-- Progress Bar -->\\n                    <div class=\\\"mb-12\\\">\\n                        <div class=\\\"relative\\\">\\n                            <div class=\\\"absolute top-1/2 -translate-y-1/2 w-full h-1 bg-gray-200 rounded-full\\\"></div>\\n                            <div id=\\\"progress-bar\\\" class=\\\"absolute top-1/2 -translate-y-1/2 w-0 h-1 rounded-full transition-all duration-500\\\" style=\\\"background-color: var(--primary-emotion);\\\"></div>\\n                            <div class=\\\"flex justify-between items-center relative\\\">\\n                                <div id=\\\"step-indicator-1\\\" class=\\\"step-indicator active\\\">\\n                                    <div class=\\\"w-8 h-8 flex items-center justify-center rounded-full text-white\\\" style=\\\"background-color: var(--primary-emotion);\\\"><i data-lucide=\\\"upload-cloud\\\" class=\\\"w-5 h-5\\\"></i></div>\\n                                    <p class=\\\"text-sm mt-2 text-center\\\">Design Details</p>\\n                                </div>\\n                                <div id=\\\"step-indicator-2\\\" class=\\\"step-indicator\\\">\\n                                    <div class=\\\"w-8 h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-500\\\"><i data-lucide=\\\"clipboard-check\\\" class=\\\"w-5 h-5\\\"></i></div>\\n                                    <p class=\\\"text-sm mt-2 text-center\\\">Internal Review</p>\\n                                </div>\\n                                <div id=\\\"step-indicator-3\\\" class=\\\"step-indicator\\\">\\n                                    <div class=\\\"w-8 h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-500\\\"><i data-lucide=\\\"send\\\" class=\\\"w-5 h-5\\\"></i></div>\\n                                    <p class=\\\"text-sm mt-2 text-center\\\">Submit</p>\\n                                </div>\\n                            </div>\\n                        </div>\\n                    </div>\\n                    \\n                    <!-- Wizard Content -->\\n                    <div id=\\\"wizard-form\\\" class=\\\"desktop-card p-12\\\">\\n                        <!-- Step 1: Design Submission -->\\n                        <div id=\\\"step-1\\\" class=\\\"wizard-step active\\\">\\n                            <h2 class=\\\"text-2xl heading mb-2\\\">1. Design Details & Artwork</h2>\\n                            <p class=\\\"text-gray-600 mb-8\\\">Provide project details and upload the card artwork for review.</p>\\n                            <div class=\\\"space-y-6\\\">\\n                                <div>\\n                                    <label for=\\\"projectName\\\" class=\\\"block text-sm font-medium text-gray-700 mb-2\\\">Project Name</label>\\n                                    <input type=\\\"text\\\" id=\\\"projectName\\\" class=\\\"form-input\\\" placeholder=\\\"e.g., MVU Platinum Rewards 2024\\\">\\n                                </div>\\n                                <div>\\n                                    <label for=\\\"cardType\\\" class=\\\"block text-sm font-medium text-gray-700 mb-2\\\">Card Type</label>\\n                                    <select id=\\\"cardType\\\" class=\\\"form-input\\\">\\n                                        <option>Mastercard Credit</option>\\n                                        <option>Mastercard Debit</option>\\n                                        <option>Mastercard Business</option>\\n                                    </select>\\n                                </div>\\n                                <div>\\n                                    <label class=\\\"block text-sm font-medium text-gray-700 mb-2\\\">Card Artwork</label>\\n                                    <div class=\\\"mt-1 flex justify-center px-6 pt-10 pb-12 border-2 border-gray-300 border-dashed rounded-md\\\">\\n                                        <div class=\\\"space-y-1 text-center\\\">\\n                                            <i data-lucide=\\\"image\\\" class=\\\"mx-auto h-12 w-12 text-gray-400\\\"></i>\\n                                            <div class=\\\"flex text-sm text-gray-600\\\">\\n                                                <label for=\\\"file-upload\\\" class=\\\"relative cursor-pointer bg-white rounded-md font-medium text-purple-600 hover:text-purple-500 focus-within:outline-none\\\">\\n                                                    <span>Upload a file</span>\\n                                                    <input id=\\\"file-upload\\\" name=\\\"file-upload\\\" type=\\\"file\\\" class=\\\"sr-only\\\">\\n                                                </label>\\n                                                <p class=\\\"pl-1\\\">or drag and drop</p>\\n                                            </div>\\n                                            <p class=\\\"text-xs text-gray-500\\\">AI, PDF, PSD up to 25MB</p>\\n                                        </div>\\n                                    </div>\\n                                    <p class=\\\"text-xs text-gray-500 mt-2\\\">Uploading this file confirms you have the rights to use this artwork. <a href=\\\"#\\\" class=\\\"underline\\\">Learn more</a>.</p>\\n                                </div>\\n                            </div>\\n                        </div>\\n\\n                        <!-- Step 2: Internal Review -->\\n                        <div id=\\\"step-2\\\" class=\\\"wizard-step\\\">\\n                            <h2 class=\\\"text-2xl heading mb-2\\\">2. Internal Compliance Checklist</h2>\\n                            <p class=\\\"text-gray-600 mb-8\\\">Confirm the design meets MV Credit Union and Mastercard brand standards before submission.</p>\\n                            <div class=\\\"space-y-5\\\">\\n                                <label class=\\\"flex items-start p-4 border rounded-lg hover:bg-gray-50 cursor-pointer\\\">\\n                                    <input type=\\\"checkbox\\\" class=\\\"h-5 w-5 rounded border-gray-300 text-purple-600 focus:ring-purple-500 mt-1\\\">\\n                                    <div class=\\\"ml-4\\\">\\n                                        <p class=\\\"font-medium text-gray-800\\\">Logo Placement</p>\\n                                        <p class=\\\"text-sm text-gray-500\\\">MVU and Mastercard logos are in approved positions per Brand Guide 3.4.</p>\\n                                    </div>\\n                                </label>\\n                                <label class=\\\"flex items-start p-4 border rounded-lg hover:bg-gray-50 cursor-pointer\\\">\\n                                    <input type=\\\"checkbox\\\" class=\\\"h-5 w-5 rounded border-gray-300 text-purple-600 focus:ring-purple-500 mt-1\\\">\\n                                    <div class=\\\"ml-4\\\">\\n                                        <p class=\\\"font-medium text-gray-800\\\">Color Palette</p>\\n                                        <p class=\\\"text-sm text-gray-500\\\">Colors used are from the approved corporate palette.</p>\\n                                    </div>\\n                                </label>\\n                                <label class=\\\"flex items-start p-4 border rounded-lg hover:bg-gray-50 cursor-pointer\\\">\\n                                    <input type=\\\"checkbox\\\" class=\\\"h-5 w-5 rounded border-gray-300 text-purple-600 focus:ring-purple-500 mt-1\\\">\\n                                    <div class=\\\"ml-4\\\">\\n                                        <p class=\\\"font-medium text-gray-800\\\">EMV Chip & Magstripe</p>\\n                                        <p class=\\\"text-sm text-gray-500\\\">Design provides clear space for the EMV chip and magnetic stripe.</p>\\n                                    </div>\\n                                </label>\\n                                <label class=\\\"flex items-start p-4 border rounded-lg hover:bg-gray-50 cursor-pointer\\\">\\n                                    <input type=\\\"checkbox\\\" class=\\\"h-5 w-5 rounded border-gray-300 text-purple-600 focus:ring-purple-500 mt-1\\\">\\n                                    <div class=\\\"ml-4\\\">\\n                                        <p class=\\\"font-medium text-gray-800\\\">FCA Compliance Text</p>\\n                                        <p class=\\\"text-sm text-gray-500\\\">All required regulatory text is present, legible, and accurate.</p>\\n                                    </div>\\n                                </label>\\n                            </div>\\n                        </div>\\n\\n                        <!-- Step 3: Submit -->\\n                        <div id=\\\"step-3\\\" class=\\\"wizard-step text-center\\\">\\n                            <div class=\\\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto\\\">\\n                                <i data-lucide=\\\"check-circle-2\\\" class=\\\"w-10 h-10 text-green-600\\\"></i>\\n                            </div>\\n                            <h2 class=\\\"text-3xl heading mt-6 mb-2\\\">Ready to Submit</h2>\\n                            <p class=\\\"text-gray-600 max-w-md mx-auto mb-8\\\">You have completed all the steps. The design will be sent to Mastercard for final approval. The typical response time is 5-7 business days.</p>\\n                             <label class=\\\"flex items-center justify-center text-sm text-gray-600 mb-8\\\">\\n                                <input type=\\\"checkbox\\\" id=\\\"final-consent\\\" class=\\\"h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500\\\">\\n                                <span class=\\\"ml-2\\\">I confirm that this submission is accurate and ready for final review.</span>\\n                            </label>\\n                            <p class=\\\"text-xs text-gray-500\\\">By submitting, you agree to our <a href=\\\"#\\\" class=\\\"underline\\\">Terms of Service</a> and acknowledge our <a href=\\\"#\\\" class=\\\"underline\\\">Privacy Policy</a> regarding data handling for this submission.</p>\\n                        </div>\\n                        \\n                        <!-- Navigation Buttons -->\\n                        <div class=\\\"mt-12 pt-8 border-t flex justify-between items-center\\\">\\n                            <button id=\\\"prev-btn\\\" class=\\\"btn btn-secondary px-6 py-3 rounded-lg cta\\\" style=\\\"display: none;\\\">Back</button>\\n                            <button id=\\\"next-btn\\\" class=\\\"btn btn-primary px-6 py-3 rounded-lg ml-auto cta\\\">Next Step</button>\\n                            <button id=\\\"submit-btn\\\" class=\\\"btn btn-primary px-6 py-3 rounded-lg ml-auto cta\\\" style=\\\"display: none;\\\" onclick=\\\"window.location.href='Track_Page.html'\\\">Submit to Mastercard</button>\\n                        </div>\\n                    </div>\\n                </div>\\n            </main>\\n        </div>\\n    </div>\\n\\n    <script>\\n        document.addEventListener('DOMContentLoaded', () => {\\n            lucide.createIcons();\\n            \\n            let currentStep = 1;\\n            const totalSteps = 3;\\n            const nextBtn = document.getElementById('next-btn');\\n            const prevBtn = document.getElementById('prev-btn');\\n            const submitBtn = document.getElementById('submit-btn');\\n            const progressBar = document.getElementById('progress-bar');\\n            const currentStepSpan = document.getElementById('current-step');\\n\\n            function updateWizard() {\\n                // Update content\\n                document.querySelectorAll('.wizard-step').forEach(step => step.classList.remove('active'));\\n                document.getElementById(`step-${currentStep}`).classList.add('active');\\n\\n                // Update progress bar and indicators\\n                for (let i = 1; i <= totalSteps; i++) {\\n                    const indicator = document.getElementById(`step-indicator-${i}`).querySelector('div');\\n                    const text = document.getElementById(`step-indicator-${i}`).querySelector('p');\\n                    if (i < currentStep) {\\n                        indicator.style.backgroundColor = 'var(--success)';\\n                        indicator.innerHTML = `<i data-lucide=\\\"check\\\" class=\\\"w-5 h-5\\\"></i>`;\\n                        text.style.color = 'var(--accent-dark)';\\n                    } else if (i === currentStep) {\\n                        indicator.style.backgroundColor = 'var(--primary-emotion)';\\n                        text.style.color = 'var(--primary-emotion)';\\n                    } else {\\n                        indicator.style.backgroundColor = '#E5E7EB';\\n                        text.style.color = '#6B7280';\\n                    }\\n                }\\n                lucide.createIcons();\\n\\n\\n                const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;\\n                progressBar.style.width = `${progress}%`;\\n                currentStepSpan.textContent = currentStep;\\n\\n                // Update buttons\\n                prevBtn.style.display = currentStep > 1 ? 'block' : 'none';\\n                nextBtn.style.display = currentStep < totalSteps ? 'block' : 'none';\\n                submitBtn.style.display = currentStep === totalSteps ? 'block' : 'none';\\n            }\\n\\n            nextBtn.addEventListener('click', () => {\\n                if (currentStep < totalSteps) {\\n                    currentStep++;\\n                    updateWizard();\\n                }\\n            });\\n\\n            prevBtn.addEventListener('click', () => {\\n                if (currentStep > 1) {\\n                    currentStep--;\\n                    updateWizard();\\n                }\\n            });\\n\\n            updateWizard();\\n        });\\n    </script>\\n</body>\\n</html>\", \"fileName\": \"Design_Page.html\"}, {\"content\": \"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>Track Approval | MV Credit Union</title>\\n    <script src=\\\"https://cdn.tailwindcss.com\\\"></script>\\n    <script src=\\\"https://unpkg.com/lucide@latest\\\"></script>\\n    <style>\\n        :root {\\n            --primary-emotion: #A862A8;\\n            --secondary-emotion: #1C2B5E;\\n            --neutral-light: #F2F1F2;\\n            --accent-dark: #32373C;\\n            --success: #10B981;\\n            --warning: #F59E0B;\\n            --error: #EF4444;\\n\\n            --heading-font: 'Mint Grotesk Display ExtraBold', sans-serif;\\n            --body-font: 'Mint Grotesk Medium', sans-serif;\\n            --heading-weight: 800;\\n            --body-weight: 500;\\n            --desktop-comfortable: 2rem;\\n            --desktop-spacious: 3rem;\\n            --desktop-easing: cubic-bezier(0.4, 0.0, 0.2, 1);\\n            --desktop-smooth: 300ms;\\n        }\\n\\n        @font-face {\\n            font-family: 'Mint Grotesk Medium';\\n            src: url('https://www.shiftkey.com/fonts/MintGrotesk-Medium.woff2') format('woff2');\\n            font-weight: 500;\\n            font-style: normal;\\n            font-display: swap;\\n        }\\n\\n        @font-face {\\n            font-family: 'Mint Grotesk Display ExtraBold';\\n            src: url('https://www.shiftkey.com/fonts/MintGroteskDisplay-ExtraBold.woff2') format('woff2');\\n            font-weight: 800;\\n            font-style: normal;\\n            font-display: swap;\\n        }\\n        \\n        body { font-family: var(--body-font); background-color: var(--neutral-light); color: var(--accent-dark); }\\n        h1, h2, h3, h4, h5, h6, .heading { font-family: var(--heading-font); font-weight: var(--heading-weight); color: var(--secondary-emotion); }\\n        .btn, button, .cta { font-family: var(--heading-font); font-weight: var(--heading-weight); }\\n        \\n        .desktop-card { background: white; border-radius: 16px; padding: var(--desktop-comfortable); box-shadow: 0 4px 20px rgba(0,0,0,0.05); border: 1px solid #E5E7EB; }\\n        \\n        .timeline-item {\\n            opacity: 0;\\n            transform: translateX(-20px);\\n            animation: fadeIn 0.5s var(--desktop-easing) forwards;\\n        }\\n    \\n        @keyframes fadeIn {\\n            to {\\n                opacity: 1;\\n                transform: translateX(0);\\n            }\\n        }\\n\\n        .timeline-line {\\n            content: '';\\n            position: absolute;\\n            left: 20px;\\n            top: 20px;\\n            bottom: -20px;\\n            width: 2px;\\n            background-color: #E5E7EB;\\n        }\\n\\n        .timeline-dot {\\n            position: absolute;\\n            left: 20px;\\n            top: 20px;\\n            transform: translateX(-50%);\\n            width: 16px;\\n            height: 16px;\\n            border-radius: 9999px;\\n            border: 3px solid white;\\n        }\\n    </style>\\n</head>\\n<body class=\\\"antialiased\\\">\\n    <div class=\\\"flex min-h-screen\\\">\\n        <!-- Sidebar Navigation -->\\n        <aside class=\\\"w-64 bg-white border-r border-gray-200 flex flex-col\\\">\\n            <div class=\\\"p-6 flex items-center space-x-3 border-b border-gray-200\\\">\\n                <a href=\\\"Dashboard_Page.html\\\">\\n                    <img src=\\\"https://cdn.brandfetch.io/idI5LYj5RP/theme/dark/logo.svg?c=1dxbfHSJFAPEGdCLU4o5B\\\" alt=\\\"MV Credit Union Logo\\\" class=\\\"h-8\\\">\\n                </a>\\n            </div>\\n            <nav class=\\\"flex-1 p-6 space-y-2\\\">\\n                <a href=\\\"Dashboard_Page.html\\\" class=\\\"flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg\\\">\\n                    <i data-lucide=\\\"layout-dashboard\\\" class=\\\"w-5 h-5 mr-3\\\"></i>\\n                    <span>Dashboard</span>\\n                </a>\\n            </nav>\\n        </aside>\\n\\n        <!-- Main Content -->\\n        <div class=\\\"flex-1\\\">\\n            <header class=\\\"bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10\\\">\\n                <div class=\\\"px-8 h-20 flex items-center justify-between\\\">\\n                    <div>\\n                         <a href=\\\"Dashboard_Page.html\\\" class=\\\"text-sm text-gray-500 hover:text-gray-800 flex items-center mb-1\\\">\\n                            <i data-lucide=\\\"arrow-left\\\" class=\\\"w-4 h-4 mr-2\\\"></i>Back to Dashboard\\n                        </a>\\n                        <h1 class=\\\"text-3xl heading\\\">MVU Platinum Rewards</h1>\\n                    </div>\\n                    <div class=\\\"flex items-center space-x-4\\\">\\n                        <span class=\\\"px-4 py-2 text-base rounded-full bg-blue-100 text-blue-800 font-medium\\\">Awaiting Mastercard Feedback</span>\\n                         <button class=\\\"btn btn-secondary px-5 py-3 rounded-lg flex items-center space-x-2 cta\\\">\\n                            <i data-lucide=\\\"download\\\" class=\\\"w-5 h-5\\\"></i>\\n                            <span>Download Package</span>\\n                        </button>\\n                    </div>\\n                </div>\\n            </header>\\n\\n            <main class=\\\"p-8 lg:p-12\\\">\\n                <div class=\\\"grid grid-cols-1 lg:grid-cols-3 gap-8\\\">\\n                    <!-- Timeline -->\\n                    <div class=\\\"lg:col-span-2\\\">\\n                        <div class=\\\"desktop-card p-10\\\">\\n                            <h2 class=\\\"text-2xl heading mb-8\\\">Approval Timeline</h2>\\n                            <div class=\\\"relative\\\">\\n                                <!-- Timeline Item 4: Current -->\\n                                <div class=\\\"relative pl-12 pb-12 timeline-item\\\" style=\\\"animation-delay: 600ms;\\\">\\n                                    <div class=\\\"timeline-line\\\"></div>\\n                                    <div class=\\\"timeline-dot bg-blue-500\\\"></div>\\n                                    <p class=\\\"text-sm text-gray-500 mb-1\\\">July 20, 2024 - 10:05 AM</p>\\n                                    <h3 class=\\\"text-lg font-semibold text-gray-800\\\">Submitted to Mastercard</h3>\\n                                    <p class=\\\"text-gray-600 mt-1\\\">The design package has been sent to Mastercard for final review. Expected response within 5-7 business days.</p>\\n                                    <div class=\\\"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg\\\">\\n                                        <p class=\\\"font-semibold text-blue-800\\\">Mastercard Submission ID: MC-DES-84621-B</p>\\n                                    </div>\\n                                </div>\\n                                \\n                                <!-- Timeline Item 3: Completed -->\\n                                <div class=\\\"relative pl-12 pb-12 timeline-item\\\" style=\\\"animation-delay: 400ms;\\\">\\n                                    <div class=\\\"timeline-line\\\"></div>\\n                                    <div class=\\\"timeline-dot bg-green-500\\\"></div>\\n                                    <p class=\\\"text-sm text-gray-500 mb-1\\\">July 20, 2024 - 9:30 AM</p>\\n                                    <h3 class=\\\"text-lg font-semibold text-gray-800\\\">Internal Compliance Approved</h3>\\n                                    <p class=\\\"text-gray-600 mt-1\\\">The design passed all internal checks. Approved by Maria Garcia.</p>\\n                                </div>\\n\\n                                <!-- Timeline Item 2: Completed -->\\n                                <div class=\\\"relative pl-12 pb-12 timeline-item\\\" style=\\\"animation-delay: 200ms;\\\">\\n                                     <div class=\\\"timeline-line\\\"></div>\\n                                    <div class=\\\"timeline-dot bg-green-500\\\"></div>\\n                                    <p class=\\\"text-sm text-gray-500 mb-1\\\">July 19, 2024 - 3:15 PM</p>\\n                                    <h3 class=\\\"text-lg font-semibold text-gray-800\\\">Internal Review Started</h3>\\n                                    <p class=\\\"text-gray-600 mt-1\\\">Compliance checklist initiated for internal review.</p>\\n                                </div>\\n                                \\n                                <!-- Timeline Item 1: Completed -->\\n                                <div class=\\\"relative pl-12 timeline-item\\\" style=\\\"animation-delay: 0ms;\\\">\\n                                    <div class=\\\"timeline-dot bg-green-500\\\"></div>\\n                                    <p class=\\\"text-sm text-gray-500 mb-1\\\">July 19, 2024 - 2:00 PM</p>\\n                                    <h3 class=\\\"text-lg font-semibold text-gray-800\\\">Project Created</h3>\\n                                    <p class=\\\"text-gray-600 mt-1\\\">New card design project \\\"MVU Platinum Rewards\\\" was created and artwork was uploaded.</p>\\n                                </div>\\n                            </div>\\n                        </div>\\n                    </div>\\n\\n                    <!-- Details & Artwork -->\\n                    <div class=\\\"space-y-8\\\">\\n                        <div class=\\\"desktop-card\\\">\\n                            <h2 class=\\\"text-2xl heading mb-4\\\">Artwork Preview</h2>\\n                            <div class=\\\"aspect-video bg-gray-200 rounded-lg flex items-center justify-center\\\">\\n                                <img src=\\\"https://pixabay.com/get/g1d550516fa61b8a35e62c1474ba54aceef186385205e1b2ecd0d8457b805c40513145c21a74c17c1c6b42e6701de012f102cfa96401f07a8074e63c4d6e073a4_1280.jpg\\\" alt=\\\"Artwork for MVU Platinum Rewards card showing a modern geometric pattern\\\" class=\\\"w-full h-full object-cover rounded-lg\\\">\\n                            </div>\\n                        </div>\\n                        <div class=\\\"desktop-card\\\">\\n                            <h2 class=\\\"text-2xl heading mb-4\\\">Project Details</h2>\\n                            <div class=\\\"space-y-3 text-sm\\\">\\n                                <div class=\\\"flex justify-between\\\">\\n                                    <span class=\\\"text-gray-500\\\">Card Type</span>\\n                                    <span class=\\\"font-semibold text-gray-800\\\">Mastercard Credit</span>\\n                                </div>\\n                                <div class=\\\"flex justify-between\\\">\\n                                    <span class=\\\"text-gray-500\\\">Created By</span>\\n                                    <span class=\\\"font-semibold text-gray-800\\\">Maria Garcia</span>\\n                                </div>\\n                                <div class=\\\"flex justify-between\\\">\\n                                    <span class=\\\"text-gray-500\\\">Creation Date</span>\\n                                    <span class=\\\"font-semibold text-gray-800\\\">July 19, 2024</span>\\n                                </div>\\n                                 <div class=\\\"flex justify-between\\\">\\n                                    <span class=\\\"text-gray-500\\\">Internal ID</span>\\n                                    <span class=\\\"font-semibold text-gray-800\\\">MVU-2024-007</span>\\n                                </div>\\n                            </div>\\n                        </div>\\n                        <div class=\\\"desktop-card bg-red-50 border-red-200\\\">\\n                             <h3 class=\\\"heading text-xl text-red-800\\\">Action Required</h3>\\n                             <p class=\\\"text-red-700 mt-2\\\">Mastercard has requested revisions. Please review their feedback and submit an updated design.</p>\\n                             <button class=\\\"mt-4 w-full bg-red-600 text-white py-2 rounded-lg font-semibold hover:bg-red-700 transition\\\">View Feedback & Revise</button>\\n                        </div>\\n                    </div>\\n                </div>\\n            </main>\\n        </div>\\n    </div>\\n\\n    <script>\\n        document.addEventListener('DOMContentLoaded', () => {\\n            lucide.createIcons();\\n        });\\n    </script>\\n</body>\\n</html>\", \"fileName\": \"Track_Page.html\"}]", "created_at": "2025-07-22T18:09:45.549110+00:00"}], "metadata": "null"}