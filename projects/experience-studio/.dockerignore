# =============================================================================
# Docker Ignore Configuration for Experience Studio Application
# =============================================================================
# 
# This file excludes unnecessary files from the Docker build context
# to optimize build performance and reduce image size.
# =============================================================================

# =============================================================================
# Exclude Everything by Default
# =============================================================================
*

# =============================================================================
# Include Required Files
# =============================================================================
# Root configuration files
!package.json
!package-lock.json
!angular.json
!tsconfig.json
!.npmrc
!ava-play-0.6.tgz

# Application source code
!projects/experience-studio/
!projects/experience-studio/**

# Shared libraries
!projects/shared/
!projects/shared/**

# =============================================================================
# Explicit Exclusions (for clarity)
# =============================================================================
# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist
out-tsc
*.tsbuildinfo

# IDE and editor files
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory
coverage
*.lcov

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack' (excluding specific packages we need)
*.tgz
!ava-play-0.6.tgz

# Yarn Integrity file
.yarn-integrity

# Environment variables
.env
.env.test
.env.production
.env.local

# Cache directories
.cache
.parcel-cache

# Git
.git
.gitignore

# Docker files (to avoid recursion)
Dockerfile*
docker-compose*
.dockerignore

# Documentation
README.md
CHANGELOG.md
LICENSE

# Test files
*.spec.ts
*.test.ts
test/
tests/
__tests__/

# Angular specific
.angular/ 