<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_8250_32659)">
    <rect width="1920" height="1080" fill="#F8F8FF"/>
    <g opacity="0.3" filter="url(#filter0_f_8250_32659)">
    <circle cx="658" cy="359" r="790" fill="url(#paint0_radial_8250_32659)"/>
    </g>
    <g opacity="0.3" filter="url(#filter1_f_8250_32659)">
    <circle cx="790" cy="790" r="790" transform="matrix(-1 0 0 1 2258 -470)" fill="url(#paint1_radial_8250_32659)" fill-opacity="0.15"/>
    </g>
    </g>
    <defs>
    <filter id="filter0_f_8250_32659" x="-212" y="-511" width="1740" height="1740" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
    <feFlood flood-opacity="0" result="BackgroundImageFix"/>
    <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
    <feGaussianBlur stdDeviation="40" result="effect1_foregroundBlur_8250_32659"/>
    </filter>
    <filter id="filter1_f_8250_32659" x="598" y="-550" width="1740" height="1740" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
    <feFlood flood-opacity="0" result="BackgroundImageFix"/>
    <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
    <feGaussianBlur stdDeviation="40" result="effect1_foregroundBlur_8250_32659"/>
    </filter>
    <radialGradient id="paint0_radial_8250_32659" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(77.438 291.142) rotate(43.2502) scale(1163.98 1393.43)">
    <stop stop-color="#FCC1DC"/>
    <stop offset="1" stop-color="white"/>
    </radialGradient>
    <radialGradient id="paint1_radial_8250_32659" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(209.438 722.142) rotate(43.2502) scale(1163.98 1393.43)">
    <stop stop-color="#E30A6D"/>
    <stop offset="1" stop-color="white"/>
    </radialGradient>
    <clipPath id="clip0_8250_32659">
    <rect width="1920" height="1080" fill="white"/>
    </clipPath>
    </defs>
    </svg>
    