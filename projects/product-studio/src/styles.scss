/* You can add global styles to this file, and also import other style files */

@use "../public/assets/styles/padding.scss";
@use "../public/assets/styles/_variables.scss";
@use "../public/assets/styles/_themes.scss";
@use "../public/assets/styles/_mixins.scss";
@use "../public/assets/styles/animation.scss";

* {
  font-family: "Mulish", sans-serif;
}

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  font-family: "Mulish", sans-serif;
  background-color: #f5f5f7;
}

/* Global Scrollbar Styling */
* {
  /* For Webkit browsers (Chrome, Safari) */
  &::-webkit-scrollbar {
    width: 6px;
    height: 8px;
  }

  ::-webkit-scrollbar-button {
    display: none;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb, rgba(0, 0, 0, 0.2));
    border-radius: 4px;
    transition: background 0.3s ease;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover, rgba(0, 0, 0, 0.4));
  }

  /* For Firefox */
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb, rgba(0, 0, 0, 0.2)) transparent;
}

body {
  display: flex;
  flex-direction: column;
}

app-root {
  display: flex;
  flex-direction: column;
  flex: 1;
}

html {
  font-size: 13px !important;
}

@media (min-width: 600px) and (max-width: 1023px) {
  html {
    font-size: 14px !important;
  }
}

@media (min-width: 1024px) {
  html {
    font-size: 16px !important;
  }
}

.cursor-pointer {
  cursor: pointer;
}

.text-center {
  text-align: center;
}

.w-100 {
  width: 100%;
}
