@use '../../../../public/assets/styles/_mixins.scss' as mixins;
#prompt-content-container {
  margin-top: 7%;
  #suggetions-contianer {
    .invisible {
      visibility: hidden !important;
    }
  }

  #divider-section-container {
    padding-top: 4rem;
    .divider-image {
      width: 100%;
      @media (max-width: 767px) {
        max-width: 90%;
      }
    }
  }
}

:host {
  ::ng-deep {
    .prompt-bar {
      &.dark {
        @include mixins.prompt-bar-style(none, breatheDark, rgba(255, 92, 163, 1));
      }
      &.light {
        @include mixins.prompt-bar-style(
          var(--Light---60, rgba(240, 240, 245, 0.5)),
          breatheLight,
          rgba(66, 68, 194, 0.8)
        );
      }
    }
  }
}

#prompt-bar-container {
  width: 100%;
  ::ng-deep .prompt-bar-wrapper {
    width: 100%;
  }
  ::ng-deep .prompt-input-wrapper {
    width: 100%;
  }
  ::ng-deep .text-input-container {
    width: 100%;
  }
}
// Code Cleanup done till here


.container-fluid {
  display: flex;
  flex-direction: column;
  // min-height: 85vh;
}


:host ::ng-deep awe-cards {
  width: 100% !important;
}

:host ::ng-deep .awe-card--medium {
  padding: none !important;
  border-radius: 8px !important;
  background-color: var(--feature-card-bg);
}

:host ::ng-deep .awe-card--image-right .awe-card__media {
  width: 66%;
}

:host ::ng-deep .awe-card--info {
  text-align: start;
  padding-right: 10px !important;
}

::ng-deep button.primary {
  background: var(--feature-card-btn);
  border-radius: 6px;
  width: 81px;
}

::ng-deep awe-cards {
  width: 576px !important;
  // height: 280px !important;
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;

  .awe-card-title{
    font-size: 40px;
  }
}

:host ::ng-deep .awe-card--light {
  background: var(--feature-card-bg);
  color: var(--feature-card-text-color);
  border: 1.5px solid var(--feature-card-border-front);
  backdrop-filter: blur(7.5px);
}

awe-cards p {
  font-size: 14px;
  width: 80%;
}

#projects-section-container {
  width: 100%;
  padding: 2rem 0;
  background-color: transparent;
  min-height: 200px;
  display: block;
}

@media (min-width: 1420px) {
  .col-xl-6 {
    max-width: 33.33%;
  }
}
