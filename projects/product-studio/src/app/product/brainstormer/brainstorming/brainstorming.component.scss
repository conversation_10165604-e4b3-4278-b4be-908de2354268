.brainstorming-container {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  overflow-y: auto;
  // background-color: #f5f5f7;

  .page-header {
    text-align: center;

    .page-title {
      font-size: 28px;
      font-weight: bold;
      color: #333;
      margin-bottom: 0.5rem;
    }

    .page-description {
      font-size: 16px;
      color: #666;
      margin: 0;
    }
  }

  .sub-header {
    display: flex;
    height: 54px;
    padding: 9px 26px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    border-radius: 12px;
    background: var(--page-header-bg);

    .sub-header-title {
      font-size: 20px;
      font-weight: bold;
      color: #fff;
    }

    .sub-header-actions {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 1rem;

      button {
        min-width: 0;
        border: none;
        height: 40px;
        width: 40px;
        border-radius: 50px;
        background: #fff;
        margin-top: 0;
        line-height: none;
        min-height: 0;
      }
    }
  }

  .stepper-section {
    flex-shrink: 0;
  }

  .content-container-panel {
    flex: 1;
    max-width: 6048px;
    // max-height: 3048px;
    min-width: 1780px;
    // min-height: 1016px !important;
    height: 65vh;

    .step-content {
      animation: fadeIn 0.3s ease-in-out;
    }
  }

  .content-container {
    flex: 1;
    .step-content {
      animation: fadeIn 0.3s ease-in-out;
    }
  }

  .ai-assistant-prompt {
    display: inline-flex;
    align-items: center;
    background-color: #ffffff;
    min-width: 0;
    max-width: 320px;
    height: 59px;
    border-radius: 28.5px;
    background: #fff;
    box-shadow: 0px 0px 20px 0px rgba(151, 151, 151, 0.25);
    // box-shadow: 0px 6px 16px rgba(0, 0, 0, 0.08);
    font-family: "Inter", sans-serif;
    position: fixed;
    bottom: 30px;
    left: 30px;
    z-index: 1001;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    animation: float 3s ease-in-out infinite;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0px 8px 25px rgba(151, 151, 151, 0.35);
    }

    .ai-assistant-icon {
      border: none;
      cursor: pointer;
      width: 59px;
      height: 59px;
      flex-shrink: 0;
      border-radius: 50px;
      background-color: #ffffff;
      border: 1px solid #eaeaea;
      box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.15);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

      &:hover {
        border-color: #4a90e2;
        box-shadow: 0px 4px 20px rgba(74, 144, 226, 0.3);
      }

      &:active {
        transform: scale(0.95);
      }

      &.chat-open {
        background-color: #4a90e2;
        border-color: #4a90e2;
        box-shadow: 0px 4px 20px rgba(74, 144, 226, 0.5);

        img {
          filter: brightness(0) invert(1); // Make the icon white when chat is open
        }

        .pulse-ring,
        .pulse-ring-2 {
          border-color: #ffffff;
        }
      }

      img {
        width: 47px;
        height: 47px;
        object-fit: cover;
        transition: transform 0.3s ease;
        z-index: 2;
      }
    }

    .ai-assistant-text {
      font-size: 18px;
      padding: 0 1rem;
      font-weight: 600;
      color: #1a1a1a;
      white-space: nowrap;
      letter-spacing: -0.2px;
      transition: color 0.3s ease;
    }
  }

  .ai-assistant-prompt-chat-open {
    display: flex;
    width: 57.717px;
    padding: 6.413px;
    flex-direction: column;
    align-items: flex-start;
    gap: 6.413px;
    flex-shrink: 0;
    border-radius: 128.261px;
    border: 0.641px solid #aeb0bc;
    background: #fff;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.25);

    .ai-assistant-icon-chat-open {
      display: flex;
      width: 46.174px;
      height: 46.174px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 6.413px;
      border-radius: 64.13px;
      background: #000;

      img {
        width: 47px;
        height: 47px;
        object-fit: cover;
        transition: transform 0.3s ease;
        z-index: 2;
      }
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 0.7;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.3;
    }
    100% {
      transform: scale(1.4);
      opacity: 0;
    }
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .brainstorming-container {
    padding: 0.5rem;
    gap: 1rem;

    .page-header {
      .page-title {
        font-size: 24px;
      }

      .page-description {
        font-size: 14px;
      }
    }

    .action-buttons {
      flex-direction: column;
      gap: 1rem;

      .fab-button {
        width: 70px;
        height: 70px;
        order: 2;

        img {
          width: 32px;
          height: 32px;
        }
      }

      .navigation-buttons {
        order: 1;
        width: 100%;
        justify-content: space-between;

        .nav-button {
          flex: 1;
          max-width: 150px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .brainstorming-container {
    .ai-assistant-prompt {
      width: 250px;
      height: 50px;
      bottom: 15px;
      right: 15px;

      .ai-assistant-icon {
        width: 50px;
        height: 50px;

        img {
          width: 38px;
          height: 38px;
        }
      }

      .ai-assistant-text {
        font-size: 14px;
        margin-left: 10px;
      }
    }

    .navigation-buttons {
      flex-direction: column;
      width: 100%;

      .nav-button {
        width: 100%;
        max-width: none;
      }
    }
  }
}

// Split Screen Styles

/* Chat Panel Styles */
.chat-panel {
  display: flex;
  height: 100%;
  flex-direction: column;
  position: relative;
  animation: slideInLeft 0.5s ease-out;
  flex-shrink: 0;
  border-radius: 12px;
  background: #fff;
  box-shadow: 0px 0px 20px 0px rgba(151, 151, 151, 0.25);

  // Ensure the panel takes full height and positions input at bottom
  justify-content: space-between;
}

// Chat content area (messages, etc.) - takes remaining space above input
.chat-content {
  flex: 1; // Take all available space above the input
  overflow-y: auto; // Allow scrolling for chat messages
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;

  // Custom scrollbar for chat content
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// Welcome message styling
.welcome-message {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;

  p {
    margin: 0;
    color: #4a5568;
    font-size: 14px;
    line-height: 1.5;
    text-align: center;
  }
}

@keyframes slideInLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.chat-header {
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  border-radius: 12px;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent
    );
  }
}

.chat-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.status-indicator {
  width: 10px;
  height: 10px;
  background: #48bb78;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(72, 187, 120, 0.5);
  animation: pulse-status 2s infinite;
}

@keyframes pulse-status {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.chat-messages {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.message {
  margin-bottom: 12px;
}

.bot-message .message-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 14px 18px;
  border-radius: 20px 20px 20px 6px;
  max-width: 85%;
  font-size: 15px;
  line-height: 1.5;
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
  position: relative;
  animation: messageSlideIn 0.4s ease-out;

  &::before {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 6px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid #764ba2;
  }
}

@keyframes messageSlideIn {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.chat-input-container {
  // padding: 16px;
  border-top: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  // gap: 8px;

  // Remove absolute positioning - use flexbox instead
  // This will make it stick to bottom naturally and resize with panel
  flex-shrink: 0; // Don't shrink when panel content grows
  width: 100%; // Take full width of the panel
  box-sizing: border-box; // Include padding in width calculation

  // Ensure proper spacing and alignment
  justify-content: flex-start;

  // Add background to distinguish from chat content
  background: #fff;
  border-radius: 0 0 12px 12px; // Match panel border radius

  // AI Assistant Icon styling
  .ai-assistant-icon {
    flex-shrink: 0; // Don't shrink the icon
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    &:active {
      transform: scale(0.95);
    }

    img {
      width: 47px;
      height: 47px;
      object-fit: contain;
    }
  }

  // Input field styling within the container
  awe-input {
    flex: 1; // Take remaining space after icon
    min-width: 0; // Allow input to shrink if needed
  }
}

:host ::ng-deep .input-container label {
  display: none;
}
:host ::ng-deep .input-container .input-wrapper {
  width: auto;
  border: 1px solid #e2e8f0;
  // border-radius: 50px;
}

/* Main Content Styles */
/* Main Content Styles - Updated for proper overflow handling */
.main-content {
  display: flex;

  height: 65vh; // Fixed height for split screen mode
  flex-direction: column;
  flex-shrink: 0;
  overflow: visible;

  .content-container {
    flex: 1;
    min-height: 0;
    overflow: auto;
    padding: 5px;
    scroll-behavior: smooth;
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;

      &:hover {
        background: #a8a8a8;
      }
    }

    // Ensure step content maintains its natural size
    .step-content {
      animation: fadeIn 0.3s ease-in-out;

      // Remove any max-height or height constraints that might limit content
      min-height: fit-content;
      width: 100%;

      // Let the content determine its own size
      // This ensures the content renders at the same size as non-split screen mode
      > * {
        max-width: none; // Remove any width constraints
      }
    }
  }
}

.finish-btn {
  
  position: fixed;
  bottom: 49px;
  right: 54px;
  z-index: 1000;
  border-radius: 8px;
  padding: 10px 20px;
  border: 1px solid var(--Primary-50, #f2ebfd);
  background: var(
    --Main-gradient,
    linear-gradient(
      109deg,
      var(--Primary-500, #7c3aed) 4.7%,
      var(--Secondary-500, #2563eb) 94.91%
    )
  );
  button {
    border-radius: 0.5rem;
    background-color: transparent;
    border: none;
    min-width: 0;
    color:#fff;
  }
}
