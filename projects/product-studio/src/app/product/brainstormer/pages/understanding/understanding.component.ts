import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ChangeDetectorRef, Component, OnInit, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import {
  BodyTextComponent,
  ButtonComponent,
  CaptionComponent,
  HeadingComponent,
  InputComponent,
} from '@awe/play-comp-library';
import { IconsComponent } from '../../../../shared/components/icons/icons.component';
import { AweCardComponent } from '../../components/awe-card/awe-card.component';
import { AweModalComponent } from '../../components/awe-modal/awe-modal.component';
import { UnderstandingDataService, CanvasItem } from '../../services/understanding-data.service';

@Component({
  selector: 'app-understanding',
  templateUrl: './understanding.component.html',
  styleUrls: ['./understanding.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    CaptionComponent,
    AweCardComponent,
    IconsComponent,
    AweModalComponent,
    HeadingComponent,
    BodyTextComponent,
    InputComponent,
    ButtonComponent,
  ],
})
export class UnderstandingComponent implements OnInit, OnDestroy {
  // Action Icons (used in the projected header)
  trashIcon: string = 'icons/awe_trash.svg';
  editIcon: string = 'icons/awe_edit.svg';
  errorIcon: string = 'icons/awe_error.svg';

  // Subscription management
  private subscription = new Subscription();

  // Card Data Icons (used in the projected header)
  problemIcon: string = 'cards-icons/problem.svg';
  keyPartnerCardImg: string = 'cards-images/key-partners.png';
  valuePropositionCardImg: string = 'cards-images/value-proposition.png';
  solutionCardImg: string = 'cards-images/solution-card.png';
  customerSegmentsCardImg: string = 'cards-images/ customer-segments.png';
  keyMetricsCardImg: string = 'cards-images/key-metrics-card.png';
  alternativesCardImg: string = 'cards-images/alternatives-card.png';
  costStructureCardImg: string = 'cards-images/cost-structure.png';
  revenueStreamsCardImg: string = 'cards-images/revenue-stream.png';

  button_bg: string =
    'linear-gradient(90deg, rgba(101, 102, 205, 0.40) -2.03%, rgba(249, 108, 171, 0.40) 109.39%);';
  loadingStates: Record<string, boolean> = {
    submit: false,
    save: false,
    delete: false,
    skeleton1: false,
    skeleton2: false,
  };

  // Main data source for the cards
  businessModelCanvas: CanvasItem[] = [];

  // Modal State
  isEditModalOpen = false;
  selectedItemForEdit: CanvasItem | null = null;
  editableItemData: string[] = [];
  regeneratePrompt: string = '';

  constructor(
    private cdRef: ChangeDetectorRef,
    private understandingDataService: UnderstandingDataService
  ) {}

  ngOnInit(): void {
    // Subscribe to data changes
    this.subscription.add(
      this.understandingDataService.businessModelCanvas$.subscribe(
        (canvas) => {
          this.businessModelCanvas = canvas;
          this.updateIconsForCanvas();
        }
      )
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  private updateIconsForCanvas(): void {
    // Update icons for canvas items based on their IDs
    this.businessModelCanvas.forEach(item => {
      switch (item.id) {
        case 'problem':
          item.icon = this.problemIcon;
          break;
        case 'key-partners':
          item.icon = this.keyPartnerCardImg;
          break;
        case 'value-proposition':
          item.icon = this.valuePropositionCardImg;
          break;
        case 'solution':
          item.icon = this.solutionCardImg;
          break;
        case 'customer-segments':
          item.icon = this.customerSegmentsCardImg;
          break;
        case 'key-metrics':
          item.icon = this.keyMetricsCardImg;
          break;
        case 'alternatives':
          item.icon = this.alternativesCardImg;
          break;
        case 'cost-structure':
          item.icon = this.costStructureCardImg;
          break;
        case 'revenue-streams':
          item.icon = this.revenueStreamsCardImg;
          break;
      }
    });
  }

  get firstRowProblemCard(): CanvasItem[] {
    return this.understandingDataService.getFirstRowProblemCard();
  }

  get firstRowSolutionCard(): CanvasItem[] {
    return this.understandingDataService.getFirstRowSolutionCard();
  }

  get keyPartnersData(): CanvasItem[] {
    return this.understandingDataService.getKeyPartnersData();
  }

  get keyMetricsAlternativesData(): CanvasItem[] {
    return this.understandingDataService.getKeyMetricsAlternativesData();
  }

  get customerSegmentItems(): CanvasItem[] {
    return this.understandingDataService.getCustomerSegmentItems();
  }

  get costRevenueItems(): CanvasItem[] {
    return this.understandingDataService.getCostRevenueItems();
  }

  // Action handlers
  onEdit(item: CanvasItem): void {
    console.log('Edit:', item.title);
    this.openEditModal(item);
  }

  onDelete(item: CanvasItem): void {
    console.log('Delete:', item.title);
    // Implement delete functionality if needed
  }

  // Modal Methods
  openEditModal(item: CanvasItem): void {
    this.selectedItemForEdit = item;
    this.editableItemData = [...item.data];
    this.regeneratePrompt = '';
    this.isEditModalOpen = true;
  }

  closeEditModal(): void {
    this.isEditModalOpen = false;
    this.selectedItemForEdit = null;
    this.editableItemData = [];
  }

  updateUnderstandingItem(event: Event): void {
    if (this.selectedItemForEdit) {
      // Update via service
      this.understandingDataService.updateCanvasItem(
        this.selectedItemForEdit.id,
        [...this.editableItemData]
      );

      // Handle regenerate prompt
      if (this.regeneratePrompt) {
        console.log('Regenerate with prompt:', this.regeneratePrompt);
        this.loadingStates['submit'] = true;
        setTimeout(() => {
          this.loadingStates['submit'] = false;
          console.log('Submit completed:', event);
        }, 2000);
        this.regeneratePrompt = '';
      }
      this.closeEditModal();
    }
  }

  // Methods for managing editableItemData in the modal
  addEditableDataItem(): void {
    this.editableItemData.push('');
    this.cdRef.detectChanges();
    setTimeout(() => {
      const inputs = document.querySelectorAll('.edit-data-item-input');
      const lastInput = inputs[inputs.length - 1] as HTMLInputElement;
      if (lastInput) {
        lastInput.focus();
      }
    });
  }

  removeEditableDataItem(index: number): void {
    this.editableItemData.splice(index, 1);
  }

  trackByFn(index: number, _item: any): any {
    return index;
  }
}
