<div class="container-fluid mt-3" (click)="closeAllDropdowns()">
  <div class="row g-3">
    <div *ngFor="let section of swotDataService.getSections()" class="col-12 col-sm-6 col-lg-3">
      <div class="section-header p-3">
        <!-- Child 1: The title text -->
        <div class="section-title">
          <span>{{ section.subtitle }}</span>
        </div>
        <!-- Child 2: The action button with initials -->
        <div class="section-action">{{ section.title }}</div>
      </div>

      <!-- Drop Zone -->
      <div
        cdkDropList
        [id]="section.id"
        [cdkDropListData]="section.features"
        [cdkDropListConnectedTo]="swotDataService.getSectionIds()"
        (cdkDropListDropped)="onDrop($event)"
        class="feature-list bg-light border border-top-0 p-2 d-flex flex-column flex-lg-grow-1"
      >
        <!-- Feature Cards -->
        <awe-card
          *ngFor="let feature of section.features"
          class="feature-card bg-white shadow-sm rounded p-3 position-relative"
          [showHeader]="true"
          [showBody]="true"
          [applyHeaderPadding]="true"
          [applyBodyPadding]="true"
          cardClass="feature-item-card"
          cdkDrag
          (cdkDragStarted)="
            $event.source.element.nativeElement.style.cursor = 'grabbing'
          "
          (cdkDragEnded)="
            $event.source.element.nativeElement.style.cursor = 'grab'
          "
        >
          <div
            awe-card-header-content
            class="d-flex justify-content-between align-items-center"
          >
            <awe-heading
              variant="s2"
              type="bold"
              class="feature-title mb-0 flex-grow-1 pe-2"
              >{{ feature.title }}</awe-heading
            >

            <awe-icons
              (click)="toggleDropdown(feature.id, $event)"
              [iconName]="'three-dot-vertical'"
              [iconColor]="'blue'"
              class="three-dot-icon"
            ></awe-icons>
            <div class="dropdown-arrow position-relative">
              <div
                class="dropdown-menu dropdown-menu-end"
                [class.show]="isDropdownOpen(feature.id)"
              >
                <button
                  class="dropdown-item border-buttom"
                  (click)="openEditModal(feature)"
                  type="button"
                >
                  Edit
                </button>
                <button
                  class="dropdown-item text-danger"
                  type="button"
                  (click)="deleteFeature(feature.id); closeAllDropdowns()"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>

          <!-- Card Description -->
          <awe-body-text
            type="body-text"
            class="feature-description text-muted small flex-grow-1"
          >
            {{ feature.description }}
          </awe-body-text>

          <!-- Progress Bars -->
          <div class="mb-3">
            <!-- Impact Progress Bar -->
            <div class="mb-2">
              <div
                class="d-flex justify-content-between align-items-center mb-1"
              >
                <span class="small fw-medium text-muted">Impact</span>
              </div>
              <div class="progress">
                <div
                  class="progress-bar"
                  [style.width]="getImpactWidth(feature)"
                  [style.background-color]="getImpactColor(feature.impact)"
                  role="progressbar"
                ></div>
              </div>
            </div>

            <!-- Priority Progress Bar -->
            <div class="mb-2">
              <div
                class="d-flex justify-content-between align-items-center mb-1"
              >
                <span class="small fw-medium text-muted">Priority</span>
              </div>
              <div class="progress">
                <div
                  class="progress-bar"
                  [style.width]="getPriorityWidth(feature)"
                  [style.background-color]="getPriorityColor(feature.priority)"
                  role="progressbar"
                ></div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
        </awe-card>
        <div
          *ngIf="section.features.length === 0"
          class="text-center text-muted fst-italic py-4"
        >
          Drag and drop SWOT items here
        </div>

        <!-- Add More Button -->
        <div class="add-more-section mt-3 text-center p-3">
          <button
            class="add-more-btn w-100"
            (click)="addNewFeature(section.id)"
          >
            Add more
            <awe-icons iconName="awe_plus" class="mt-2"></awe-icons>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Edit Feature Modal -->
<awe-modal
  [isOpen]="isEditModalOpen"
  (closed)="closeEditModal()"
  [showHeader]="true"
  [showFooter]="true"
  width="600px"
  height="auto"
  position="center"
  animation="fade"
  [showCloseButton]="true"
  modalClass="edit-feature-modal"
>
  <!-- Projected Modal Header -->
  <div awe-modal-header class="edit-modal-header">
    <awe-heading variant="s1" type="bold" class="modal-title mb-0">
      {{ isAddingNewFeature ? "Add New SWOT Item" : "Edit SWOT Item" }}
    </awe-heading>
  </div>

  <!-- Projected Modal Body -->
  <div awe-modal-body class="edit-modal-body">
    <h6
      class="feature-title mb-3"
      *ngIf="!isAddingNewFeature && selectedFeatureForEdit"
    >
      {{ selectedFeatureForEdit.title }}
    </h6>
    <h6 class="feature-title mb-3" *ngIf="isAddingNewFeature">
      Create a new SWOT item for this section
    </h6>

    <!-- Feature Title -->
    <div class="row">
      <div class="col-md-12 inp-container mt-2">
        <div class="label">
          <label for="featureTitle">Title:</label>
        </div>
        <div class="input-wrapper">
          <awe-input
            variant="fluid"
            id="featureTitle"
            [(ngModel)]="editableFeatureTitle"
            placeholder="Enter SWOT item title"
            class="w-100"
          ></awe-input>
        </div>
      </div>
      <!-- Feature Description -->
      <div class="col-md-12 mt-2">
        <div>
          <label for="featureDescription">Description:</label>
        </div>
        <div class="input-wrapper">
          <awe-input
            variant="fluid"
            [expand]="true"
            id="featureDescription"
            [(ngModel)]="editableFeatureDescription"
            placeholder="Enter SWOT item description"
            class="w-100"
          ></awe-input>
        </div>
      </div>
    </div>

    <!-- Impact and Priority Sliders -->
    <div class="row mt-3">
      <div class="col-md-12">
        <div class="input-wrapper mb-3">
          <awe-slider
            label="Impact"
            [(ngModel)]="editableFeatureImpact"
            mobileSize="small"
            tabletSize="medium"
            desktopSize="large"
            touchTargetSize="44px"
            [showTicks]="true"
            [customTickValues]="[0, 25, 50, 75, 100]"
            variant="primary"
            [min]="0"
            [max]="100"
          ></awe-slider>
        </div>
        <div class="input-wrapper">
          <awe-slider
            label="Priority"
            [(ngModel)]="editableFeaturePriority"
            mobileSize="small"
            tabletSize="medium"
            desktopSize="large"
            touchTargetSize="44px"
            [showTicks]="true"
            [customTickValues]="[0, 25, 50, 75, 100]"
            variant="primary"
            [min]="0"
            [max]="100"
          ></awe-slider>
        </div>
      </div>
    </div>

    <!-- Regenerate Section -->
    <div class="regenerate-section mt-3">
      <awe-heading variant="s2" type="bold">Regenerate with AI</awe-heading>
      <awe-input
        label="Prompt:"
        [expand]="true"
        [(ngModel)]="regeneratePrompt"
        id="regeneratePrompt"
        [icons]="['awe_send']"
        variant="fluid"
        placeholder="Enter prompt to regenerate content..."
        (iconClickEvent)="updateFeature()"
      >
      </awe-input>
    </div>
  </div>

  <!-- Projected Modal Footer -->
  <div awe-modal-footer class="edit-modal-footer">
    <div class="action-btn gap-4 mt-4 d-flex w-100 justify-content-center">
      <button type="button" class="btn-cancel px-5" (click)="closeEditModal()">
        Cancel
      </button>
      <button type="button" class="btn-delete px-5" (click)="updateFeature()">
        {{ isAddingNewFeature ? "Add SWOT Item" : "Save Changes" }}
      </button>
    </div>
  </div>
</awe-modal>
