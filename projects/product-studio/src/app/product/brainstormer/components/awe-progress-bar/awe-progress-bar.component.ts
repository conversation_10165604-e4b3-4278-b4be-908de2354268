import { Component, Input, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'awe-progress-bar',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './awe-progress-bar.component.html',
  styleUrls: ['./awe-progress-bar.component.scss']
})
export class AweProgressBarComponent implements OnChanges {
  /** The progress value, a number between 0 and 100 */
  @Input() progress: number = 0;

  /** The text displayed inside the semi-circle. Defaults to the progress percentage. */
  @Input() displayText: string = '';

  // This will hold the calculated value for the CSS gradient (0 to 50 for a semi-circle)
  protected gradientProgress: number = 0;

  ngOnChanges(): void {
    // Clamp the progress value between 0 and 100
    const clampedProgress = Math.max(0, Math.min(100, this.progress));
    
    // Convert the 0-100 scale to a 0-50 scale for the half-circle gradient
    this.gradientProgress = clampedProgress / 2;

    // Set the display text if it's not provided
    if (!this.displayText) {
      this.displayText = `${Math.round(clampedProgress)}%`;
    }
  }
}