// Define customizable CSS variables at the host level
:host {
  --progress-color: #743EED; /* The main purple color */
  --trail-color: #e9ecef; /* The light gray background trail */
  --text-color: #343a40; /* Color for the percentage text */
  --background-color: #D5C3FF; /* This color will hide the bottom half */
  --size: 200px; /* The overall width of the component */
  --thickness: 20px; /* The thickness of the progress ring */
}

.progress-semicircle-container {
  width: var(--size);
  height: calc(
    var(--size) / 2
  ); // The container is a rectangle (half the height of the width)
  position: relative;
  overflow: hidden; // This is crucial to hide the bottom half of the inner circle
  display: flex;
  align-items: end; // Aligns the text to the bottom
  justify-content: center;

  // The pseudo-element that creates the actual progress circle
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: var(--size);
    height: var(--size); // It's a full square
    border-radius: 50%;

    // The conic-gradient for progress. It's a full 360-degree gradient.
    // We use a variable that goes from 0-50 because we only see half the circle.
    background: conic-gradient(
      var(--progress-color) calc(var(--progress) * 1%),
      var(--trail-color) 0
    );

    // Create the mask for the center hole
    -webkit-mask: radial-gradient(
      transparent 0,
      transparent calc(50% - var(--thickness)),
      black calc(50% - var(--thickness) + 1px),
      black 50%
    );
    mask: radial-gradient(
      transparent 0,
      transparent calc(50% - var(--thickness)),
      black calc(50% - var(--thickness) + 1px),
      black 50%
    );
  }
}

.progress-text {
  display: flex;
  align-items: end;
  justify-content: end;
  font-size: calc(var(--size) / 7);
  font-weight: bold;
  color: var(--text-color);
  z-index: 1; // Ensure text is on top
  // Adjust position to sit nicely at the bottom of the arc
  transform: translateY(calc(var(--size) * -0.15));
}
