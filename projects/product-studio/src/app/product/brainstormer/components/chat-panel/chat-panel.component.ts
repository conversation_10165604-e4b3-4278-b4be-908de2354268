import { Component, ViewChild, ElementRef, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { InputComponent } from '@awe/play-comp-library';

// Chat message interface
export interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  isTyping?: boolean;
}

@Component({
  selector: 'app-chat-panel',
  imports: [CommonModule, InputComponent],
  templateUrl: './chat-panel.component.html',
  styleUrl: './chat-panel.component.scss'
})
export class ChatPanelComponent {
  // Chat properties
  messages: ChatMessage[] = [];
  currentMessage: string = '';
  isAiTyping: boolean = false;
  isLoading: boolean = false;
  roboBallIcon: string = 'icons/robo_ball.svg';

  @ViewChild('chatContent') chatContent!: ElementRef;
  @ViewChild('messageInput') messageInput!: ElementRef;

  // Output event for closing split screen
  @Output() closeSplitScreen = new EventEmitter<void>();

  // Dummy AI responses for testing
  private dummyResponses: string[] = [
    "That's a great idea! Let me help you explore that further. What specific aspects would you like to focus on?",
    "I understand your concern. Based on your product brainstorming, here are some suggestions that might help...",
    "Interesting perspective! Have you considered how this might impact your target users? Let's think about the user experience.",
    "That's a solid approach. To build on that idea, you might want to consider the technical feasibility and resource requirements.",
    "Great question! From a product strategy standpoint, this could really differentiate your offering in the market.",
    "I see what you're getting at. Let's break this down into smaller, actionable steps that you can implement.",
    "That's definitely worth exploring. How do you think this aligns with your overall product vision and goals?",
    "Excellent point! This could be a key feature that addresses a real pain point for your users.",
    "I like where you're going with this. What would be the success metrics you'd use to measure this feature's impact?",
    "That's a thoughtful consideration. Let's think about the potential challenges and how to overcome them."
  ];

  sendMessage(event?: any) {
    const inputElement = event?.target as HTMLInputElement;
    const message = inputElement?.value?.trim() || this.currentMessage?.trim();

    if (!message) {
      return;
    }

    // Add user message
    this.addMessage(message, 'user');

    // Clear input
    if (inputElement) {
      inputElement.value = '';
    }
    this.currentMessage = '';

    // Show loading and AI typing indicator
    this.isLoading = true;
    this.isAiTyping = true;

    // Simulate API call with delay
    this.simulateAiResponse(message);
  }

  private addMessage(content: string, sender: 'user' | 'ai'): void {
    const message: ChatMessage = {
      id: this.generateMessageId(),
      content,
      sender,
      timestamp: new Date()
    };

    this.messages.push(message);

    // Scroll to bottom after message is added
    setTimeout(() => {
      this.scrollToBottom();
    }, 100);
  }

  private simulateAiResponse(userMessage: string): void {
    // Simulate API delay (1-3 seconds)
    const delay = Math.random() * 2000 + 1000;

    setTimeout(() => {
      this.isLoading = false;
      this.isAiTyping = false;

      // Get random response from dummy data
      const randomIndex = Math.floor(Math.random() * this.dummyResponses.length);
      const aiResponse = this.dummyResponses[randomIndex];

      // Add AI response
      this.addMessage(aiResponse, 'ai');

      // TODO: Replace with actual API call
      // this.chatService.sendMessage(userMessage).subscribe(response => {
      //   this.isLoading = false;
      //   this.isAiTyping = false;
      //   this.addMessage(response.content, 'ai');
      // });

    }, delay);
  }

  private generateMessageId(): string {
    return Date.now().toString() + Math.random().toString(36).substring(2, 11);
  }

  private scrollToBottom(): void {
    if (this.chatContent) {
      const element = this.chatContent.nativeElement;
      element.scrollTop = element.scrollHeight;
    }
  }

  // Method to handle input changes (for two-way binding if needed)
  onMessageInput(event: any): void {
    this.currentMessage = event.target.value;
  }

  // Method to handle close split screen
  onCloseSplitScreen(): void {
    this.closeSplitScreen.emit();
  }
}
