import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
// **UPDATE THIS PATH**
import { HeadingComponent, BodyTextComponent, CaptionComponent } from '@awe/play-comp-library';
import { IconsComponent } from '../../../../shared/components/icons/icons.component';
import { AweCardComponent } from '../awe-card/awe-card.component';
import { AweProgressBarComponent } from "../awe-progress-bar/awe-progress-bar.component";
import { AweButtonComponent } from '../awe-button/awe-button.component';

// --- Interfaces for our Dummy Data ---
interface UserPersona {
  name: string;
  role: string;
  avatarUrl: string;
}
interface Feature {
  name: string;
  color: string;
}
interface SwotItem {
  category: 'strengths' | 'weaknesses' | 'opportunities' | 'threats';
  points: string[];
  color: string;
}
interface TimelineItem {
  icon: string;
  label: string;
}
interface TimelineQuarter {
  quarter: string;
  items: TimelineItem[];
}
interface ProjectData {
  name: string;
  description: string;
  progress: number;
  contributionText: string;
  understandingText: string;
  features: Feature[];
  personas: UserPersona[];
  swot: SwotItem[];
  timeline: TimelineQuarter[];
}
@Component({
  selector: 'app-summary',
  imports: [
    CommonModule,
    AweCardComponent,
    IconsComponent,
    HeadingComponent,
    BodyTextComponent,
    AweProgressBarComponent,
    CaptionComponent,
    AweButtonComponent
],
  templateUrl: './summary.component.html',
  styleUrl: './summary.component.scss',
})
export class SummaryComponent implements OnInit {
  // Use '!' for definite assignment since it's initialized in ngOnInit
  projectData!: ProjectData;

  constructor() {}

  ngOnInit(): void {
    this.projectData = {
      name: 'Project Name',
      description:
        'The project aims to develop a debit/credit card with an embedded fingerprint scanner for secure, contactless transactions. The card addresses issues such as fraudulent transactions, the inconvenience of entering PINs, and security concerns with traditional cards. It offers increased security, convenience, and a user-friendly experience.',
      progress: 84,
      contributionText:
        'Michelangelo contributed 84%, and you added the final 20%.',
      understandingText:
       
      'By analyzing all nine components of the Business Model Canvas, we understand blah blah blah (the summary of the generated understanding will come here) dummy data. By analyzing all nine components of the Business Model Canvas, we understand blah blah blah.',
      features: [{ name: 'Smart Nap Timer', color: '#FD7542' },
        { name: 'Gentle wake', color: '#FDC100' },
        { name: 'Gamification', color: '#0F9D57' },
        { name: 'Dashboard', color: '#FD7542' },
        { name: 'Mood-to-nap', color: '#0F9D57' },
        { name: 'Journal integration', color: '#FDC100' },
        { name: 'Smart device', color: '#25364D' },
      ],
      personas: [
        {
          name: 'Debbie Perkins',
          role: 'Sales Manager',
          avatarUrl: 'assets/avatar1.png',
        },
        {
          name: 'Donald Terry',
          role: 'Architect',
          avatarUrl: 'assets/avatar2.png',
        },
        {
          name: 'Bob Prowell',
          role: 'Designer',
          avatarUrl: 'assets/avatar3.png',
        },
        {
          name: 'Bob Prowell',
          role: 'Business Analyst',
          avatarUrl: 'assets/avatar4.png',
        },
      ],
      swot: [
        {
          category: 'strengths',
          color: '#0F9D57',
          points: ['Nap Boss mode', 'AI that actually cares'],
        },
        { category: 'opportunities', color: '#FDC100', points: ['For Nap nerds'] },
        {
          category: 'weaknesses',
          color: '#FD7542',
          points: ['Sleep is new cool', "HR's secret weapon"],
        },
        { category: 'threats', color: '#0F9D57', points: ["'I'll just close my eyes' syndrome"] },
      ],
      timeline: [
        {
          quarter: 'Quater 1',
          items: [
            { icon: 'awe_wireframe', label: 'Wire framing' },
            { icon: 'awe_timer', label: 'Sleep timer' },
          ],
        },
        {
          quarter: 'Quater 2',
          items: [
            { icon: 'awe_gamify', label: 'Gamification' },
            { icon: 'awe_design', label: 'Design System' },
          ],
        },
        {
          quarter: 'Quater 3',
          items: [
            { icon: 'awe_mood', label: 'Mood-to-nap' },
            { icon: 'awe_dashboard', label: 'Dashboard' },
          ],
        },
        {
          quarter: 'Quater 4',
          items: [
            { icon: 'awe_code', label: 'Frontend' },
            { icon: 'awe_database', label: 'Database' },
          ],
        },
        {
          quarter: 'Quater 5',
          items: [{ icon: 'awe_test', label: 'Testing' }],
        },
      ],
    };
  }

  exportProject() {
    // Logic to export the project data
    console.log('Exporting project data:', this.projectData);
  }
}
