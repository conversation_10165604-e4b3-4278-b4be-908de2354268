:host {
  display: block;
  background-color: #f7f7f9;
  font-family:
    system-ui,
    -apple-system,
    sans-serif;
}

// --- GENERAL STYLING ---
.project-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  position: relative;
  display: inline-block;

  &::after {
    content: "";
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, #8a2be2, #6a5acd);
  }
}

.project-description {
  color: #6c757d;
  font-size: 1rem;
  line-height: 1.6;
}

.btn-icon {
  background: none;
  border: none;
  color: #adb5bd;
  margin: 0;
  transition: color 0.2s ease;
  &:hover {
    color: #6c757d;
  }
}

.card-wrapper {
  border-radius: 24px;
  border: 1px solid rgba(124, 185, 254, 0.9);
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.6) 0%,
    rgba(255, 255, 255, 0.8) 100%
  );
  box-shadow: 0px 0px 16px 0px rgba(225, 225, 225, 0.25);
  backdrop-filter: blur(25px);
}

// --- PROGRESS SECTION ---
.progress-heading {
  font-size: 1.1rem;
  font-weight: 600;
}
.progress-subheading {
  font-size: 0.9rem;
  color: #6c757d;
}
.export-btn {
  border-radius: 8px;
  border: 1px solid var(--Primary-50, #f2ebfd);
  background: var(
    --Main-gradient,
    linear-gradient(
      109deg,
      var(--Primary-500, #7c3aed) 4.7%,
      var(--Secondary-500, #2563eb) 94.91%
    )
  );
  color: #fff;
  font-weight: 500;
  &:hover {
    background-color: darken(#6f42c1, 5%);
  }
}

// Circular Progress Bar
@property --progress {
  syntax: "<integer>";
  initial-value: 0;
  inherits: false;
}

.progress-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: grid;
  place-items: center;
  background: conic-gradient(#8a2be2 calc(var(--progress) * 1%), #e9ecef 0);
  transition: --progress 1s ease-out;
  animation: progress-animation 1s ease-out forwards;

  .progress-text {
    font-size: 1.5rem;
    font-weight: bold;
    color: #495057;
  }
}

@keyframes progress-animation {
  from {
    --progress: 0;
  }
}

// --- CARD-SPECIFIC STYLING ---
h4 {
  font-size: 1.25rem;
  font-weight: 600;
}

// Feature List
.feature-list {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  .feature-tag {
    padding: 0.3rem 1rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1.5px solid;

    &.yellow {
      border-color: #ffc107;
      background-color: #fff9e6;
    }
    &.light-green {
      border-color: #28a745;
      background-color: #eaf6ec;
    }
    &.light-orange {
      border-color: #fd7e14;
      background-color: #fff2e8;
    }
    &.gray {
      border-color: #6c757d;
      background-color: #f8f9fa;
    }
    // Add other colors as needed
  }
}

.task-bar {
  height: 45px;
  border-radius: 8px;
  background: #eeeff0;
}
.task-bar-swot {
  border-radius: 8px;
  padding: 11px;
  background: #eeeff0;

  ul {
    list-style-type: disc; // Removes the default black bullet points
    padding-left: 0;
    margin-bottom: 0; // Removes default browser margin
  }

  // Target the li inside the container
  li {
    padding: 4px 0; // Adds some vertical spacing between list items
    color: #343a40; // A dark color for the text
  }
}
// User Persona
.persona-list {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
}
.persona-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  border-radius: 12px;

  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #cbd4cd;
  }

  .persona-role {
    font-size: 0.85rem;
    color: #6c757d;
  }
}

// SWOT Analysis
.swot-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;

  ul {
    list-style: none;
    padding-left: 0;
    margin: 0;
    li {
      position: relative;
      padding-left: 1.2rem;
      margin-bottom: 0.5rem;
      &::before {
        content: "•";
        position: absolute;
        left: 0;
        font-size: 1.5rem;
        line-height: 1;
      }
    }
  }

  .strengths li::before {
    color: #28a745;
  }
  .weaknesses li::before {
    color: #6f42c1;
  }
  .opportunities li::before {
    color: #fd7e14;
  }
  .threats li::before {
    color: #dc3545;
  }
}



.my-custom-bar {
  --progress-color: #743eed;
  --trail-color: #d5c3ff;
  --text-color: #575555;
  --size: 190px;
  --thickness: -16px;
}
button {
  margin-top: 1rem;
}
 :host {
  display: block;
  width: 100%;
}

h4 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}
.btn-export{
  border-radius: 12px;
  border: none;
  width: auto;
  background-color: #743eed;
  color: #fff;
  font-weight: 500; 

  &:hover {
    background-color: #6f42c1;
  }
}

.footer-arrow-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 1px solid #d0d5dd;
  background: none;
  font-size: 1.2rem;
  color: #6c757d;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}


// --- TIMELINE CONTAINER & MAIN LINE ---
.timeline-container {
  position: relative;
  width: 100%;
}

.timeline-main-line {
  position: absolute;
  left: 50%;
  top: 10px;
  bottom: 10px;
  width: 4px;
  transform: translateX(-50%);
  background: linear-gradient(180deg, #f0e6ff 0%, #e9f3ff 100%);
  border-radius: 2px;
  z-index: 1;
}


// --- TIMELINE ITEM BASE STYLES ---
.timeline-item {
  position: relative;
  width: 50%;

  // The colored dot on the timeline
  &::after {
    content: '';
    position: absolute;
    top: 6px; // Adjust vertical alignment
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: #eab9c9; // Pink/rose color of the dot
    z-index: 2; // On top of the main line
  }

  // The horizontal connecting line
  &::before {
    content: '';
    position: absolute;
    top: 12px; // Align with the center of the dot
    height: 2px;
    width: 2.5rem; // Length of the horizontal line
    background: #eab9c9;
    z-index: 1;
  }

  // Remove padding from the last item
  &:last-child {
    padding-bottom: 0;
  }
}


// --- ALTERNATING LAYOUT LOGIC ---

// ODD items (1, 3, 5) are positioned on the LEFT side
.timeline-item:nth-child(odd) {
  left: 0;
  padding-right: 2.5rem;
  
  .timeline-content {
    text-align: right;
    align-items: flex-end; // Align internal items to the right
  }
  
  &::after { right: -7px; } // Position dot on its right edge
  &::before { right: 0; }   // Position line on its right edge
}

// EVEN items (2, 4) are positioned on the RIGHT side
.timeline-item:nth-child(even) {
  left: 50%;
  padding-left: 2.5rem;
  
  .timeline-content {
    text-align: left;
    align-items: flex-start; // Align internal items to the left
  }
  
  &::after { left: -7px; }  // Position dot on its left edge
  &::before { left: 0; }    // Position line on its left edge
}


// --- CONTENT STYLING ---

.timeline-content {
  display: flex;
  flex-direction: column;
}

.quarter-title {
  font-size: 1rem;
  font-weight: 600;
  color: #8A2BE2;
  margin-bottom: 0.75rem;
}

.quarter-tasks {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.task-item {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #495057;
  font-size: 0.95rem;

  awe-icons {
    color: #8A2BE2;
  }
}


// --- RESPONSIVE STYLES for mobile screens ---
@media (max-width: 768px) {
  // Move the main line to the far left
  .timeline-main-line {
    left: 7px; // Half the width of the dot
    transform: none;
  }

  // Make all items full-width and aligned to the left
  .timeline-item,
  .timeline-item:nth-child(even) {
    width: 100%;
    left: 0;
    padding-left: 2.5rem; // Add padding to all items
    padding-right: 0;
  }
  
  // Align all content to the left
  .timeline-item .timeline-content,
  .timeline-item:nth-child(odd) .timeline-content {
    text-align: left;
    align-items: flex-start;
  }
  
  // Position all dots and lines to the left
  .timeline-item::after,
  .timeline-item:nth-child(odd)::after,
  .timeline-item:nth-child(even)::after {
    left: 0;
  }
  
  .timeline-item::before,
  .timeline-item:nth-child(odd)::before,
  .timeline-item:nth-child(even)::before {
    left: 14px; // Start the line after the dot
  }
}
