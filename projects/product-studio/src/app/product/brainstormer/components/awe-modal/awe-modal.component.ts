import {
  Component,
  Input,
  Output,
  EventEmitter,
  HostListener,
  ElementRef,
  Renderer2,
  OnChanges,
  SimpleChanges,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  OnInit,
  OnDestroy,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconsComponent } from '../../../../shared/components/icons/icons.component';

export type ModalPosition = 'center' | 'left' | 'right' | 'top' | 'bottom';
export type ModalAnimation =
  | 'fade'
  | 'slide-in-left'
  | 'slide-in-right'
  | 'slide-in-top'
  | 'slide-in-bottom'
  | 'zoom'
  | 'none';

@Component({
  selector: 'awe-modal',
  standalone: true,
  imports: [CommonModule, IconsComponent],
  templateUrl: './awe-modal.component.html',
  styleUrls: ['./awe-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush, // Optimize for performance
})
export class AweModalComponent implements OnChanges, OnInit, OnDestroy {
  @Input() isOpen: boolean = false;
  @Input() showHeader: boolean = true;
  @Input() showFooter: boolean = false;
  @Input() title?: string; // Optional title if not projecting full header
  @Input() width: string = '500px'; // Default width
  @Input() height: string = 'auto'; // Default height, content will dictate
  @Input() maxWidth: string = '90vw';
  @Input() maxHeight: string = '90vh';
  @Input() position: ModalPosition = 'center';
  @Input() animation: ModalAnimation = 'fade';
  @Input() closeOnBackdropClick: boolean = true;
  @Input() closeOnEscape: boolean = true;
  @Input() backdropClass: string = ''; // Custom class for backdrop
  @Input() modalClass: string = ''; // Custom class for modal content panel
  @Input() showCloseButton: boolean = true; // Whether to show the default X button

  @Output() opened = new EventEmitter<void>();
  @Output() closed = new EventEmitter<void>();
  @Output() beforeOpen = new EventEmitter<void>(); // Emits before opening animation starts
  @Output() beforeClose = new EventEmitter<void>(); // Emits before closing animation starts

  private _isAnimatingOut: boolean = false;
  _internalVisible: boolean = false; // Used to delay *ngIf removal for animations

  // Public getter for template access
  get isAnimatingOut(): boolean {
    return this._isAnimatingOut;
  }

  // Check if header content is projected
  hasProjectedHeaderContent(): boolean {
    const headerSlot = this.el.nativeElement.querySelector('[awe-modal-header]');
    return headerSlot && headerSlot.children.length > 0;
  }

  constructor(
    private el: ElementRef,
    private renderer: Renderer2,
    private cdRef: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    // For initial state if isOpen is true on load
    if (this.isOpen) {
      this.handleOpen();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isOpen']) {
      if (this.isOpen) {
        this.handleOpen();
      } else {
        this.handleClose();
      }
    }
  }

  private handleOpen(): void {
    if (this._internalVisible) return; // Already open or opening

    this.beforeOpen.emit();
    this._internalVisible = true;
    this._isAnimatingOut = false;
    this.renderer.setStyle(document.body, 'overflow', 'hidden'); // Prevent body scroll
    this.cdRef.detectChanges(); // Ensure template is updated before animation

    // Emit opened after a short delay to allow CSS transition/animation to start
    // For CSS animations, this might not be perfectly synced.
    // Angular animations provide better callbacks.
    setTimeout(() => {
      if (this.isOpen) { // Check if still open (could have been closed quickly)
        this.opened.emit();
      }
    }, this.getAnimationDuration());
  }

  private handleClose(): void {
    if (!this._internalVisible || this._isAnimatingOut) return; // Already closed or closing

    this.beforeClose.emit();
    this._isAnimatingOut = true;
    this.cdRef.detectChanges();

    setTimeout(() => {
      this._internalVisible = false;
      this._isAnimatingOut = false;
      this.renderer.removeStyle(document.body, 'overflow');
      this.closed.emit();
      this.cdRef.detectChanges();
    }, this.getAnimationDuration()); // Match animation duration
  }

  private getAnimationDuration(): number {
    // Crude way to get duration. Better to define it as a constant or from CSS vars.
    // This assumes a CSS animation/transition is applied.
    if (this.animation === 'none') return 0;
    return 300; // ms, example duration
  }

  closeModal(): void {
    if (this.isOpen) {
      // Parent controls isOpen, so we emit an event for parent to update it
      // This pattern is more common for controlled components
      // this.isOpen = false; // Don't directly mutate input
      this.closed.emit(); // Signal intent to close
      // The parent component should listen to (closed) and set its `isModalOpen` variable to false.
      // Then ngOnChanges will trigger `handleClose`.
      // OR, for simpler immediate visual feedback if parent doesn't react fast enough with isOpen:
      // this.handleClose(); // This might lead to double execution if parent also sets isOpen
    }
  }

  onBackdropClicked(event: MouseEvent): void {
    if (this.closeOnBackdropClick && event.target === this.el.nativeElement.querySelector('.awe-modal-backdrop')) {
      this.closeModal();
    }
  }

  @HostListener('document:keydown.escape', ['$event'])
  onEscapeKey(_event: KeyboardEvent): void {
    if (this.isOpen && this.closeOnEscape) {
      this.closeModal();
    }
  }

  ngOnDestroy(): void {
    this.renderer.removeStyle(document.body, 'overflow'); // Cleanup
  }
}