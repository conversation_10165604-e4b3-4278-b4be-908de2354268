import { CommonModule } from '@angular/common';
import { Component, Input, OnDestroy, OnInit, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import {
  ButtonComponent,
  PromptBarComponent,
} from '@awe/play-comp-library'
import { IconsComponent } from '../../../../shared/components/icons/icons.component';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import {
  buttonLabels,
  fileOptions,
} from '../constants/promt-screen.constant';
import { FileAttachPillComponent, FileAttachOption } from '../../../../shared/components/file-attach-pill/file-attach-pill.component';
import { HeroSectionHeaderComponent } from "../../../shared/components/hero-section-header/hero-section-header.component";
import { IconStatus, Buttons, SelectedFile } from '../modals/promt-screen.modal';

@Component({
  selector: 'app-promt-screen',
  standalone: true,
  imports: [CommonModule, FormsModule, HeroSectionHeaderComponent, PromptBarComponent,
  ButtonComponent, IconsComponent, FileAttachPillComponent],  
  templateUrl: './promt-screen.component.html',
  styleUrls: ['./promt-screen.component.scss']
})
export class PromtScreenComponent implements OnInit, OnDestroy {
  fileOptions: FileAttachOption[] = fileOptions;
  selectedFile: File | null = null;
  selectedFiles: SelectedFile[] = [];
  selectedFileName = '';
  previewFile: SelectedFile | null = null;
  leftIcons: { name: string; status: IconStatus; color: string }[] = [];
  rightIcons: { name: string; status: IconStatus; color: string }[] = [];
  currentPrompt = '';
  @Input() buttons: Buttons[] = buttonLabels;
  submissionData = {
    prompt: this.currentPrompt,
    timestamp: new Date().toISOString(),
    imageFile: this.selectedFile,
    imageUrl: this.selectedFile ? URL.createObjectURL(this.selectedFile) : null,
    imageDataUri: null as string | null,
    fileName: this.selectedFileName || null,
  };

  getIconColor(): string {
    const isDisabled =
      !this.currentPrompt?.trim() ||
      this.isEnhancing ||
      this.enhanceClickCount >= this.maxEnhanceClicks;
    return `var(--icon-${isDisabled ? 'disabled' : 'enabled'}-color)`;
  }
  // Properties
  theme: 'light' | 'dark' = 'light';
  animatedTexts = [
    'Brainstorm a new idea',
    'Help me improve user engagement for my app.',
    'Create user personas for Fly high airlines'
  ];

  // File handling

  readonly maxAllowedFiles = 1;
  isFileAttachDisabled = false;
  fileError = '';
  showPreview = false;

  // State flags
  isEnhancing = false;
  isGenerating = false;
  isEnhancedPromptValid = true;
  isPromptEnhanced = false;
  enhanceClickCount = 0;
  maxEnhanceClicks = 2;

  private subscriptions: Subscription[] = [];
  private boundHandlePasteEvent: (event: ClipboardEvent) => void;

  constructor(
    private router: Router,
    // public readonly promptService: PromptBarService,
    // private readonly themeService: ThemeService,
    
    private cdr: ChangeDetectorRef) {
    this.boundHandlePasteEvent = this.handlePasteEvent.bind(this);
  }
  private handlePasteEvent(event: ClipboardEvent): void {
    // Optionally handle pasted images or text here
    // Example: Prevent default paste if image is present
    if (event.clipboardData) {
      const items = event.clipboardData.items;
      for (let i = 0; i < items.length; i++) {
        if (items[i].type.indexOf('image') !== -1) {
          // Handle pasted image if needed
          event.preventDefault();
          // You can implement image handling logic here
        }
      }
    }
  }

  onFileOptionSelected(option: FileAttachOption): void {
    if (this.isProcessing() || this.isMaxFilesReached()) {
      this.fileError = `Only ${this.maxAllowedFiles} image can be uploaded at a time`;
      return;
    }
    if (option.value === 'computer') {
      this.handleEnhancedAlternate();
      return;
    }
    if (option.value === 'url') {
      if (this.isEnhancing || this.isGenerating) return;
      const url = prompt('Enter the URL of the file:');
      if (!url) return;
      const mockFile: SelectedFile = {
        id: Math.random().toString(36).substring(2, 11),
        name: url.split('/').pop() || 'file.jpg',
        url,
        type: 'image/jpeg',
      };
      this.selectedFiles = [...this.selectedFiles, mockFile];
      this.updateFileAttachPillStatus();
    }
  }
  closePreview(): void {
    this.previewFile = null;
    this.showPreview = false;
  }
  showFilePreview(file: SelectedFile): void {
    this.previewFile = file;
    this.showPreview = true;
  }
  private handleEnhancedAlternate(): void {
    if (this.isEnhancing || this.isGenerating) {
      return;
    }
    if (this.isMaxFilesReached()) {
      this.fileError = `Only ${this.maxAllowedFiles} image can be uploaded at a time`;
      return;
    }
    const fileInput = this.createFileInput();
    fileInput.addEventListener('change', (event: Event) => this.handleFileSelect(event));
    fileInput.click();
  }
  private isMaxFilesReached(): boolean {
    return this.selectedFiles.length >= this.maxAllowedFiles;
  }
  private createFileInput(): HTMLInputElement {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'image/*';
    fileInput.style.display = 'none';
    document.body.appendChild(fileInput);
    return fileInput;
  }
  ngOnInit(): void {
    this.resetComponentState();
    this.initTheme();

    // Detect system theme preference
    const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)');
    this.theme = prefersDarkScheme.matches ? 'dark' : 'light';

    const mediaQueryListener = (event: MediaQueryListEvent) => {
      this.theme = event.matches ? 'dark' : 'light';
      this.cdr.detectChanges();
    };

    prefersDarkScheme.addEventListener('change', mediaQueryListener);
    this.subscriptions.push(new Subscription(() => {
      prefersDarkScheme.removeEventListener('change', mediaQueryListener);
    }));
  }
  handleIconClick(event: { name: string; side: string; index: number }): void {
    const normalizedIconName = event.name.toLowerCase();
    switch (normalizedIconName) {
      case 'awe_enhanced_alternate':
        this.handleEnhancedAlternate();
        break;
      case 'awe_enhance':
        this.handleEnhanceText();
        break;
      case 'awe_enhanced_send':
        this.handleEnhancedSend();
        break;
      default:
        console.warn('Unknown icon clicked:', event.name);
    }
  }
  onFileRemoved(fileId: string): void {
    this.removeFile(fileId);
    // this.promptService.resetEnhancedPromptState();
    // this.promptService.setImage(null);
    this.isPromptEnhanced = false;
    this.isEnhancedPromptValid = true;
    this.enhanceClickCount = 0;
    this.currentPrompt = '';
    // this.updateSendButtonState();
  }
  removeFile(fileId: string): void {
    this.selectedFiles = this.selectedFiles.filter(file => file.id !== fileId);
    this.updateFileAttachPillStatus();
  }
  ngAfterViewInit(): void {
    document.addEventListener('paste', this.boundHandlePasteEvent);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    document.removeEventListener('paste', this.boundHandlePasteEvent);
  }


  handleEnterPressed(): void {
    if (this.isProcessing() || !this.currentPrompt?.trim()) {
      return;
    }
    this.handleEnhancedSend();
  }


  private updateIconDisabledState(): void {
    requestAnimationFrame(() => {
      const isEmptyPrompt = !this.currentPrompt?.trim();
      const isProcessing = this.isProcessing();
      // this.disablePromptBarElements(isProcessing);
      const enhanceIcons = document.querySelectorAll<HTMLElement>('.enhance-icons awe-icons');
      enhanceIcons.forEach((icon, index) => {
        const isEnhanceButton = index === 0;
        const isSendButton = index === 1;
        let isDisabled = isEmptyPrompt || isProcessing;
        if (isEnhanceButton) {
          isDisabled =
            isEmptyPrompt || isProcessing || this.enhanceClickCount >= this.maxEnhanceClicks;
          if (
            this.isPromptEnhanced &&
            !this.isEnhancedPromptValid &&
            this.enhanceClickCount < this.maxEnhanceClicks
          ) {
            isDisabled = isEmptyPrompt || isProcessing;
          }
        } else if (isSendButton) {
          if (this.isPromptEnhanced) {
            isDisabled = isEmptyPrompt || isProcessing || !this.isEnhancedPromptValid;
          } else {
            isDisabled = isEmptyPrompt || isProcessing;
          }
        }
        icon.classList.toggle('disabled', isDisabled);
        icon.style.cssText = `
          color: var(${isDisabled ? '--icon-disabled-color' : '--icon-enabled-color'});
          cursor: ${isDisabled ? 'not-allowed' : 'pointer'};
          opacity: ${isDisabled ? 'var(--icon-disabled-opacity, 0.9)' : '1'};
        `;
      });
    });
  }
  handleEnhancedSend(): void {
    if (this.isEnhancing || !this.currentPrompt?.trim()) return;
    if (this.isPromptEnhanced && !this.isEnhancedPromptValid) {
      return;
    }

    const prompt = this.currentPrompt.trim();
    const timestamp = new Date().toISOString();
    const file = this.selectedFile;
    const imageUrl = file ? URL.createObjectURL(file) : null;

    // Here you would normally send the data to the backend
    this.isGenerating = true;

    // Navigate to understanding page
    this.router.navigate(['brainstormer/brainstormer-form'], { relativeTo: this.router.routerState.root.firstChild?.firstChild });

    this.isGenerating = false;
  }

  handleEnhanceText(): void {
    if (this.enhanceClickCount >= this.maxEnhanceClicks || !this.currentPrompt?.trim()) return;

    this.isEnhancing = true;

    // Simulate enhancing the prompt (normally an API call)
    setTimeout(() => {
      const enhancedPrompt = `${this.currentPrompt} with responsive layout, modern UI components, and optimized performance`;
      this.currentPrompt = enhancedPrompt;
      this.isPromptEnhanced = true;
      this.isEnhancedPromptValid = true;
      this.enhanceClickCount++;
      this.isEnhancing = false;
      this.cdr.detectChanges();
      this.adjustTextareaHeight();
    }, 1500);
  }

  handleSuggestionClick(suggestion: string): void {
    this.currentPrompt = suggestion.replace(/^✨\s*/, '').trim();
    this.adjustTextareaHeight();
  }

  handleFileAttach(): void {
    if (this.isProcessing() || this.isMaxFilesReached()) {
      this.fileError = `Only ${this.maxAllowedFiles} image can be uploaded at a time`;
      return;
    }

    const fileInput = document.getElementById('fileInput') as HTMLInputElement;
    fileInput.click();
  }

  handleFileSelect(event: Event): void {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];

    if (!file || !this.validateFile(file)) return;

    this.isPromptEnhanced = false;
    this.isEnhancedPromptValid = true;
    this.enhanceClickCount = 0;
    this.selectedFile = file;
    this.selectedFileName = file.name;

    // const fileUrl = URL.createObjectURL(file);
    // this.selectedFiles = [
    //   {
    //     id: Math.random().toString(36).substring(2, 11),
    //     name: file.name,
    //     url: fileUrl,
    //     type: file.type,
    //   },
    // ];

    this.updateFileAttachPillStatus();
    this.currentPrompt = '';
    input.value = '';
  }

  truncateFileName(filename: string): string {
    const maxLength = 13;
    const dotIndex = filename.lastIndexOf('.');

    if (filename.length <= maxLength || dotIndex === -1) return filename;

    const extension = filename.slice(dotIndex);
    const nameWithoutExt = filename.slice(0, dotIndex);
    const truncatedName = nameWithoutExt.slice(0, maxLength - extension.length - 3);

    return `${truncatedName}...${extension}`;
  }

  // Tech/Design selection



  // Utility methods
  isProcessing(): boolean {
    return this.isEnhancing || this.isGenerating;
  }

  // isMaxFilesReached(): boolean {
  //   return this.selectedFiles.length >= this.maxAllowedFiles;
  // }

  private validateFile(file: File): boolean {
    const acceptedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];

    if (!file.type) {
      this.fileError = 'Folders cannot be uploaded';
    } else if (!acceptedImageTypes.includes(file.type)) {
      this.fileError = 'Only image files (JPEG, PNG, GIF, WEBP, SVG) are allowed';
    } else if (file.size > 5 * 1024 * 1024) {
      this.fileError = 'File size must be less than 5MB';
    } else {
      this.fileError = '';
      return true;
    }

    return false;
  }

  private resetComponentState(): void {
    this.currentPrompt = '';
    // this.selectedFiles = [];
    this.selectedFile = null;
    this.selectedFileName = '';
    // this.previewFile = null;
    this.isFileAttachDisabled = false;
    this.fileError = '';
    this.isEnhancing = false;
    this.isGenerating = false;
    this.showPreview = false;
    this.enhanceClickCount = 0;
    this.isEnhancedPromptValid = true;
    this.isPromptEnhanced = false;
  }

  private updateFileAttachPillStatus(): void {
    const isMaxReached = this.isMaxFilesReached();
    const uploadIcon = this.leftIcons.find(icon => icon.name === 'awe_enhanced_alternate');
    if (uploadIcon) {
      uploadIcon.status = isMaxReached ? 'disable' : 'default';
    }
    this.isFileAttachDisabled = isMaxReached;
  }
  handlePromptChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    this.currentPrompt = input.value;
    // this.promptService.setPrompt(this.currentPrompt);
    this.isEnhancedPromptValid = true;
    this.isPromptEnhanced = false;
    this.adjustTextareaHeight();
    this.updateIconDisabledState();
    // this.updateSendButtonState();
  }


  private adjustTextareaHeight(): void {
    setTimeout(() => {
      const textareas = document.querySelectorAll<HTMLTextAreaElement>('.prompt-text');
      if (!textareas.length) return;

      textareas.forEach(textarea => {
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 'px';
      });
    }, 0);
  }

  private initTheme(): void {
    // This would normally subscribe to a theme service
    this.theme = 'light';
  }
}
