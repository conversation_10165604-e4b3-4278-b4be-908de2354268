import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';

@Component({
  selector: 'awe-button',
  imports: [CommonModule ],
  templateUrl: './awe-button.component.html',
  styleUrl: './awe-button.component.scss'
})
export class AweButtonComponent {

  constructor() { }

  @Input() label: string = "";
  // @Input() iconName: string = "";
  // @Input() iconSize: string = "16px";
  @Input() btnBgColor: string = ""
  @Input() btnTextColor: string = "";
  @Input() class: string = "";
}
