import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface CanvasItem {
  id: string;
  title: string;
  icon: string;
  iconBg: string;
  data: string[];
}

@Injectable({
  providedIn: 'root'
})
export class UnderstandingDataService {
  private businessModelCanvasSubject = new BehaviorSubject<CanvasItem[]>(this.getInitialData());
  public businessModelCanvas$ = this.businessModelCanvasSubject.asObservable();

  constructor() {}

  private getInitialData(): CanvasItem[] {
    return [
      {
        id: 'problem',
        title: 'Problem',
        icon: '/icons/problem.svg',
        iconBg: '#ffebee',
        data: [
          'People are tired but bad at napping — breaks turn into oversleeping, alarms are harsh, and naps feel unproductive.',
        ],
      },
      {
        id: 'key-partners',
        title: 'Key partners',
        icon: '/icons/key-partners.svg',
        iconBg: '#e3f2fd',
        data: [
          'Mattress and pillow companies',
          'Mental health & wellness platforms',
          'Universities and startups',
          'Alarm clock rehabilitation centers',
        ],
      },
      {
        id: 'value-proposition',
        title: 'Value Proposition',
        icon: '/icons/value-proposition.svg',
        iconBg: '#fff3e0',
        data: [
          'Helps users find the perfect time, duration, and location to nap',
          'Smart nap alerts that won\'t jolt you awake like a banshee',
          'Shareable "Nap Achievements" to flex your sleep game',
        ],
      },
      {
        id: 'customer-segments',
        title: 'Customer Segments',
        icon: '/icons/customer-segments.svg',
        iconBg: '#f3e5f5',
        data: [
          'Overworked techies',
          'College students',
          'Remote workers',
          'Lazy geniuses',
        ],
      },
      {
        id: 'key-metrics',
        title: 'Key Metrics',
        icon: '/icons/key-metrics.svg',
        iconBg: '#e8eaf6',
        data: ['Daily active users', 'Average Nap quality', 'Conversion rate'],
      },
      {
        id: 'solution',
        title: 'Solution',
        icon: '/icons/solution.svg',
        iconBg: '#e8f5e9',
        data: [
          'Napify helps you nap smarter by suggesting ideal times and durations, waking you up gently, and tracking nap stats.',
        ],
      },
      {
        id: 'alternatives',
        title: 'Alternatives',
        icon: '/icons/alternatives.svg',
        iconBg: '#ede7f6',
        data: ['Generic Alarms', 'Sleep Tracking Wearables', 'Manual Napping'],
      },
      {
        id: 'cost-structure',
        title: 'Cost Structure',
        icon: '/icons/cost-structure.svg',
        iconBg: '#fce4ec',
        data: [
          'App development and maintenance',
          'Cloud storage (for all those dreamy naps)',
          'Marketing (especially targeting tired people)',
        ],
      },
      {
        id: 'revenue-streams',
        title: 'Revenue Streams',
        icon: '/icons/revenue-streams.svg',
        iconBg: '#e0f2f1',
        data: [
          'Freemium model with paid "Nap Elite" tier (includes rain sounds, nap mask discounts)',
          'In-app purchases (pillow reviews, snore recordings)',
          'Sponsored naps from mattress brands',
        ],
      },
    ];
  }

  // Get all canvas items
  getBusinessModelCanvas(): CanvasItem[] {
    return this.businessModelCanvasSubject.value;
  }

  // Get specific canvas item by ID
  getCanvasItemById(id: string): CanvasItem | undefined {
    return this.businessModelCanvasSubject.value.find(item => item.id === id);
  }

  // Update canvas item data
  updateCanvasItem(id: string, newData: string[]): void {
    const currentCanvas = this.businessModelCanvasSubject.value;
    const itemIndex = currentCanvas.findIndex(item => item.id === id);

    if (itemIndex !== -1) {
      const updatedCanvas = [...currentCanvas];
      updatedCanvas[itemIndex] = {
        ...updatedCanvas[itemIndex],
        data: [...newData]
      };
      this.businessModelCanvasSubject.next(updatedCanvas);
    }
  }

  // Add data item to specific canvas item
  addDataItem(canvasId: string, dataItem: string): void {
    const currentCanvas = this.businessModelCanvasSubject.value;
    const itemIndex = currentCanvas.findIndex(item => item.id === canvasId);

    if (itemIndex !== -1) {
      const updatedCanvas = [...currentCanvas];
      updatedCanvas[itemIndex] = {
        ...updatedCanvas[itemIndex],
        data: [...updatedCanvas[itemIndex].data, dataItem]
      };
      this.businessModelCanvasSubject.next(updatedCanvas);
    }
  }

  // Remove data item from specific canvas item
  removeDataItem(canvasId: string, dataIndex: number): void {
    const currentCanvas = this.businessModelCanvasSubject.value;
    const itemIndex = currentCanvas.findIndex(item => item.id === canvasId);

    if (itemIndex !== -1) {
      const updatedCanvas = [...currentCanvas];
      const newData = [...updatedCanvas[itemIndex].data];
      newData.splice(dataIndex, 1);

      updatedCanvas[itemIndex] = {
        ...updatedCanvas[itemIndex],
        data: newData
      };
      this.businessModelCanvasSubject.next(updatedCanvas);
    }
  }

  // Get grouped data for specific layouts
  getFirstRowProblemCard(): CanvasItem[] {
    const canvas = this.getBusinessModelCanvas();
    return [canvas[0]]; // Problem
  }

  getFirstRowSolutionCard(): CanvasItem[] {
    const canvas = this.getBusinessModelCanvas();
    return [canvas[5]]; // Solution
  }

  getKeyPartnersData(): CanvasItem[] {
    const canvas = this.getBusinessModelCanvas();
    return [canvas[1], canvas[2]]; // Key Partners, Value Proposition
  }

  getKeyMetricsAlternativesData(): CanvasItem[] {
    const canvas = this.getBusinessModelCanvas();
    return [canvas[4], canvas[6]]; // Key Metrics, Alternatives
  }

  getCustomerSegmentItems(): CanvasItem[] {
    const canvas = this.getBusinessModelCanvas();
    return [canvas[3]]; // Customer Segments
  }

  getCostRevenueItems(): CanvasItem[] {
    const canvas = this.getBusinessModelCanvas();
    return [canvas[7], canvas[8]]; // Cost Structure, Revenue Streams
  }

  // Reset to initial data
  resetToDefaults(): void {
    this.businessModelCanvasSubject.next(this.getInitialData());
  }
}