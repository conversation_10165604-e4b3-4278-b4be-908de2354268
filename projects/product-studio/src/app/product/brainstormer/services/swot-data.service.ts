import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface FeatureCard {
  id: string;
  title: string;
  description: string;
  tags: string[];
  impact: number; // 0-100 for progress bar
  priority: number; // 0-100 for progress bar
}

export interface FeatureSection {
  id: string;
  title: string;
  subtitle: string;
  features: FeatureCard[];
}

@Injectable({
  providedIn: 'root'
})
export class SwotDataService {
  private sectionsSubject = new BehaviorSubject<FeatureSection[]>(this.getInitialData());
  public sections$ = this.sectionsSubject.asObservable();

  constructor() {}

  private getInitialData(): FeatureSection[] {
    return [
      {
        id: 'strengths',
        title: 'S',
        subtitle: 'STRENGTHS',
        features: [
          {
            id: 'strength-1',
            title: 'Unique Biometric Authentication',
            description: 'Advanced fingerprint technology that sets us apart from traditional payment methods.',
            tags: ['Impact', 'Security', 'Innovation'],
            impact: 85,
            priority: 90,
          },
          {
            id: 'strength-2',
            title: 'Advanced Security Features',
            description: 'Multi-layer security with biometric verification and encryption protocols.',
            tags: ['Security', 'Technology', 'Trust'],
            impact: 92,
            priority: 88,
          },
          {
            id: 'strength-3',
            title: 'User-Friendly Interface',
            description: 'Intuitive design that makes secure payments accessible to all users.',
            tags: ['UX', 'Accessibility', 'Design'],
            impact: 78,
            priority: 75,
          },
        ],
      },
      {
        id: 'weaknesses',
        title: 'W',
        subtitle: 'WEAKNESSES',
        features: [
          {
            id: 'weakness-1',
            title: 'High Development Costs',
            description: 'Significant investment required for biometric technology development and implementation.',
            tags: ['Cost', 'Development', 'Investment'],
            impact: 70,
            priority: 85,
          },
          {
            id: 'weakness-2',
            title: 'Limited Market Awareness',
            description: 'Low consumer awareness about biometric payment solutions and their benefits.',
            tags: ['Marketing', 'Awareness', 'Education'],
            impact: 65,
            priority: 80,
          },
        ],
      },
      {
        id: 'opportunities',
        title: 'O',
        subtitle: 'OPPORTUNITIES',
        features: [
          {
            id: 'opportunity-1',
            title: 'Growing Security Concerns',
            description: 'Increasing demand for secure payment methods due to rising fraud incidents.',
            tags: ['Market', 'Security', 'Demand'],
            impact: 88,
            priority: 92,
          },
          {
            id: 'opportunity-2',
            title: 'Digital Payment Growth',
            description: 'Rapid expansion of contactless payment adoption globally post-pandemic.',
            tags: ['Growth', 'Digital', 'Global'],
            impact: 82,
            priority: 78,
          },
          {
            id: 'opportunity-3',
            title: 'Partnership Potential',
            description: 'Opportunities to partner with banks, fintech companies, and payment processors.',
            tags: ['Partnership', 'Collaboration', 'Expansion'],
            impact: 75,
            priority: 70,
          },
        ],
      },
      {
        id: 'threats',
        title: 'T',
        subtitle: 'THREATS',
        features: [
          {
            id: 'threat-1',
            title: 'Competitive Technology',
            description: 'Alternative authentication methods like facial recognition and voice authentication.',
            tags: ['Competition', 'Technology', 'Innovation'],
            impact: 65,
            priority: 75,
          },
          {
            id: 'threat-2',
            title: 'Privacy Regulations',
            description: 'Strict biometric data protection laws may limit adoption and implementation.',
            tags: ['Regulation', 'Privacy', 'Compliance'],
            impact: 78,
            priority: 82,
          },
          {
            id: 'threat-3',
            title: 'Economic Downturn',
            description: 'Economic instability could reduce investment in new payment technologies.',
            tags: ['Economy', 'Investment', 'Risk'],
            impact: 60,
            priority: 65,
          },
        ],
      },
    ];
  }

  // Get all sections
  getSections(): FeatureSection[] {
    return this.sectionsSubject.value;
  }

  // Get section by ID
  getSectionById(id: string): FeatureSection | undefined {
    return this.sectionsSubject.value.find(section => section.id === id);
  }

  // Get section IDs
  getSectionIds(): string[] {
    return this.sectionsSubject.value.map(section => section.id);
  }

  // Add new feature to a section
  addFeature(sectionId: string, feature: Omit<FeatureCard, 'id'>): void {
    const currentSections = this.sectionsSubject.value;
    const sectionIndex = currentSections.findIndex(section => section.id === sectionId);

    if (sectionIndex !== -1) {
      const updatedSections = [...currentSections];
      const newFeature: FeatureCard = {
        ...feature,
        id: `${sectionId}-${Date.now()}`
      };

      updatedSections[sectionIndex] = {
        ...updatedSections[sectionIndex],
        features: [...updatedSections[sectionIndex].features, newFeature]
      };

      this.sectionsSubject.next(updatedSections);
    }
  }

  // Update existing feature
  updateFeature(featureId: string, updatedFeature: Partial<FeatureCard>): void {
    const currentSections = this.sectionsSubject.value;
    const updatedSections = [...currentSections];

    for (let sectionIndex = 0; sectionIndex < updatedSections.length; sectionIndex++) {
      const featureIndex = updatedSections[sectionIndex].features.findIndex(f => f.id === featureId);

      if (featureIndex !== -1) {
        updatedSections[sectionIndex] = {
          ...updatedSections[sectionIndex],
          features: [
            ...updatedSections[sectionIndex].features.slice(0, featureIndex),
            { ...updatedSections[sectionIndex].features[featureIndex], ...updatedFeature },
            ...updatedSections[sectionIndex].features.slice(featureIndex + 1)
          ]
        };
        break;
      }
    }

    this.sectionsSubject.next(updatedSections);
  }

  // Delete feature
  deleteFeature(featureId: string): void {
    const currentSections = this.sectionsSubject.value;
    const updatedSections = [...currentSections];

    for (let sectionIndex = 0; sectionIndex < updatedSections.length; sectionIndex++) {
      const featureIndex = updatedSections[sectionIndex].features.findIndex(f => f.id === featureId);

      if (featureIndex !== -1) {
        updatedSections[sectionIndex] = {
          ...updatedSections[sectionIndex],
          features: updatedSections[sectionIndex].features.filter(f => f.id !== featureId)
        };
        break;
      }
    }

    this.sectionsSubject.next(updatedSections);
  }

  // Get feature by ID across all sections
  getFeatureById(featureId: string): FeatureCard | undefined {
    const sections = this.sectionsSubject.value;
    for (const section of sections) {
      const feature = section.features.find(f => f.id === featureId);
      if (feature) return feature;
    }
    return undefined;
  }

  // Reset to initial data
  resetToDefaults(): void {
    this.sectionsSubject.next(this.getInitialData());
  }
}