import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface FeatureCard {
  id: string;
  title: string;
  description: string;
  tags: string[];
}

export interface FeatureSection {
  id: string;
  title: string; // 'Mo', 'S', 'Co', 'W'
  subtitle: string; // 'MUST HAVE', 'SHOULD HAVE', etc.
  features: FeatureCard[];
}

@Injectable({
  providedIn: 'root'
})
export class FeatureDataService {
  private sectionsSubject = new BehaviorSubject<FeatureSection[]>(this.getInitialData());
  public sections$ = this.sectionsSubject.asObservable();

  constructor() {}

  private getInitialData(): FeatureSection[] {
    return [
      {
        id: 'must-have',
        title: 'Mo',
        subtitle: 'MUST HAVE',
        features: [
          {
            id: 'must-1',
            title: 'Contactless Payment Capability',
            description:
              'Allows user to make contactless payments using the biometric card with advanced security features and seamless integration.',
            tags: ['Convenience', 'User experience', 'Technology'],
          },
          {
            id: 'must-2',
            title: 'Secure Payment Processing',
            description:
              'Ensures all payment transactions are processed securely with end-to-end encryption and fraud detection.',
            tags: ['Security', 'Trust', 'Compliance'],
          },
          {
            id: 'must-3',
            title: 'Biometric Authentication',
            description:
              'Fingerprint-based authentication system for secure access to payment functions.',
            tags: ['Security', 'Innovation', 'Authentication'],
          },
        ],
      },
      {
        id: 'should-have',
        title: 'S',
        subtitle: 'SHOULD HAVE',
        features: [
          {
            id: 'should-1',
            title: 'Transaction History Tracking',
            description:
              'Comprehensive tracking and reporting of all payment transactions with detailed analytics.',
            tags: ['Analytics', 'User experience', 'Reporting'],
          },
          {
            id: 'should-2',
            title: 'Multi-Currency Support',
            description:
              'Support for multiple currencies with real-time exchange rates and conversion.',
            tags: ['Global', 'Currency', 'Flexibility'],
          },
          {
            id: 'should-3',
            title: 'Mobile App Integration',
            description:
              'Seamless integration with mobile applications for enhanced user control and monitoring.',
            tags: ['Mobile', 'Integration', 'Control'],
          },
        ],
      },
      {
        id: 'could-have',
        title: 'Co',
        subtitle: 'COULD HAVE',
        features: [
          {
            id: 'could-1',
            title: 'Loyalty Program Integration',
            description:
              'Integration with various loyalty programs and reward systems for enhanced user benefits.',
            tags: ['Rewards', 'Loyalty', 'Benefits'],
          },
          {
            id: 'could-2',
            title: 'Spending Analytics Dashboard',
            description:
              'Advanced analytics dashboard showing spending patterns, budgeting tools, and financial insights.',
            tags: ['Analytics', 'Budgeting', 'Insights'],
          },
        ],
      },
      {
        id: 'wont-have',
        title: 'W',
        subtitle: "WON'T HAVE",
        features: [
          {
            id: 'wont-1',
            title: 'Cryptocurrency Support',
            description:
              'Support for cryptocurrency transactions and digital wallet integration.',
            tags: ['Cryptocurrency', 'Digital', 'Future'],
          },
          {
            id: 'wont-2',
            title: 'Voice Command Interface',
            description:
              'Voice-activated payment commands and audio feedback system.',
            tags: ['Voice', 'Audio', 'Accessibility'],
          },
        ],
      },
    ];
  }

  // Get all sections
  getSections(): FeatureSection[] {
    return this.sectionsSubject.value;
  }

  // Get section by ID
  getSectionById(id: string): FeatureSection | undefined {
    return this.sectionsSubject.value.find(section => section.id === id);
  }

  // Get section IDs for drag-drop
  getSectionIds(): string[] {
    return this.sectionsSubject.value.map(section => section.id);
  }

  // Add new feature to a section
  addFeature(sectionId: string, feature: Omit<FeatureCard, 'id'>): void {
    const currentSections = this.sectionsSubject.value;
    const sectionIndex = currentSections.findIndex(section => section.id === sectionId);

    if (sectionIndex !== -1) {
      const updatedSections = [...currentSections];
      const newFeature: FeatureCard = {
        ...feature,
        id: `${sectionId}-feature-${Date.now()}`
      };

      updatedSections[sectionIndex] = {
        ...updatedSections[sectionIndex],
        features: [...updatedSections[sectionIndex].features, newFeature]
      };

      this.sectionsSubject.next(updatedSections);
    }
  }

  // Update existing feature
  updateFeature(featureId: string, updatedFeature: Partial<FeatureCard>): void {
    const currentSections = this.sectionsSubject.value;
    const updatedSections = [...currentSections];

    for (let sectionIndex = 0; sectionIndex < updatedSections.length; sectionIndex++) {
      const featureIndex = updatedSections[sectionIndex].features.findIndex(f => f.id === featureId);

      if (featureIndex !== -1) {
        updatedSections[sectionIndex] = {
          ...updatedSections[sectionIndex],
          features: [
            ...updatedSections[sectionIndex].features.slice(0, featureIndex),
            { ...updatedSections[sectionIndex].features[featureIndex], ...updatedFeature },
            ...updatedSections[sectionIndex].features.slice(featureIndex + 1)
          ]
        };
        break;
      }
    }

    this.sectionsSubject.next(updatedSections);
  }

  // Delete feature
  deleteFeature(featureId: string): void {
    const currentSections = this.sectionsSubject.value;
    const updatedSections = [...currentSections];

    for (let sectionIndex = 0; sectionIndex < updatedSections.length; sectionIndex++) {
      const featureIndex = updatedSections[sectionIndex].features.findIndex(f => f.id === featureId);

      if (featureIndex !== -1) {
        updatedSections[sectionIndex] = {
          ...updatedSections[sectionIndex],
          features: updatedSections[sectionIndex].features.filter(f => f.id !== featureId)
        };
        break;
      }
    }

    this.sectionsSubject.next(updatedSections);
  }

  // Move feature between sections (for drag-drop)
  moveFeature(featureId: string, fromSectionId: string, toSectionId: string, newIndex: number): void {
    const currentSections = this.sectionsSubject.value;
    const updatedSections = [...currentSections];

    // Find and remove feature from source section
    const fromSectionIndex = updatedSections.findIndex(s => s.id === fromSectionId);
    const toSectionIndex = updatedSections.findIndex(s => s.id === toSectionId);

    if (fromSectionIndex !== -1 && toSectionIndex !== -1) {
      const featureIndex = updatedSections[fromSectionIndex].features.findIndex(f => f.id === featureId);

      if (featureIndex !== -1) {
        const [movedFeature] = updatedSections[fromSectionIndex].features.splice(featureIndex, 1);
        updatedSections[toSectionIndex].features.splice(newIndex, 0, movedFeature);

        this.sectionsSubject.next(updatedSections);
      }
    }
  }

  // Reorder features within same section
  reorderFeatures(sectionId: string, fromIndex: number, toIndex: number): void {
    const currentSections = this.sectionsSubject.value;
    const sectionIndex = currentSections.findIndex(s => s.id === sectionId);

    if (sectionIndex !== -1) {
      const updatedSections = [...currentSections];
      const features = [...updatedSections[sectionIndex].features];
      const [movedFeature] = features.splice(fromIndex, 1);
      features.splice(toIndex, 0, movedFeature);

      updatedSections[sectionIndex] = {
        ...updatedSections[sectionIndex],
        features
      };

      this.sectionsSubject.next(updatedSections);
    }
  }

  // Reset to initial data
  resetToDefaults(): void {
    this.sectionsSubject.next(this.getInitialData());
  }
}