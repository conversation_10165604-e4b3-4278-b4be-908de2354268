<div
  class="file-attach-pill-container"
  (mouseenter)="onMouseEnter()"
  (mouseleave)="onMouseLeave()"
>
  <button
    class="file-attach-pill"
    [class.expanded]="isHovered()"
    (click)="toggleDropdown($event)"
    [attr.aria-label]="mainText"
    [attr.aria-expanded]="isDropdownOpen()"
    [attr.aria-haspopup]="true"
  >
    <span class="icon-wrapper">
      <awe-icons [iconName]="mainIcon" [iconColor]="iconColor"></awe-icons>
    </span>
    <span class="text" *ngIf="isHovered()">{{ mainText }}</span>
    <span class="arrow" *ngIf="isHovered()">
      <awe-icons [iconName]="'awe_arrow_drop_up_filled'" [iconColor]="iconColor"></awe-icons>
    </span>
  </button>

  <div
    class="dropdown"
    [class.show]="isDropdownOpen()"
    role="menu"
    (mouseenter)="onDropdownMouseEnter()"
    (mouseleave)="onDropdownMouseLeave()"
  >
    <div
      *ngFor="let option of options"
      class="dropdown-item"
      role="menuitem"
      tabindex="0"
      (click)="selectOption(option, $event)"
      (keydown.enter)="selectOption(option, $event)"
      (keydown.space)="selectOption(option, $event)"
    >
      <span class="dropdown-item-text">{{ option.name }}</span>
      <span class="dropdown-item-icon">
        <awe-icons [iconName]="option.icon" [iconColor]="iconColor"></awe-icons>
      </span>
    </div>
  </div>
</div>
