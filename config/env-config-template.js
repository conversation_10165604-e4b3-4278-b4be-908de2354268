// Environment configuration template for Angular applications
// This file will be processed at container startup to inject environment variables
(function (window) {
  window.__env = window.__env || {};

  // Environment variables - these will be replaced at runtime
  window.__env.API_BASE_URL = '${API_BASE_URL}';
  window.__env.BASE_URL = '${BASE_URL}';
  window.__env.NODE_ENV = '${NODE_ENV}';
  
  // Runtime information
  window.__env.LOADED_AT = new Date().toISOString();
  
  console.log('🌍 Environment configuration loaded:', window.__env);
})(this); 