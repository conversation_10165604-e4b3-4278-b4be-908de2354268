#!/bin/bash

# =============================================================================
# Elder Wand Pipeline-Style Deployment Script
# =============================================================================
# 
# This script deploys all Elder Wand projects using individual Dockerfiles
# with environment-specific configurations (pipeline-style approach).
# 
# Usage: ./deploy.sh [local|dev|qa|uat|prod]
# =============================================================================

set -e

# =====================================================================
# Configuration
# =====================================================================
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# =====================================================================
# Functions
# =====================================================================
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

error() {
    echo "[ERROR] $1" >&2
    exit 1
}

usage() {
    echo "Usage: $0 [local|dev|qa|uat|prod]"
    echo ""
    echo "Environment options:"
    echo "  local - Local development environment"
    echo "  dev   - Development environment"
    echo "  qa    - QA environment"
    echo "  uat   - UAT environment"
    echo "  prod  - Production environment"
    exit 1
}

validate_environment() {
    local env="$1"
    case "$env" in
        local|dev|qa|uat|prod)
            return 0
            ;;
        *)
            return 1
            ;;
    esac
}

check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check if pipeline script exists
    if [ ! -f "$PROJECT_ROOT/scripts/start-docker.sh" ]; then
        error "Pipeline deployment script not found: scripts/start-docker.sh"
    fi
    
    # Check if environment file exists
    if [ ! -f "$PROJECT_ROOT/environments/env.$ENVIRONMENT" ]; then
        error "Environment file 'environments/env.$ENVIRONMENT' not found."
    fi
    
    log "Prerequisites check passed."
}

deploy_pipeline_style() {
    log "Deploying services using pipeline-style approach for environment: $ENVIRONMENT"
    
    # Change to project root directory
    cd "$PROJECT_ROOT"
    
    # Copy environment file to local for pipeline script
    cp "$PROJECT_ROOT/environments/env.$ENVIRONMENT" "$PROJECT_ROOT/environments/env.local"
    
    # Use pipeline deployment script
    ./scripts/start-docker.sh rebuild
    
    log "Pipeline-style deployment completed successfully."
}

check_health() {
    log "Checking service health..."
    
    # Change to project root directory
    cd "$PROJECT_ROOT"
    
    # Wait for services to be ready
    sleep 10
    
    # Use pipeline-style health checking
    ./scripts/start-docker.sh status
    
    log "Health checks completed."
}

show_status() {
    log "Deployment status:"
    ./scripts/start-docker.sh status
    
    echo ""
    log "Service URLs:"
    echo "  Console:        http://localhost/console/ (temporarily disabled)"
    echo "  Experience:     http://localhost/experience/"
    echo "  Launchpad:      http://localhost/launchpad/"
    echo "  Product:        http://localhost/product/"
    echo ""
    log "Health check: http://localhost/health"
}

cleanup() {
    log "Cleaning up..."
    ./scripts/start-docker.sh stop
    docker system prune -f
    log "Cleanup completed."
}

# =====================================================================
# Main Script
# =====================================================================
main() {
    # Check if environment is provided
    if [ $# -eq 0 ]; then
        usage
    fi
    
    ENVIRONMENT="$1"
    
    # Validate environment
    if ! validate_environment "$ENVIRONMENT"; then
        error "Invalid environment: $ENVIRONMENT"
    fi
    
    log "Starting deployment for environment: $ENVIRONMENT"
    
    # Change to project root
    cd "$PROJECT_ROOT"
    
    # Run deployment steps
    check_prerequisites
    deploy_pipeline_style
    check_health
    show_status
    
    log "Deployment completed successfully for environment: $ENVIRONMENT"
}

# =====================================================================
# Signal Handling
# =====================================================================
trap 'error "Deployment interrupted by user"' INT TERM

# =====================================================================
# Execute Main Function
# =====================================================================
main "$@" 