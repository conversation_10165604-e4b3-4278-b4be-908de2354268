#!/bin/bash

# =============================================================================
# Elder Wand Docker Management Script (Pipeline-style)
# =============================================================================
# 
# This script builds and runs Docker containers directly using individual
# Dockerfiles from each project, similar to how the Azure pipelines work.
# 
# Usage: ./scripts/start-docker.sh [COMMAND]
# =============================================================================

set -e

# =============================================================================
# Configuration
# =============================================================================
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
ENV_FILE="$PROJECT_ROOT/environments/env.local"

# Project configurations (compatible with older bash)
PROJECTS="console elder-wand experience-studio product-studio"

# Function to get project path
get_project_path() {
    case "$1" in
        "console") echo "projects/console" ;;
        "elder-wand") echo "projects/elder-wand" ;;
        "experience-studio") echo "projects/experience-studio" ;;
        "product-studio") echo "projects/product-studio" ;;
    esac
}

# Function to get container port
get_container_port() {
    case "$1" in
        "console") echo "8081:8080" ;;
        "elder-wand") echo "8082:8080" ;;
        "experience-studio") echo "8083:8080" ;;
        "product-studio") echo "8084:8080" ;;
    esac
}

# Image names (following pipeline naming convention)
IMAGE_PREFIX="elderwand-direct"

# =============================================================================
# Utility Functions
# =============================================================================
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

success() {
    echo "[SUCCESS] $1"
}

error() {
    echo "[ERROR] $1" >&2
    exit 1
}

# =============================================================================
# Docker Functions
# =============================================================================
build_image() {
    local project=$1
    local project_path=$(get_project_path "$project")
    local image_name="$IMAGE_PREFIX-$project"
    
    log "Building $project Docker image..."
    
    cd "$PROJECT_ROOT"
    
    # Build using individual Dockerfile (like pipeline)
    docker build \
        -f "$project_path/Dockerfile" \
        -t "$image_name:latest" \
        --build-arg NODE_ENV=production \
        .
    
    success "$project image built successfully"
}

run_container() {
    local project=$1
    local image_name="$IMAGE_PREFIX-$project"
    local container_name="$project-app-direct"
    local port=$(get_container_port "$project")
    
    log "Starting $project container..."
    
    # Stop existing container if running
    if docker ps -q --filter "name=$container_name" | grep -q .; then
        docker stop "$container_name" >/dev/null 2>&1 || true
        docker rm "$container_name" >/dev/null 2>&1 || true
    fi
    
    # Load environment variables
    ENV_ARGS=""
    if [ -f "$ENV_FILE" ]; then
        while IFS='=' read -r key value; do
            # Skip comments and empty lines
            [[ $key =~ ^#.*$ ]] && continue
            [[ -z "$key" ]] && continue
            # Remove quotes and export
            value=$(echo "$value" | sed 's/^["'\'']//' | sed 's/["'\'']$//')
            ENV_ARGS="$ENV_ARGS -e $key=$value"
        done < "$ENV_FILE"
    fi
    
    # Run container
    docker run -d \
        --name "$container_name" \
        -p "$port" \
        $ENV_ARGS \
        --restart unless-stopped \
        "$image_name:latest"
    
    success "$project container started on port $port"
}

setup_nginx_proxy() {
    log "Setting up nginx reverse proxy..."
    
    # Stop existing proxy if running
    if docker ps -q --filter "name=elder-wand-proxy-direct" | grep -q .; then
        docker stop "elder-wand-proxy-direct" >/dev/null 2>&1 || true
        docker rm "elder-wand-proxy-direct" >/dev/null 2>&1 || true
    fi
    
    # Create temporary nginx config
    cat > /tmp/nginx-direct.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    upstream console_backend {
        server host.docker.internal:8081;
    }
    
    upstream elder_wand_backend {
        server host.docker.internal:8082;
    }
    
    upstream experience_studio_backend {
        server host.docker.internal:8083;
    }
    
    upstream product_studio_backend {
        server host.docker.internal:8084;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        # Health check
        location /health {
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # Console app
        location /console/ {
            proxy_pass http://console_backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location = /console {
            return 301 /console/;
        }
        
        # Launchpad (Elder Wand)
        location /launchpad/ {
            proxy_pass http://elder_wand_backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location = /launchpad {
            return 301 /launchpad/;
        }
        
        # Experience Studio
        location /experience/ {
            proxy_pass http://experience_studio_backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location = /experience {
            return 301 /experience/;
        }
        
        # Product Studio
        location /product/ {
            proxy_pass http://product_studio_backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location = /product {
            return 301 /product/;
        }
    }
}
EOF
    
    # Run nginx proxy
    docker run -d \
        --name "elder-wand-proxy-direct" \
        -p "80:80" \
        -v "/tmp/nginx-direct.conf:/etc/nginx/nginx.conf:ro" \
        --restart unless-stopped \
        nginx:alpine
    
    success "Nginx proxy started"
}

# =============================================================================
# Main Commands
# =============================================================================
start_all() {
    log "Starting Elder Wand application stack (direct Docker)..."
    
    # Check prerequisites
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed or not in PATH"
    fi
    
    if ! docker info &> /dev/null; then
        error "Docker daemon is not running"
    fi
    
    # Build all images
    for project in $PROJECTS; do
        build_image "$project"
    done
    
    # Start all containers
    for project in $PROJECTS; do
        run_container "$project"
    done
    
    # Setup nginx proxy
    setup_nginx_proxy
    
    # Wait for containers to be ready
    sleep 5
    
    success "All services started successfully! 🚀"
    echo ""
    echo "🌐 Application URLs:"
    echo "  • Health Check:    http://localhost/health"
    echo "  • Console:         http://localhost/console/"
    echo "  • Experience:      http://localhost/experience/"
    echo "  • Launchpad:       http://localhost/launchpad/"
    echo "  • Product Studio:  http://localhost/product/"
    echo ""
    echo "📊 Container Ports (direct access):"
    echo "  • Console:         http://localhost:8081"
    echo "  • Elder Wand:      http://localhost:8082"
    echo "  • Experience:      http://localhost:8083"
    echo "  • Product Studio:  http://localhost:8084"
}

stop_all() {
    log "Stopping all Elder Wand containers..."
    
    # Stop nginx proxy
    docker stop "elder-wand-proxy-direct" >/dev/null 2>&1 || true
    docker rm "elder-wand-proxy-direct" >/dev/null 2>&1 || true
    
    # Stop all project containers
    for project in $PROJECTS; do
        container_name="$project-app-direct"
        docker stop "$container_name" >/dev/null 2>&1 || true
        docker rm "$container_name" >/dev/null 2>&1 || true
        log "Stopped $project container"
    done
    
    # Clean up temporary files
    rm -f /tmp/nginx-direct.conf
    
    success "All containers stopped successfully ✅"
}

rebuild_all() {
    log "Performing full rebuild..."
    stop_all
    
    # Remove images
    for project in $PROJECTS; do
        image_name="$IMAGE_PREFIX-$project"
        docker rmi "$image_name:latest" >/dev/null 2>&1 || true
    done
    
    start_all
}

show_status() {
    log "Checking status of all containers..."
    echo ""
    
    echo "📦 Project Containers:"
    for project in $PROJECTS; do
        container_name="$project-app-direct"
        if docker ps --filter "name=$container_name" --format "table {{.Names}}\t{{.Status}}" | grep -q "$container_name"; then
            status=$(docker ps --filter "name=$container_name" --format "{{.Status}}")
            echo "  ✅ $project: $status"
        else
            echo "  ❌ $project: Not running"
        fi
    done
    
    echo ""
    echo "🌐 Nginx Proxy:"
    if docker ps --filter "name=elder-wand-proxy-direct" --format "table {{.Names}}\t{{.Status}}" | grep -q "elder-wand-proxy-direct"; then
        status=$(docker ps --filter "name=elder-wand-proxy-direct" --format "{{.Status}}")
        echo "  ✅ Proxy: $status"
    else
        echo "  ❌ Proxy: Not running"
    fi
    
    # Test endpoints if curl is available
    if command -v curl &> /dev/null; then
        echo ""
        echo "🔍 Health checks:"
        
        if curl -s http://localhost/health &> /dev/null; then
            echo "  ✅ Health endpoint: OK"
        else
            echo "  ❌ Health endpoint: Failed"
        fi
        
        if curl -s http://localhost/console/ &> /dev/null; then
            echo "  ✅ Console: OK"
        else
            echo "  ❌ Console: Failed"
        fi
        
        if curl -s http://localhost/launchpad/ &> /dev/null; then
            echo "  ✅ Launchpad: OK"
        else
            echo "  ❌ Launchpad: Failed"
        fi
    fi
}

show_logs() {
    if [ -n "$1" ]; then
        # Show logs for specific project
        container_name="$1-app-direct"
        docker logs -f "$container_name"
    else
        echo "Available containers for logs:"
        for project in $PROJECTS; do
            echo "  • $project"
        done
        echo ""
        echo "Usage: $0 logs [project_name]"
        echo "   or: $0 logs console"
        echo "   or: $0 logs elder-wand"
    fi
}

show_help() {
    cat << EOF
Elder Wand Docker Management Script (Pipeline-style)

This script builds and runs Docker containers directly using individual
Dockerfiles from each project, similar to how the Azure pipelines work.

Usage: $0 [COMMAND] [OPTIONS]

Available commands:
  start     - Build and start all services (default)
  stop      - Stop all services
  restart   - Restart all services
  rebuild   - Stop, rebuild images, and start all services
  status    - Show status of all services
  logs      - Show logs from services
  help      - Show this help message

Examples:
  $0                    # Start all services
  $0 start              # Start all services  
  $0 rebuild            # Full rebuild and restart
  $0 logs               # List available containers
  $0 logs console       # Show console logs
  $0 status             # Check service status

Features:
  • Uses individual Dockerfiles (like Azure pipelines)
  • Direct container management (no docker-compose)
  • Environment variable injection
  • Nginx reverse proxy setup
  • Health checking and status monitoring

EOF
}

# =============================================================================
# Main Script Logic
# =============================================================================
case "${1:-start}" in
    "start")
        start_all
        ;;
    "stop")
        stop_all
        ;;
    "restart")
        stop_all
        start_all
        ;;
    "rebuild")
        rebuild_all
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs "$2"
        ;;
    "help")
        show_help
        ;;
    *)
        echo "Unknown command: $1"
        echo "Use '$0 help' for usage information."
        exit 1
        ;;
esac 